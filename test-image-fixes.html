<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>إصلاح مشكلة عرض صورة المتهم</title>
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      direction: rtl;
      text-align: right;
      margin: 20px;
      background: #f8f9fa;
      line-height: 1.6;
    }

    .container {
      max-width: 1000px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .title {
      color: #3b82f6;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
      text-align: center;
    }

    .problem-box {
      background: #fef2f2;
      border: 1px solid #ef4444;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .problem-title {
      font-weight: bold;
      color: #ef4444;
      margin-bottom: 5px;
    }

    .solution-box {
      background: #f0fdf4;
      border: 1px solid #10b981;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .solution-title {
      font-weight: bold;
      color: #10b981;
      margin-bottom: 5px;
    }

    .section {
      margin-bottom: 30px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }

    .section-title {
      font-weight: bold;
      color: #374151;
      margin-bottom: 15px;
      font-size: 18px;
    }

    .code-box {
      background: #1f2937;
      color: #f9fafb;
      padding: 15px;
      border-radius: 6px;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      overflow-x: auto;
      margin: 10px 0;
    }

    .before-after {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin: 20px 0;
    }

    .before, .after {
      padding: 15px;
      border-radius: 8px;
    }

    .before {
      background: #fef2f2;
      border: 1px solid #ef4444;
    }

    .after {
      background: #f0fdf4;
      border: 1px solid #10b981;
    }

    .feature-list {
      list-style: none;
      padding: 0;
    }

    .feature-list li {
      padding: 8px 0;
      border-bottom: 1px solid #e5e7eb;
    }

    .feature-list li:before {
      content: "✅ ";
      color: #10b981;
      font-weight: bold;
    }

    .step-list {
      list-style: none;
      padding: 0;
      counter-reset: step-counter;
    }

    .step-list li {
      padding: 10px 0;
      border-bottom: 1px solid #e5e7eb;
      counter-increment: step-counter;
    }

    .step-list li:before {
      content: counter(step-counter) ". ";
      color: #3b82f6;
      font-weight: bold;
      margin-left: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">🔧 إصلاح مشكلة عرض صورة المتهم</h1>
    
    <div class="problem-box">
      <div class="problem-title">❌ المشاكل المكتشفة:</div>
      <ul>
        <li><strong>في البطاقات:</strong> الصورة لا تظهر مطلقاً</li>
        <li><strong>في ملف HTML:</strong> أيقونة صورة مكسورة</li>
        <li><strong>السبب:</strong> استخدام Blob URL مؤقت بدلاً من Data URL دائم</li>
      </ul>
    </div>

    <div class="solution-box">
      <div class="solution-title">✅ الحلول المطبقة:</div>
      <ul class="feature-list">
        <li>تحويل الصور إلى Data URL للحفظ الدائم</li>
        <li>تحسين دالة البحث عن الصور في البطاقات</li>
        <li>إصلاح عرض الصور في ملف HTML</li>
        <li>إضافة طرق متعددة للعثور على الصورة</li>
      </ul>
    </div>

    <div class="section">
      <div class="section-title">1️⃣ إصلاح حفظ الصور</div>
      
      <div class="before-after">
        <div class="before">
          <h4>❌ الطريقة القديمة:</h4>
          <div class="code-box">
// حفظ كـ Blob URL (مؤقت)
filePath: URL.createObjectURL(file)

// المشكلة: ينتهي عند إعادة تحميل الصفحة
          </div>
        </div>

        <div class="after">
          <h4>✅ الطريقة الجديدة:</h4>
          <div class="code-box">
// حفظ كـ Data URL (دائم)
const reader = new FileReader()
reader.onload = (e) => {
  const dataUrl = e.target?.result as string
  formData[fieldId + '_dataUrl'] = dataUrl
}
reader.readAsDataURL(file)
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title">2️⃣ تحسين البحث عن الصور</div>
      
      <div class="before-after">
        <div class="before">
          <h4>❌ البحث القديم:</h4>
          <div class="code-box">
// بحث في مكان واحد فقط
const attachment = suspect.attachments.find(...)
return attachment?.filePath || null
          </div>
        </div>

        <div class="after">
          <h4>✅ البحث المحسن:</h4>
          <div class="code-box">
// بحث في عدة أماكن:
// 1. attachments array (data URL)
// 2. field data (data URL)  
// 3. field data (blob URL)
// 4. fallback methods
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title">3️⃣ إصلاح عرض HTML</div>
      
      <div class="before-after">
        <div class="before">
          <h4>❌ العرض القديم:</h4>
          <div class="code-box">
// استخدام getFieldValue مباشرة
const value = getFieldValue(suspect, field)
&lt;img src="${value}" /&gt;

// المشكلة: value = "مرفق" وليس مسار الصورة
          </div>
        </div>

        <div class="after">
          <h4>✅ العرض المحسن:</h4>
          <div class="code-box">
// استخدام دالة مخصصة للصور
const imageData = getImageDataForField(suspect, field)
if (imageData) {
  &lt;img src="${imageData}" /&gt;
} else {
  &lt;p&gt;لا توجد صورة&lt;/p&gt;
}
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <div class="section-title">🧪 خطوات الاختبار</div>
      
      <div class="solution-box">
        <div class="solution-title">للاختبار الآن:</div>
        <ol class="step-list">
          <li><strong>أضف متهم جديد</strong> مع صورة في حقل "صورة المتهم"</li>
          <li><strong>احفظ البيانات</strong> وتأكد من ظهور الصورة في المعاينة</li>
          <li><strong>اذهب إلى "عرض بطاقات المتهمين"</strong></li>
          <li><strong>تحقق من ظهور الصورة</strong> في البطاقة بحجم 40مم × 60مم</li>
          <li><strong>اضغط على زر التصدير</strong> للمتهم</li>
          <li><strong>افتح ملف HTML</strong> من الملف المضغوط</li>
          <li><strong>تحقق من ظهور الصورة</strong> في HTML بنفس الحجم</li>
        </ol>
      </div>
    </div>

    <div class="section">
      <div class="section-title">🔧 التحسينات التقنية</div>
      
      <h4>أ) تحسين دالة processFile:</h4>
      <div class="code-box">
// إضافة حفظ Data URL للصور
if (field.inputType === 'image' && file.type.startsWith('image/')) {
  const reader = new FileReader()
  reader.onload = (e) => {
    const dataUrl = e.target?.result as string
    if (dataUrl) {
      formData[fieldId + '_dataUrl'] = dataUrl
    }
  }
  reader.readAsDataURL(file)
}
      </div>

      <h4>ب) تحسين دالة getSuspectImage:</h4>
      <div class="code-box">
// بحث متدرج في عدة أماكن:
// 1. attachments array (data URL)
// 2. field data (data URL suffix)
// 3. field data (direct)
// 4. fallback to blob URL
      </div>

      <h4>ج) تحسين دالة getImageDataForField:</h4>
      <div class="code-box">
// دالة مخصصة للبحث عن بيانات الصورة
// تدعم جميع طرق الحفظ المختلفة
// ترجع Data URL صالح أو null
      </div>
    </div>

    <div class="solution-box">
      <div class="solution-title">🎯 النتائج المتوقعة:</div>
      <ul class="feature-list">
        <li>صورة المتهم تظهر في البطاقات بحجم 40مم × 60مم</li>
        <li>صورة المتهم تظهر في ملف HTML بنفس الحجم</li>
        <li>الصور تبقى ظاهرة حتى بعد إعادة تحميل الصفحة</li>
        <li>جودة عالية للصور في جميع الأماكن</li>
        <li>لا رسائل خطأ أو صور مكسورة</li>
      </ul>
    </div>

    <div class="solution-box">
      <div class="solution-title">🔒 ضمانات الأمان:</div>
      <ul class="feature-list">
        <li>لا تأثير على حفظ البيانات الأخرى</li>
        <li>متوافق مع الصور الموجودة</li>
        <li>يدعم جميع صيغ الصور</li>
        <li>لا كسر في الوظائف الحالية</li>
      </ul>
    </div>
  </div>

  <script>
    console.log('🔧 تم تطبيق إصلاحات عرض صورة المتهم!');
    console.log('📸 الآن الصور تُحفظ كـ Data URL للاستمرارية');
    console.log('🔍 تم تحسين البحث عن الصور في عدة أماكن');
    console.log('🧪 جرب الآن إضافة متهم جديد مع صورة');
  </script>
</body>
</html>
