<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-lg w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6 pb-4 border-b border-secondary-200">
        <div class="flex items-center gap-3">
          <div class="neumorphic-icon">
            <i class="fas fa-user-plus text-primary-600"></i>
          </div>
          <h2 class="text-xl font-bold text-secondary-800">
            {{ isEditing ? 'تعديل المستخدم' : 'إضافة مستخدم جديد' }}
          </h2>
        </div>
        <button @click="$emit('cancel')" class="text-secondary-400 hover:text-secondary-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- User Name -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-user ml-1"></i>
            اسم المستخدم *
          </label>
          <input
            v-model="formData.name"
            type="text"
            class="neumorphic-input w-full"
            placeholder="الاسم الكامل"
            required
          />
        </div>

        <!-- Email -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-envelope ml-1"></i>
            البريد الإلكتروني *
          </label>
          <input
            v-model="formData.email"
            type="email"
            class="neumorphic-input w-full"
            placeholder="<EMAIL>"
            required
          />
        </div>

        <!-- Password (only for new users) -->
        <div v-if="!isEditing">
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-lock ml-1"></i>
            كلمة المرور *
          </label>
          <div class="relative">
            <input
              v-model="formData.password"
              :type="showPassword ? 'text' : 'password'"
              class="neumorphic-input w-full pl-10"
              placeholder="كلمة مرور قوية"
              required
            />
            <button
              type="button"
              @click="showPassword = !showPassword"
              class="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
            >
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
          <p class="text-xs text-secondary-500 mt-1">يجب أن تحتوي على 8 أحرف على الأقل</p>
        </div>

        <!-- Role Selection -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-user-tag ml-1"></i>
            الدور *
          </label>
          <div class="space-y-3">
            <label
              v-for="role in roles"
              :key="role.id"
              :class="[
                'neumorphic-button cursor-pointer p-4 transition-all duration-300 block',
                formData.role.id === role.id 
                  ? 'bg-primary-100 text-primary-700 shadow-neumorphic-inset' 
                  : ''
              ]"
            >
              <input
                v-model="formData.role"
                :value="role"
                type="radio"
                class="hidden"
              />
              <div class="flex items-start gap-3">
                <div class="flex-shrink-0 mt-1">
                  <div :class="[
                    'w-4 h-4 rounded-full border-2 flex items-center justify-center',
                    formData.role.id === role.id 
                      ? 'bg-primary-500 border-primary-500' 
                      : 'border-secondary-300'
                  ]">
                    <div v-if="formData.role.id === role.id" class="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
                <div class="flex-1">
                  <h3 class="font-semibold mb-1">{{ role.displayName }}</h3>
                  <p class="text-sm text-secondary-600 mb-2">{{ getRoleDescription(role.name) }}</p>
                  <div class="flex flex-wrap gap-1">
                    <span
                      v-for="permission in getPermissionsSummary(role)"
                      :key="permission"
                      class="px-2 py-1 bg-secondary-100 rounded-neumorphic-sm text-xs text-secondary-700"
                    >
                      {{ permission }}
                    </span>
                  </div>
                </div>
              </div>
            </label>
          </div>
        </div>

        <!-- User Status -->
        <div>
          <label class="flex items-center gap-3 neumorphic-button cursor-pointer p-4">
            <input
              v-model="formData.isActive"
              type="checkbox"
              class="hidden"
            />
            <div :class="[
              'w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-300',
              formData.isActive 
                ? 'bg-success-500 border-success-500 text-white' 
                : 'border-secondary-300'
            ]">
              <i v-if="formData.isActive" class="fas fa-check text-xs"></i>
            </div>
            <div>
              <span class="font-medium">حساب نشط</span>
              <p class="text-sm text-secondary-600">يمكن للمستخدم تسجيل الدخول واستخدام النظام</p>
            </div>
          </label>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-end gap-3 pt-4 border-t border-secondary-200">
          <button
            type="button"
            @click="$emit('cancel')"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700"
          >
            إلغاء
          </button>
          <button
            type="submit"
            class="neumorphic-button text-primary-600 hover:text-primary-700"
            :disabled="!isFormValid"
          >
            <i class="fas fa-save ml-2"></i>
            {{ isEditing ? 'تحديث' : 'إضافة' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { User, UserRole } from '@/types'

// Props
interface Props {
  user?: User | null
  isEditing: boolean
  roles: UserRole[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  save: [user: Omit<User, 'id'> | User]
  cancel: []
}>()

// Form data
const formData = ref<Omit<User, 'id'> & { id?: string; password?: string }>({
  name: '',
  email: '',
  role: props.roles[0] || {} as UserRole,
  permissions: [],
  createdAt: new Date(),
  updatedAt: new Date(),
  isActive: true,
  password: ''
})

const showPassword = ref(false)

// Computed
const isFormValid = computed(() => {
  const hasRequiredFields = formData.value.name.trim().length > 0 && 
                           formData.value.email.trim().length > 0 &&
                           formData.value.role.id
  
  if (!props.isEditing) {
    return hasRequiredFields && formData.value.password && formData.value.password.length >= 8
  }
  
  return hasRequiredFields
})

// Methods
function getRoleDescription(roleName: string): string {
  const descriptions: Record<string, string> = {
    admin: 'صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات',
    officer: 'إدارة بيانات المتهمين وإنشاء التقارير',
    viewer: 'عرض البيانات والتقارير فقط دون إمكانية التعديل'
  }
  return descriptions[roleName] || ''
}

function getPermissionsSummary(role: UserRole): string[] {
  const permissions = role.permissions
  const summary: string[] = []
  
  if (permissions.some(p => p.resource === '*')) {
    summary.push('جميع الصلاحيات')
  } else {
    const resources = [...new Set(permissions.map(p => p.resource))]
    resources.forEach(resource => {
      const resourcePermissions = permissions.filter(p => p.resource === resource)
      const actions = resourcePermissions.map(p => p.action)
      
      let resourceName = resource
      if (resource === 'suspects') resourceName = 'المتهمين'
      else if (resource === 'reports') resourceName = 'التقارير'
      else if (resource === 'settings') resourceName = 'الإعدادات'
      
      if (actions.includes('view') && actions.includes('create') && actions.includes('update') && actions.includes('delete')) {
        summary.push(`${resourceName} (كامل)`)
      } else {
        const actionNames = actions.map(a => {
          if (a === 'view') return 'عرض'
          if (a === 'create') return 'إضافة'
          if (a === 'update') return 'تعديل'
          if (a === 'delete') return 'حذف'
          return a
        })
        summary.push(`${resourceName} (${actionNames.join(', ')})`)
      }
    })
  }
  
  return summary
}

function handleSubmit() {
  if (!isFormValid.value) return

  const userData = {
    ...formData.value,
    updatedAt: new Date()
  }

  // Remove password field for editing
  if (props.isEditing) {
    delete userData.password
  }

  emit('save', userData)
}

// Initialize form data
onMounted(() => {
  if (props.user) {
    formData.value = {
      ...props.user,
      password: ''
    }
  }
})
</script>
