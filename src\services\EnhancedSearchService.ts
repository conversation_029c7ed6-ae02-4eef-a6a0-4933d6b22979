import type { SuspectData } from '@/types'

export interface SearchParams {
  query?: string
  filters?: SearchFilters
  pagination?: SearchPagination
  sorting?: SearchSorting
}

export interface SearchFilters {
  nationality?: string
  isReleased?: boolean
  isTransferred?: boolean
  dateFrom?: string
  dateTo?: string
  ageFrom?: number
  ageTo?: number
  profession?: string
  maritalStatus?: string
}

export interface SearchPagination {
  page: number
  limit: number
}

export interface SearchSorting {
  field: string
  order: 'ASC' | 'DESC'
}

export interface SearchResult {
  success: boolean
  data: SuspectData[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasMore: boolean
  }
  query?: string
  filters?: SearchFilters
  executionTime?: number
  error?: string
}

export interface QuickSearchResult {
  success: boolean
  suggestions: SearchSuggestion[]
  query: string
  error?: string
}

export interface SearchSuggestion {
  id: string
  text: string
  type: string
  data: {
    fileNumber: string
    fullName: string
    idNumber?: string
  }
}

export interface SearchStats {
  success: boolean
  stats: {
    total_suspects: number
    released_count: number
    transferred_count: number
    active_count: number
    nationalities_count: number
    earliest_arrest: string
    latest_arrest: string
    average_age: number
    nationality_distribution: Array<{ nationality: string; count: number }>
    monthly_trends: Array<{ month: string; arrests_count: number }>
  }
  error?: string
}

export class EnhancedSearchService {
  private isElectron: boolean

  constructor() {
    this.isElectron = !!(window as any).electronAPI
  }

  // Advanced search with multiple criteria
  async advancedSearch(searchParams: SearchParams): Promise<SearchResult> {
    if (this.isElectron) {
      return await this.electronSearch(searchParams)
    } else {
      return await this.webSearch(searchParams)
    }
  }

  // Quick search for autocomplete
  async quickSearch(query: string, limit: number = 10): Promise<QuickSearchResult> {
    if (this.isElectron) {
      return await (window as any).electronAPI.dbQuickSearch(query, limit)
    } else {
      return await this.webQuickSearch(query, limit)
    }
  }

  // Search with highlighting
  async searchWithHighlight(query: string, options: Partial<SearchParams> = {}): Promise<SearchResult> {
    if (this.isElectron) {
      return await (window as any).electronAPI.dbSearchWithHighlight(query, options)
    } else {
      return await this.webSearchWithHighlight(query, options)
    }
  }

  // Fuzzy search for typo tolerance
  async fuzzySearch(query: string, threshold: number = 0.7): Promise<any> {
    if (this.isElectron) {
      return await (window as any).electronAPI.dbFuzzySearch(query, threshold)
    } else {
      return await this.webFuzzySearch(query, threshold)
    }
  }

  // Get search statistics
  async getSearchStats(): Promise<SearchStats> {
    if (this.isElectron) {
      return await (window as any).electronAPI.dbGetStats()
    } else {
      return await this.webGetStats()
    }
  }

  // Electron-specific search implementation
  private async electronSearch(searchParams: SearchParams): Promise<SearchResult> {
    try {
      const result = await (window as any).electronAPI.dbSearch(searchParams)
      return result
    } catch (error) {
      console.error('Electron search error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Search failed',
        data: [],
        pagination: { page: 1, limit: 50, total: 0, totalPages: 0, hasMore: false }
      }
    }
  }

  // Web-specific search implementation (fallback to IndexedDB)
  private async webSearch(searchParams: SearchParams): Promise<SearchResult> {
    try {
      // Import database dynamically to avoid issues in Electron
      const { db } = await import('@/utils/database')
      
      const {
        query = '',
        filters = {},
        pagination = { page: 1, limit: 50 },
        sorting = { field: 'created_at', order: 'DESC' }
      } = searchParams

      let suspects = await db.suspects.orderBy(sorting.field).toArray()

      // Apply text search
      if (query.trim()) {
        const searchTerm = query.toLowerCase().trim()
        suspects = suspects.filter(suspect => {
          const searchableText = [
            suspect.fields.fileNumber,
            suspect.fields.fullName,
            suspect.fields.idNumber,
            suspect.fields.address,
            suspect.fields.phone,
            suspect.fields.nationality,
            suspect.fields.profession,
            suspect.fields.notes
          ].join(' ').toLowerCase()

          return searchableText.includes(searchTerm)
        })
      }

      // Apply filters
      if (filters.nationality) {
        suspects = suspects.filter(s => s.fields.nationality === filters.nationality)
      }

      if (filters.isReleased !== undefined) {
        suspects = suspects.filter(s => s.fields.isReleased === filters.isReleased)
      }

      if (filters.isTransferred !== undefined) {
        suspects = suspects.filter(s => s.fields.isTransferred === filters.isTransferred)
      }

      if (filters.dateFrom) {
        suspects = suspects.filter(s => s.fields.arrestDate && s.fields.arrestDate >= new Date(filters.dateFrom!))
      }

      if (filters.dateTo) {
        suspects = suspects.filter(s => s.fields.arrestDate && s.fields.arrestDate <= new Date(filters.dateTo!))
      }

      // Apply sorting
      if (sorting.order === 'DESC') {
        suspects.reverse()
      }

      // Apply pagination
      const total = suspects.length
      const offset = (pagination.page - 1) * pagination.limit
      const paginatedSuspects = suspects.slice(offset, offset + pagination.limit)

      return {
        success: true,
        data: paginatedSuspects,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasMore: offset + pagination.limit < total
        },
        query,
        filters
      }
    } catch (error) {
      console.error('Web search error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Search failed',
        data: [],
        pagination: { page: 1, limit: 50, total: 0, totalPages: 0, hasMore: false }
      }
    }
  }

  // Web quick search implementation
  private async webQuickSearch(query: string, limit: number): Promise<QuickSearchResult> {
    try {
      if (!query || query.trim().length < 2) {
        return { success: true, suggestions: [], query }
      }

      const { db } = await import('@/utils/database')
      const suspects = await db.suspects.limit(1000).toArray()
      
      const searchTerm = query.toLowerCase().trim()
      const suggestions: SearchSuggestion[] = []

      suspects.forEach(suspect => {
        const fullName = suspect.fields.fullName?.toLowerCase() || ''
        const fileNumber = suspect.fields.fileNumber?.toLowerCase() || ''
        const idNumber = suspect.fields.idNumber?.toLowerCase() || ''

        if (fullName.includes(searchTerm) || fileNumber.includes(searchTerm) || idNumber.includes(searchTerm)) {
          suggestions.push({
            id: `suspect_${suspect.fields.fileNumber}`,
            text: `${suspect.fields.fullName} (${suspect.fields.fileNumber})`,
            type: 'suspect',
            data: {
              fileNumber: suspect.fields.fileNumber || '',
              fullName: suspect.fields.fullName || '',
              idNumber: suspect.fields.idNumber
            }
          })
        }
      })

      return {
        success: true,
        suggestions: suggestions.slice(0, limit),
        query
      }
    } catch (error) {
      console.error('Web quick search error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Quick search failed',
        suggestions: [],
        query
      }
    }
  }

  // Web search with highlighting
  private async webSearchWithHighlight(query: string, options: Partial<SearchParams>): Promise<SearchResult> {
    const searchResult = await this.webSearch({ query, ...options })
    
    if (!searchResult.success || !query.trim()) {
      return searchResult
    }

    // Add highlighting to results
    const searchTerms = query.trim().toLowerCase().split(/\s+/)
    const highlightedResults = searchResult.data.map(suspect => {
      const highlighted = { ...suspect }
      
      // Fields to highlight
      const fieldsToHighlight = ['fullName', 'fileNumber', 'idNumber', 'address', 'notes']
      
      fieldsToHighlight.forEach(field => {
        const fieldValue = suspect.fields[field]
        if (fieldValue) {
          let text = fieldValue.toString()
          
          searchTerms.forEach(term => {
            if (term.length > 1) {
              const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi')
              text = text.replace(regex, '<mark>$1</mark>')
            }
          })
          
          highlighted.fields[`${field}_highlighted`] = text
        }
      })

      return highlighted
    })

    return {
      ...searchResult,
      data: highlightedResults
    }
  }

  // Web fuzzy search
  private async webFuzzySearch(query: string, threshold: number): Promise<any> {
    try {
      if (!query || query.trim().length < 3) {
        return { success: true, results: [] }
      }

      const { db } = await import('@/utils/database')
      const suspects = await db.suspects.limit(1000).toArray()
      
      const queryLower = query.trim().toLowerCase()
      const fuzzyResults: any[] = []

      suspects.forEach(suspect => {
        const nameScore = this.calculateSimilarity(queryLower, (suspect.fields.fullName || '').toLowerCase())
        const fileScore = this.calculateSimilarity(queryLower, (suspect.fields.fileNumber || '').toLowerCase())
        const idScore = suspect.fields.idNumber ? this.calculateSimilarity(queryLower, suspect.fields.idNumber.toLowerCase()) : 0

        const maxScore = Math.max(nameScore, fileScore, idScore)

        if (maxScore >= threshold) {
          fuzzyResults.push({
            ...suspect,
            similarity_score: maxScore,
            matched_field: nameScore === maxScore ? 'name' : (fileScore === maxScore ? 'file_number' : 'id_number')
          })
        }
      })

      fuzzyResults.sort((a, b) => b.similarity_score - a.similarity_score)

      return {
        success: true,
        results: fuzzyResults.slice(0, 20),
        query,
        threshold
      }
    } catch (error) {
      console.error('Web fuzzy search error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Fuzzy search failed',
        results: []
      }
    }
  }

  // Web stats implementation
  private async webGetStats(): Promise<SearchStats> {
    try {
      const { db } = await import('@/utils/database')
      const suspects = await db.suspects.toArray()

      const stats = {
        total_suspects: suspects.length,
        released_count: suspects.filter(s => s.fields.isReleased).length,
        transferred_count: suspects.filter(s => s.fields.isTransferred).length,
        active_count: suspects.filter(s => !s.fields.isReleased && !s.fields.isTransferred).length,
        nationalities_count: new Set(suspects.map(s => s.fields.nationality).filter(Boolean)).size,
        earliest_arrest: suspects.reduce((earliest, s) => {
          if (s.fields.arrestDate && (!earliest || s.fields.arrestDate < new Date(earliest))) {
            return s.fields.arrestDate.toISOString()
          }
          return earliest
        }, ''),
        latest_arrest: suspects.reduce((latest, s) => {
          if (s.fields.arrestDate && (!latest || s.fields.arrestDate > new Date(latest))) {
            return s.fields.arrestDate.toISOString()
          }
          return latest
        }, ''),
        average_age: suspects.reduce((sum, s) => sum + (parseInt(s.fields.age) || 0), 0) / suspects.length,
        nationality_distribution: [],
        monthly_trends: []
      }

      return { success: true, stats }
    } catch (error) {
      console.error('Web stats error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Stats failed',
        stats: {} as any
      }
    }
  }

  // Helper methods
  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1

    if (longer.length === 0) return 1.0

    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1,     // insertion
            matrix[i - 1][j] + 1      // deletion
          )
        }
      }
    }

    return matrix[str2.length][str1.length]
  }

  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }
}
