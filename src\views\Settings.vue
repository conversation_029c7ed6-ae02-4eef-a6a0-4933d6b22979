<template>
  <div class="min-h-screen bg-secondary-50 p-6 rtl">
    <!-- Header -->
    <div class="neumorphic-header mb-8">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <div class="neumorphic-icon">
            <i class="fas fa-cog text-primary-600"></i>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-secondary-800">الإعدادات</h1>
            <p class="text-secondary-600">إدارة وتخصيص إعدادات النظام</p>
          </div>
        </div>
        <div class="flex gap-3">
          <button 
            @click="exportSettings"
            class="neumorphic-button text-success-600 hover:text-success-700"
            :disabled="isLoading"
          >
            <i class="fas fa-download ml-2"></i>
            تصدير الإعدادات
          </button>
          <button
            @click="triggerImport"
            class="neumorphic-button text-primary-600 hover:text-primary-700"
            :disabled="isLoading"
          >
            <i class="fas fa-upload ml-2"></i>
            استيراد الإعدادات
          </button>
          <button
            @click="fixDatabase"
            class="neumorphic-button text-warning-600 hover:text-warning-700"
            :disabled="isLoading"
          >
            <i class="fas fa-wrench ml-2"></i>
            إصلاح البيانات
          </button>
          <button
            @click="clearDatabase"
            class="neumorphic-button text-danger-600 hover:text-danger-700"
            :disabled="isLoading"
          >
            <i class="fas fa-trash ml-2"></i>
            مسح البيانات
          </button>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <div class="neumorphic-card mb-8">
      <div class="flex flex-wrap gap-2 p-2">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            'neumorphic-tab flex items-center gap-2 px-4 py-3 transition-all duration-300',
            { 'active': activeTab === tab.id }
          ]"
        >
          <i :class="tab.icon"></i>
          <span>{{ tab.label }}</span>
        </button>
      </div>
    </div>

    <!-- Tab Content -->
    <div class="grid grid-cols-1 gap-8">
      <!-- Fields Configuration Tab -->
      <FieldsConfiguration 
        v-if="activeTab === 'fields'"
        :fields="suspectFields"
        :loading="isLoading"
        @add-field="handleAddField"
        @update-field="handleUpdateField"
        @delete-field="handleDeleteField"
        @reorder-fields="handleReorderFields"
      />

      <!-- Users Management Tab -->
      <UserManagement
        v-if="activeTab === 'users'"
        :loading="isLoading"
      />

      <!-- Theme Customization Tab -->
      <ThemeCustomization 
        v-if="activeTab === 'theme'"
        :theme="theme"
        :organization="organization"
        :loading="isLoading"
        @update-theme="handleUpdateTheme"
        @update-organization="handleUpdateOrganization"
      />

      <!-- Developer Settings Tab -->
      <DeveloperSettings
        v-if="activeTab === 'developer'"
      />

      <!-- Backup & Restore Tab -->
      <BackupRestore
        v-if="activeTab === 'backup'"
        :backup="backup"
        :loading="isLoading"
        @update-backup="handleUpdateBackup"
        @export-data="handleExportData"
        @import-data="handleImportData"
      />
    </div>

    <!-- Hidden file input for import -->
    <input
      ref="fileInput"
      type="file"
      accept=".json"
      @change="handleFileImport"
      class="hidden"
    />

    <!-- Loading Overlay -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="neumorphic-card p-8 text-center">
        <div class="spinner mx-auto mb-4"></div>
        <p class="text-secondary-700">جاري المعالجة...</p>
      </div>
    </div>

    <!-- Success/Error Messages -->
    <Transition name="fade">
      <div v-if="message" :class="[
        'fixed top-4 right-4 p-4 rounded-neumorphic shadow-neumorphic z-50',
        messageType === 'success' ? 'bg-success-100 text-success-800' : 'bg-danger-100 text-danger-800'
      ]">
        <div class="flex items-center gap-3">
          <i :class="messageType === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
          <span>{{ message }}</span>
          <button @click="message = ''" class="mr-auto">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useSettingsStore } from '@/stores/settings'
import type { SuspectField, ThemeSettings, OrganizationSettings, BackupSettings } from '@/types'

// Components (will be created next)
import FieldsConfiguration from '@/components/settings/FieldsConfiguration.vue'
import UserManagement from '@/components/settings/UserManagement.vue'
import ThemeCustomization from '@/components/settings/ThemeCustomization.vue'
import DeveloperSettings from '@/components/settings/DeveloperSettings.vue'
import BackupRestore from '@/components/settings/BackupRestore.vue'

// Store
const settingsStore = useSettingsStore()

// Reactive data
const activeTab = ref('fields')
const message = ref('')
const messageType = ref<'success' | 'error'>('success')
const fileInput = ref<HTMLInputElement>()

// Computed
const isLoading = computed(() => settingsStore.isLoading)
const suspectFields = computed(() => settingsStore.suspectFields)
const theme = computed(() => settingsStore.theme)
const organization = computed(() => settingsStore.organization)
const backup = computed(() => settingsStore.backup)

// Tabs configuration
const tabs = [
  {
    id: 'fields',
    label: 'تخصيص حقول البيانات',
    icon: 'fas fa-list-alt'
  },
  {
    id: 'users',
    label: 'إدارة المستخدمين',
    icon: 'fas fa-users'
  },
  {
    id: 'theme',
    label: 'تخصيص المظهر',
    icon: 'fas fa-palette'
  },
  {
    id: 'developer',
    label: 'إعدادات المطور',
    icon: 'fas fa-code'
  },
  {
    id: 'backup',
    label: 'النسخ الاحتياطي',
    icon: 'fas fa-database'
  }
]

// Methods
async function handleAddField(field: Omit<SuspectField, 'id'>) {
  try {
    await settingsStore.addSuspectField(field)
    showMessage('تم إضافة الحقل بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في إضافة الحقل', 'error')
  }
}

async function handleUpdateField(fieldId: string, updates: Partial<SuspectField>) {
  try {
    await settingsStore.updateSuspectField(fieldId, updates)
    showMessage('تم تحديث الحقل بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في تحديث الحقل', 'error')
  }
}

async function handleDeleteField(fieldId: string) {
  try {
    await settingsStore.deleteSuspectField(fieldId)
    showMessage('تم حذف الحقل بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في حذف الحقل', 'error')
  }
}

async function handleReorderFields(fields: SuspectField[]) {
  try {
    await settingsStore.updateSuspectFields(fields)
    showMessage('تم إعادة ترتيب الحقول بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في إعادة ترتيب الحقول', 'error')
  }
}

async function handleUpdateTheme(updates: Partial<ThemeSettings>) {
  try {
    await settingsStore.updateTheme(updates)
    showMessage('تم تحديث المظهر بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في تحديث المظهر', 'error')
  }
}

async function handleUpdateOrganization(updates: Partial<OrganizationSettings>) {
  try {
    await settingsStore.updateOrganization(updates)
    showMessage('تم تحديث بيانات المؤسسة بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في تحديث بيانات المؤسسة', 'error')
  }
}

async function handleUpdateBackup(updates: Partial<BackupSettings>) {
  try {
    await settingsStore.updateBackupSettings(updates)
    showMessage('تم تحديث إعدادات النسخ الاحتياطي بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في تحديث إعدادات النسخ الاحتياطي', 'error')
  }
}

async function exportSettings() {
  const success = await settingsStore.exportSettings()
  if (success) {
    showMessage('تم تصدير الإعدادات بنجاح', 'success')
  } else {
    showMessage('فشل في تصدير الإعدادات', 'error')
  }
}

function triggerImport() {
  fileInput.value?.click()
}

async function handleFileImport(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    const success = await settingsStore.importSettings(file)
    if (success) {
      showMessage('تم استيراد الإعدادات بنجاح', 'success')
    } else {
      showMessage('فشل في استيراد الإعدادات', 'error')
    }
  }
  
  // Reset file input
  target.value = ''
}

async function handleExportData() {
  await exportSettings()
}

async function handleImportData(file: File) {
  const success = await settingsStore.importSettings(file)
  if (success) {
    showMessage('تم استيراد البيانات بنجاح', 'success')
  } else {
    showMessage('فشل في استيراد البيانات', 'error')
  }
}

function showMessage(text: string, type: 'success' | 'error') {
  message.value = text
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

async function fixDatabase() {
  try {
    const { db } = await import('@/utils/database')
    const fixedCount = await db.fixFieldOptionsFormat()
    if (fixedCount > 0) {
      showMessage(`تم إصلاح ${fixedCount} حقل بنجاح`, 'success')
      // Reload settings
      await settingsStore.loadSettings()
    } else {
      showMessage('لا توجد بيانات تحتاج إصلاح', 'success')
    }
  } catch (error) {
    showMessage('فشل في إصلاح البيانات', 'error')
    console.error('Error fixing database:', error)
  }
}

async function clearDatabase() {
  if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
    try {
      const { db } = await import('@/utils/database')
      await db.clearAndReinitialize()
      showMessage('تم مسح البيانات وإعادة التهيئة بنجاح', 'success')
      // Reload the page to refresh all data
      setTimeout(() => {
        window.location.reload()
      }, 1000)
    } catch (error) {
      showMessage('فشل في مسح البيانات', 'error')
      console.error('Error clearing database:', error)
    }
  }
}

// Lifecycle
onMounted(() => {
  settingsStore.loadSettings()
})
</script>
