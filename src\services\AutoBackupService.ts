import { ref, watch } from 'vue'
import type { BackupSettings } from '@/types'
import { BackupService } from './BackupService'
import { useSettingsStore } from '@/stores/settings'

/**
 * Auto Backup Service
 * Manages automatic backup scheduling and execution
 */
export class AutoBackupService {
  private static instance: AutoBackupService
  private intervalId: number | null = null
  private isRunning = ref(false)
  private nextBackupTime = ref<Date | null>(null)
  private backupService: BackupService
  private settingsStore = useSettingsStore()

  private constructor() {
    this.backupService = BackupService.getInstance()
    this.initializeWatchers()
  }

  static getInstance(): AutoBackupService {
    if (!AutoBackupService.instance) {
      AutoBackupService.instance = new AutoBackupService()
    }
    return AutoBackupService.instance
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      nextBackupTime: this.nextBackupTime
    }
  }

  /**
   * Start auto backup service
   */
  async start(settings: BackupSettings): Promise<void> {
    try {
      // Stop any existing interval
      this.stop()

      if (!settings.autoBackup) {
        console.log('🔄 Auto backup is disabled')
        return
      }

      const intervalMs = settings.backupInterval * 60 * 60 * 1000 // Convert hours to milliseconds
      
      console.log(`🚀 Starting auto backup service with interval: ${settings.backupInterval} hours`)
      
      // Set up the interval
      this.intervalId = window.setInterval(async () => {
        await this.performAutoBackup(settings)
      }, intervalMs)

      this.isRunning.value = true
      this.calculateNextBackupTime(settings.backupInterval)

      // Perform initial backup if none exists or if it's been too long
      await this.checkAndPerformInitialBackup(settings)

      console.log('✅ Auto backup service started successfully')
    } catch (error) {
      console.error('❌ Failed to start auto backup service:', error)
      this.stop()
      throw error
    }
  }

  /**
   * Stop auto backup service
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId)
      this.intervalId = null
    }
    
    this.isRunning.value = false
    this.nextBackupTime.value = null
    
    console.log('🛑 Auto backup service stopped')
  }

  /**
   * Force an immediate auto backup
   */
  async forceBackup(): Promise<boolean> {
    try {
      const settings = this.settingsStore.backup
      return await this.performAutoBackup(settings)
    } catch (error) {
      console.error('❌ Force backup failed:', error)
      return false
    }
  }

  /**
   * Check if auto backup should run based on last backup time
   */
  shouldRunAutoBackup(settings: BackupSettings): boolean {
    if (!settings.autoBackup) return false
    
    const lastBackup = settings.lastBackup
    if (!lastBackup) return true // No previous backup
    
    const now = new Date()
    const timeSinceLastBackup = now.getTime() - lastBackup.getTime()
    const intervalMs = settings.backupInterval * 60 * 60 * 1000
    
    return timeSinceLastBackup >= intervalMs
  }

  // Private methods
  private initializeWatchers(): void {
    // Watch for settings changes and restart service if needed
    watch(
      () => this.settingsStore.backup,
      (newSettings, oldSettings) => {
        if (!oldSettings) return // Initial load
        
        // Restart service if auto backup settings changed
        if (
          newSettings.autoBackup !== oldSettings.autoBackup ||
          newSettings.backupInterval !== oldSettings.backupInterval
        ) {
          console.log('🔄 Backup settings changed, restarting auto backup service')
          this.start(newSettings)
        }
      },
      { deep: true }
    )
  }

  private async performAutoBackup(settings: BackupSettings): Promise<boolean> {
    try {
      console.log('🔄 Performing automatic backup...')
      
      // Check if we should actually run the backup
      if (!this.shouldRunAutoBackup(settings)) {
        console.log('⏭️ Skipping auto backup - not yet time')
        return false
      }

      // Create the backup
      const backupRecord = await this.backupService.createBackup(
        'auto',
        {
          compressionEnabled: true, // Enable compression for auto backups
          encryptBackups: settings.encryptBackups,
          backupPassword: settings.backupPassword
        },
        `نسخة تلقائية - ${new Date().toLocaleString('ar-SA')}`
      )

      if (backupRecord) {
        // Update last backup time in settings
        await this.settingsStore.updateBackupSettings({
          lastBackup: new Date()
        })

        // Calculate next backup time
        this.calculateNextBackupTime(settings.backupInterval)

        // Clean up old backups if needed
        await this.cleanupOldBackups(settings.maxBackups)

        console.log('✅ Automatic backup completed successfully')
        
        // Show notification (if notifications are available)
        this.showBackupNotification('تم إنشاء نسخة احتياطية تلقائية بنجاح', 'success')
        
        return true
      } else {
        console.error('❌ Automatic backup failed')
        this.showBackupNotification('فشل في إنشاء النسخة الاحتياطية التلقائية', 'error')
        return false
      }
    } catch (error) {
      console.error('❌ Auto backup error:', error)
      this.showBackupNotification('خطأ في النسخ الاحتياطي التلقائي', 'error')
      return false
    }
  }

  private async checkAndPerformInitialBackup(settings: BackupSettings): Promise<void> {
    // Check if we need to perform an initial backup
    if (this.shouldRunAutoBackup(settings)) {
      console.log('🔄 Performing initial auto backup...')
      await this.performAutoBackup(settings)
    }
  }

  private calculateNextBackupTime(intervalHours: number): void {
    const now = new Date()
    const nextTime = new Date(now.getTime() + (intervalHours * 60 * 60 * 1000))
    this.nextBackupTime.value = nextTime
  }

  private async cleanupOldBackups(maxBackups: number): Promise<void> {
    try {
      const history = await this.backupService.getBackupHistory()
      const autoBackups = history.filter(record => record.type === 'auto')
      
      if (autoBackups.length > maxBackups) {
        // Sort by date (newest first)
        autoBackups.sort((a, b) => b.date.getTime() - a.date.getTime())
        
        // Remove old backups
        const backupsToRemove = autoBackups.slice(maxBackups)
        for (const backup of backupsToRemove) {
          await this.backupService.deleteBackupRecord(backup.id)
        }
        
        console.log(`🧹 Cleaned up ${backupsToRemove.length} old auto backups`)
      }
    } catch (error) {
      console.error('❌ Failed to cleanup old backups:', error)
    }
  }

  private showBackupNotification(message: string, type: 'success' | 'error'): void {
    // Simple notification - can be enhanced with a proper notification system
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('النسخ الاحتياطي', {
        body: message,
        icon: type === 'success' ? '/favicon.ico' : undefined,
        tag: 'backup-notification'
      })
    } else {
      // Fallback to console
      console.log(`📢 ${type.toUpperCase()}: ${message}`)
    }
  }
}

// Export singleton instance
export const autoBackupService = AutoBackupService.getInstance()
