{"name": "suspects-data-management", "version": "1.0.0", "description": "برنامج متكامل لتنظيم بيانات المتهمين", "author": {"name": "محرم اليفرسي", "email": "<EMAIL>"}, "license": "MIT", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "electron": "electron .", "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5175 && electron .\"", "electron:build": "npm run build && electron-builder", "electron:dist": "vite build && electron-builder --publish=never", "electron:dist-win": "vite build && electron-builder --win --publish=never", "electron:dist-mac": "npm run build && electron-builder --mac --publish=never", "electron:dist-linux": "npm run build && electron-builder --linux --publish=never", "electron:publish": "npm run build && electron-builder --publish=always", "electron:pack": "npm run build && electron-builder --dir", "postinstall": "electron-builder install-app-deps", "clean": "rimraf dist dist-electron node_modules/.cache", "test": "echo \"No tests specified\" && exit 0", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "@vueuse/core": "^10.7.0", "bcryptjs": "^2.4.3", "better-sqlite3": "^12.2.0", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "date-fns": "^2.30.0", "dexie": "^3.2.4", "electron-builder-notarize": "^1.5.2", "electron-updater": "^6.6.2", "fast-csv": "^5.0.2", "file-saver": "^2.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jszip": "^3.10.1", "pako": "^2.1.0", "papaparse": "^5.5.3", "pinia": "^2.1.7", "stream-transform": "^3.3.3", "vue": "^3.4.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.5", "@types/jszip": "^3.4.1", "@types/node": "^20.10.0", "@types/pako": "^2.0.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.13.3", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.4", "vue-tsc": "^1.8.25", "wait-on": "^7.2.0"}, "main": "electron/main.js", "homepage": "./", "build": {"appId": "com.suspects.data.management", "productName": "برنامج بيانات المتهمين", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*", "node_modules/**/*", "!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}", "!node_modules/*/{test,__tests__,tests,powered-test,example,examples}", "!node_modules/*.d.ts", "!node_modules/.bin", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "signAndEditExecutable": false, "signDlls": false, "icon": "public/icon.png", "artifactName": "${productName}-${version}-${arch}.${ext}", "publisherName": "Mohrrm886", "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Suspects Data Management", "runAfterFinish": false, "menuCategory": "Security Tools", "installerHeaderIcon": "public/icon.ico", "installerSidebar": "public/icon.ico", "uninstallerSidebar": "public/icon.ico", "allowElevation": true, "perMachine": false, "artifactName": "${productName}-Setup-${version}.${ext}", "deleteAppDataOnUninstall": false}, "portable": {"artifactName": "${productName}-${version}-portable.${ext}"}, "linux": {"target": "AppImage"}, "compression": "maximum", "nodeGypRebuild": true, "buildDependenciesFromSource": true, "npmRebuild": true, "electronVersion": "28.3.3", "extraMetadata": {"main": "electron/main.js"}, "extraResources": [{"from": "electron/", "to": "electron/", "filter": ["**/*"]}]}}