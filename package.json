{"name": "suspects-data-management", "version": "1.0.0", "description": "برنامج متكامل لتنظيم بيانات المتهمين", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "build-electron": "npm run build && electron-builder", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "@vueuse/core": "^10.7.0", "bcryptjs": "^2.4.3", "better-sqlite3": "^12.2.0", "crypto-js": "^4.2.0", "csv-parser": "^3.2.0", "date-fns": "^2.30.0", "dexie": "^3.2.4", "fast-csv": "^5.0.2", "file-saver": "^2.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "jszip": "^3.10.1", "pako": "^2.1.0", "papaparse": "^5.5.3", "pinia": "^2.1.7", "stream-transform": "^3.3.3", "vue": "^3.4.0", "vue-i18n": "^9.8.0", "vue-router": "^4.2.5", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/jsonwebtoken": "^9.0.5", "@types/jszip": "^3.4.1", "@types/node": "^20.10.0", "@types/pako": "^2.0.3", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^28.0.0", "electron-builder": "^24.9.1", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.3.0", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.4", "vue-tsc": "^1.8.25", "wait-on": "^7.2.0"}, "main": "electron/main.js", "homepage": "./", "build": {"appId": "com.suspects.data.management", "productName": "برنامج بيانات المتهمين", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "electron/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}