# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependencies
node_modules
.pnpm
.npm

# Build outputs
dist
dist-ssr
dist-electron
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
Thumbs.db
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Electron
app/
release/
out/
packages/
electron-builder-output/

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
backups/
*.backup
*.bak
migration-backups/

# Certificate files (for security)
*.p12
*.pfx
*.pem
*.key
*.crt
*.cer

# User data and uploads
user-data/
app-data/
uploads/
attachments/

# Performance test results
performance-results/
benchmark-results/

# Runtime configuration
runtime-config.json

# Build logs
build.log
electron-build.log

# Auto-generated files
auto-generated/
generated/

# Cache directories
cache/

# Development specific
dev.db
test.db
electron-app-data/
