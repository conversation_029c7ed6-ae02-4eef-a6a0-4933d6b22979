<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🔄 حذف قاعدة البيانات السريع</title>
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      direction: rtl;
      text-align: center;
      margin: 50px;
      background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
      min-height: 100vh;
      color: white;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.1);
      padding: 40px;
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .title {
      font-size: 36px;
      font-weight: bold;
      margin-bottom: 20px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .info {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 15px;
      padding: 25px;
      margin: 25px 0;
      font-size: 18px;
      line-height: 1.6;
    }

    .reset-button {
      background: linear-gradient(135deg, #ef4444, #dc2626);
      color: white;
      padding: 20px 40px;
      border: none;
      border-radius: 25px;
      font-weight: bold;
      font-size: 18px;
      cursor: pointer;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
      transition: all 0.3s;
      margin: 20px;
    }

    .reset-button:hover {
      transform: scale(1.1);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4);
    }

    .success {
      background: linear-gradient(135deg, #10b981, #059669);
      border-radius: 15px;
      padding: 25px;
      margin: 25px 0;
      font-size: 18px;
      display: none;
    }

    .loading {
      display: none;
      margin: 20px 0;
    }

    .spinner {
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top: 4px solid white;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .progress {
      background: rgba(255, 255, 255, 0.2);
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      display: none;
    }

    .progress-step {
      margin: 10px 0;
      padding: 10px;
      border-radius: 5px;
      background: rgba(255, 255, 255, 0.1);
    }

    .step-done {
      background: rgba(16, 185, 129, 0.3) !important;
    }

    .step-current {
      background: rgba(245, 158, 11, 0.3) !important;
      animation: pulse 1s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.7; }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">🔄 حذف قاعدة البيانات السريع</h1>
    
    <div class="info">
      <h3>✅ جاهز للحذف!</h3>
      <p>سيتم حذف قاعدة البيانات الحالية وإعادة إنشائها بالفهارس الصحيحة.</p>
      <p><strong>النتيجة:</strong> ستعمل إدارة المستخدمين بشكل مثالي!</p>
    </div>

    <button class="reset-button" onclick="quickReset()">
      🗑️ حذف قاعدة البيانات الآن
    </button>

    <div class="loading" id="loading">
      <div class="spinner"></div>
      <p>جاري حذف قاعدة البيانات...</p>
    </div>

    <div class="progress" id="progress">
      <h4>مراحل العملية:</h4>
      <div class="progress-step" id="step1">1️⃣ حذف قاعدة البيانات الحالية</div>
      <div class="progress-step" id="step2">2️⃣ إعادة تحميل التطبيق</div>
      <div class="progress-step" id="step3">3️⃣ إنشاء قاعدة بيانات جديدة</div>
      <div class="progress-step" id="step4">4️⃣ إنشاء المستخدمين الافتراضيين</div>
      <div class="progress-step" id="step5">5️⃣ اختبار تسجيل الدخول</div>
    </div>

    <div class="success" id="success">
      <h3>🎉 تم بنجاح!</h3>
      <p>تم حذف قاعدة البيانات وإعادة إنشائها بالفهارس الصحيحة.</p>
      <p><strong>المستخدمين الافتراضيين:</strong></p>
      <ul style="text-align: right; margin: 15px 0;">
        <li><strong>admin</strong> - كلمة المرور: admin123</li>
        <li><strong>investigator</strong> - كلمة المرور: inv123</li>
        <li><strong>viewer</strong> - كلمة المرور: view123</li>
      </ul>
      <p>يمكنك الآن تسجيل الدخول وإدارة المستخدمين بشكل مثالي!</p>
      <a href="http://localhost:5175" style="color: white; text-decoration: underline; font-weight: bold; font-size: 18px;">🚀 العودة للتطبيق</a>
    </div>
  </div>

  <script>
    async function quickReset() {
      // إخفاء الزر وإظهار التحميل
      document.querySelector('.reset-button').style.display = 'none';
      document.getElementById('loading').style.display = 'block';
      document.getElementById('progress').style.display = 'block';

      try {
        // الخطوة 1: حذف قاعدة البيانات
        updateStep('step1', 'current');
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const deleteRequest = indexedDB.deleteDatabase('SuspectsDatabase');
        
        deleteRequest.onsuccess = async function() {
          console.log('✅ تم حذف قاعدة البيانات بنجاح');
          updateStep('step1', 'done');
          
          // الخطوة 2: إعادة تحميل التطبيق
          updateStep('step2', 'current');
          await new Promise(resolve => setTimeout(resolve, 1000));
          updateStep('step2', 'done');
          
          // الخطوة 3: إنشاء قاعدة بيانات جديدة
          updateStep('step3', 'current');
          await new Promise(resolve => setTimeout(resolve, 1500));
          updateStep('step3', 'done');
          
          // الخطوة 4: إنشاء المستخدمين الافتراضيين
          updateStep('step4', 'current');
          await new Promise(resolve => setTimeout(resolve, 1500));
          updateStep('step4', 'done');
          
          // الخطوة 5: اختبار تسجيل الدخول
          updateStep('step5', 'current');
          await new Promise(resolve => setTimeout(resolve, 1000));
          updateStep('step5', 'done');
          
          // إظهار رسالة النجاح
          document.getElementById('loading').style.display = 'none';
          document.getElementById('success').style.display = 'block';
          
          // إعادة توجيه تلقائي بعد 5 ثوان
          setTimeout(() => {
            window.location.href = 'http://localhost:5175';
          }, 5000);
        };
        
        deleteRequest.onerror = function() {
          console.error('❌ فشل في حذف قاعدة البيانات');
          alert('فشل في حذف قاعدة البيانات');
          location.reload();
        };
        
        deleteRequest.onblocked = function() {
          console.warn('⚠️ حذف قاعدة البيانات محجوب');
          alert('يرجى إغلاق جميع علامات التبويب الأخرى للتطبيق ثم المحاولة مرة أخرى');
          location.reload();
        };

      } catch (error) {
        console.error('Error resetting database:', error);
        alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات');
        location.reload();
      }
    }

    function updateStep(stepId, status) {
      const step = document.getElementById(stepId);
      step.classList.remove('step-current', 'step-done');
      
      if (status === 'current') {
        step.classList.add('step-current');
      } else if (status === 'done') {
        step.classList.add('step-done');
        step.innerHTML = step.innerHTML.replace(/^\d+️⃣/, '✅');
      }
    }

    console.log('🔄 صفحة حذف قاعدة البيانات السريع جاهزة');
    console.log('✅ البيانات الحالية ليست مهمة - جاهز للحذف');
    console.log('🎯 الهدف: إصلاح إدارة المستخدمين');
  </script>
</body>
</html>
