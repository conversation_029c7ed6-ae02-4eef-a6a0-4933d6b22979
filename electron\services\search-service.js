class SearchService {
  constructor(sqliteManager) {
    this.db = sqliteManager
  }

  // Advanced search with multiple criteria
  async advancedSearch(searchParams) {
    const {
      query = '',
      filters = {},
      pagination = { page: 1, limit: 50 },
      sorting = { field: 'created_at', order: 'DESC' }
    } = searchParams

    try {
      const offset = (pagination.page - 1) * pagination.limit

      const results = this.db.searchSuspects(query, {
        limit: pagination.limit,
        offset: offset,
        sortBy: sorting.field,
        sortOrder: sorting.order,
        filters: filters
      })

      return {
        success: true,
        data: results.results,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total: results.total,
          totalPages: Math.ceil(results.total / pagination.limit),
          hasMore: results.hasMore
        },
        query: query,
        filters: filters,
        executionTime: Date.now() // Will be calculated properly
      }
    } catch (error) {
      console.error('Advanced search error:', error)
      return {
        success: false,
        error: error.message,
        data: [],
        pagination: { page: 1, limit: 50, total: 0, totalPages: 0, hasMore: false }
      }
    }
  }

  // Quick search for autocomplete/suggestions
  async quickSearch(query, limit = 10) {
    if (!query || query.trim().length < 2) {
      return { success: true, suggestions: [] }
    }

    try {
      const sql = `
        SELECT DISTINCT 
          file_number,
          full_name,
          id_number,
          'suspect' as type
        FROM suspects 
        WHERE 
          full_name LIKE ? OR 
          file_number LIKE ? OR 
          id_number LIKE ?
        ORDER BY 
          CASE 
            WHEN full_name LIKE ? THEN 1
            WHEN file_number LIKE ? THEN 2
            ELSE 3
          END,
          full_name
        LIMIT ?
      `

      const searchTerm = `%${query.trim()}%`
      const exactTerm = `${query.trim()}%`
      
      const stmt = this.db.db.prepare(sql)
      const results = stmt.all(
        searchTerm, searchTerm, searchTerm,
        exactTerm, exactTerm,
        limit
      )

      const suggestions = results.map(row => ({
        id: `${row.type}_${row.file_number}`,
        text: `${row.full_name} (${row.file_number})`,
        type: row.type,
        data: {
          fileNumber: row.file_number,
          fullName: row.full_name,
          idNumber: row.id_number
        }
      }))

      return {
        success: true,
        suggestions: suggestions,
        query: query
      }
    } catch (error) {
      console.error('Quick search error:', error)
      return {
        success: false,
        error: error.message,
        suggestions: []
      }
    }
  }

  // Search with highlighting
  async searchWithHighlight(query, options = {}) {
    const searchResults = await this.advancedSearch({
      query,
      ...options
    })

    if (!searchResults.success || !query.trim()) {
      return searchResults
    }

    // Add highlighting to results
    const highlightedResults = searchResults.data.map(suspect => {
      const highlighted = { ...suspect }
      const searchTerms = query.trim().toLowerCase().split(/\s+/)

      // Fields to highlight
      const fieldsToHighlight = ['full_name', 'file_number', 'id_number', 'address', 'notes']

      fieldsToHighlight.forEach(field => {
        if (highlighted[field]) {
          let text = highlighted[field].toString()
          
          searchTerms.forEach(term => {
            if (term.length > 1) {
              const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi')
              text = text.replace(regex, '<mark>$1</mark>')
            }
          })
          
          highlighted[`${field}_highlighted`] = text
        }
      })

      return highlighted
    })

    return {
      ...searchResults,
      data: highlightedResults
    }
  }

  // Get search statistics
  async getSearchStats() {
    try {
      const sql = `
        SELECT 
          COUNT(*) as total_suspects,
          COUNT(CASE WHEN is_released = 1 THEN 1 END) as released_count,
          COUNT(CASE WHEN is_transferred = 1 THEN 1 END) as transferred_count,
          COUNT(CASE WHEN is_released = 0 AND is_transferred = 0 THEN 1 END) as active_count,
          COUNT(DISTINCT nationality) as nationalities_count,
          MIN(arrest_date) as earliest_arrest,
          MAX(arrest_date) as latest_arrest,
          AVG(CASE WHEN age IS NOT NULL AND age > 0 THEN age END) as average_age
        FROM suspects
      `

      const stmt = this.db.db.prepare(sql)
      const stats = stmt.get()

      // Get nationality distribution
      const nationalitySQL = `
        SELECT nationality, COUNT(*) as count
        FROM suspects 
        WHERE nationality IS NOT NULL AND nationality != ''
        GROUP BY nationality 
        ORDER BY count DESC 
        LIMIT 10
      `

      const nationalityStmt = this.db.db.prepare(nationalitySQL)
      const nationalityStats = nationalityStmt.all()

      // Get monthly arrest trends (last 12 months)
      const trendsSQL = `
        SELECT 
          strftime('%Y-%m', arrest_date) as month,
          COUNT(*) as arrests_count
        FROM suspects 
        WHERE arrest_date >= date('now', '-12 months')
        GROUP BY strftime('%Y-%m', arrest_date)
        ORDER BY month
      `

      const trendsStmt = this.db.db.prepare(trendsSQL)
      const trends = trendsStmt.all()

      return {
        success: true,
        stats: {
          ...stats,
          nationality_distribution: nationalityStats,
          monthly_trends: trends
        }
      }
    } catch (error) {
      console.error('Search stats error:', error)
      return {
        success: false,
        error: error.message,
        stats: null
      }
    }
  }

  // Fuzzy search for typo tolerance
  async fuzzySearch(query, threshold = 0.7) {
    // This is a simplified fuzzy search
    // In production, you might want to use a more sophisticated algorithm
    
    if (!query || query.trim().length < 3) {
      return { success: true, results: [] }
    }

    try {
      // Get all suspects for fuzzy matching
      const sql = `
        SELECT id, file_number, full_name, id_number
        FROM suspects
        ORDER BY created_at DESC
        LIMIT 1000
      `

      const stmt = this.db.db.prepare(sql)
      const allSuspects = stmt.all()

      const queryLower = query.trim().toLowerCase()
      const fuzzyResults = []

      allSuspects.forEach(suspect => {
        const nameScore = this.calculateSimilarity(queryLower, suspect.full_name.toLowerCase())
        const fileScore = this.calculateSimilarity(queryLower, suspect.file_number.toLowerCase())
        const idScore = suspect.id_number ? this.calculateSimilarity(queryLower, suspect.id_number.toLowerCase()) : 0

        const maxScore = Math.max(nameScore, fileScore, idScore)

        if (maxScore >= threshold) {
          fuzzyResults.push({
            ...suspect,
            similarity_score: maxScore,
            matched_field: nameScore === maxScore ? 'name' : (fileScore === maxScore ? 'file_number' : 'id_number')
          })
        }
      })

      // Sort by similarity score
      fuzzyResults.sort((a, b) => b.similarity_score - a.similarity_score)

      return {
        success: true,
        results: fuzzyResults.slice(0, 20), // Limit to top 20 results
        query: query,
        threshold: threshold
      }
    } catch (error) {
      console.error('Fuzzy search error:', error)
      return {
        success: false,
        error: error.message,
        results: []
      }
    }
  }

  // Helper method to calculate string similarity (Levenshtein distance based)
  calculateSimilarity(str1, str2) {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1

    if (longer.length === 0) return 1.0

    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  // Levenshtein distance calculation
  levenshteinDistance(str1, str2) {
    const matrix = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1, // substitution
            matrix[i][j - 1] + 1,     // insertion
            matrix[i - 1][j] + 1      // deletion
          )
        }
      }
    }

    return matrix[str2.length][str1.length]
  }

  // Helper method to escape regex special characters
  escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  // Performance monitoring
  async benchmarkSearch(query, iterations = 10) {
    const results = []

    for (let i = 0; i < iterations; i++) {
      const startTime = process.hrtime.bigint()
      
      await this.advancedSearch({ query })
      
      const endTime = process.hrtime.bigint()
      const executionTime = Number(endTime - startTime) / 1000000 // Convert to milliseconds
      
      results.push(executionTime)
    }

    const avgTime = results.reduce((sum, time) => sum + time, 0) / results.length
    const minTime = Math.min(...results)
    const maxTime = Math.max(...results)

    return {
      query,
      iterations,
      averageTime: avgTime,
      minTime,
      maxTime,
      allTimes: results
    }
  }
}

module.exports = SearchService
