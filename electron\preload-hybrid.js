const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  openFile: (options) => ipcRenderer.invoke('dialog:openFile', options),
  saveFile: (options) => ipcRenderer.invoke('dialog:saveFile', options),

  // Import operations
  importLargeDataset: (filePath, options) => ipcRenderer.invoke('import:largeDataset', filePath, options),
  getImportStatus: () => ipcRenderer.invoke('import:getStatus'),
  cancelImport: () => ipcRenderer.invoke('import:cancel'),
  onImportProgress: (callback) => ipcRenderer.on('import:progress', callback),
  removeImportProgressListener: () => ipcRenderer.removeAllListeners('import:progress'),
  
  // App info
  getVersion: () => ipcRenderer.invoke('app:getVersion'),
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('window:minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window:maximize'),
  closeWindow: () => ipcRenderer.invoke('window:close'),
  
  // Activation system
  checkActivation: () => ipcRenderer.invoke('activation:check'),
  activateApp: (code) => ipcRenderer.invoke('activation:activate', code),
  
  // Database operations
  dbQuery: (query, params) => ipcRenderer.invoke('db:query', query, params),
  dbSearch: (searchParams) => ipcRenderer.invoke('db:search', searchParams),
  dbQuickSearch: (query, limit) => ipcRenderer.invoke('db:quickSearch', query, limit),
  
  // User management
  getUsersAll: () => ipcRenderer.invoke('users:getAll'),
  authenticateUser: (username, password) => ipcRenderer.invoke('users:authenticate', username, password),
  createUser: (userData) => ipcRenderer.invoke('users:create', userData),
  updateUser: (id, userData) => ipcRenderer.invoke('users:update', id, userData),
  deleteUser: (id) => ipcRenderer.invoke('users:delete', id),
  
  // Suspects management
  getSuspectsAll: () => ipcRenderer.invoke('suspects:getAll'),
  getSuspectById: (id) => ipcRenderer.invoke('suspects:getById', id),
  createSuspect: (suspectData) => ipcRenderer.invoke('suspects:create', suspectData),
  updateSuspect: (id, suspectData) => ipcRenderer.invoke('suspects:update', id, suspectData),
  deleteSuspect: (id) => ipcRenderer.invoke('suspects:delete', id),
  
  // Search operations
  searchSuspects: (searchParams) => ipcRenderer.invoke('search:suspects', searchParams),
  getSearchSuggestions: (query) => ipcRenderer.invoke('search:suggestions', query),
  
  // Export operations
  exportToCSV: (data, filename) => ipcRenderer.invoke('export:csv', data, filename),
  exportToPDF: (data, options) => ipcRenderer.invoke('export:pdf', data, options),
  exportToExcel: (data, filename) => ipcRenderer.invoke('export:excel', data, filename),
  
  // Backup operations
  createBackup: (options) => ipcRenderer.invoke('backup:create', options),
  restoreBackup: (filePath) => ipcRenderer.invoke('backup:restore', filePath),
  getBackupHistory: () => ipcRenderer.invoke('backup:getHistory'),
  
  // Settings
  getSettings: () => ipcRenderer.invoke('settings:get'),
  updateSettings: (settings) => ipcRenderer.invoke('settings:update', settings),
  
  // Statistics
  getStatistics: () => ipcRenderer.invoke('stats:get'),
  getChartsData: (type) => ipcRenderer.invoke('stats:charts', type),
  
  // Notifications
  showNotification: (title, body, options) => {
    if (Notification.permission === 'granted') {
      new Notification(title, { body, ...options })
    }
  },
  
  // Platform info
  platform: process.platform,
  
  // Update system
  checkForUpdates: () => ipcRenderer.invoke('updater:check'),
  downloadUpdate: () => ipcRenderer.invoke('updater:download'),
  installUpdate: () => ipcRenderer.invoke('updater:install'),
  onUpdateProgress: (callback) => ipcRenderer.on('updater:progress', callback),
  removeUpdateProgressListener: () => ipcRenderer.removeAllListeners('updater:progress'),
  
  // Performance monitoring
  getPerformanceMetrics: () => ipcRenderer.invoke('performance:metrics'),
  
  // Security
  validateSession: () => ipcRenderer.invoke('security:validateSession'),
  refreshToken: () => ipcRenderer.invoke('security:refreshToken'),
  
  // Advanced search
  performAdvancedSearch: (criteria) => ipcRenderer.invoke('search:advanced', criteria),
  getFuzzySearchResults: (query, options) => ipcRenderer.invoke('search:fuzzy', query, options),
  
  // Data validation
  validateData: (data, schema) => ipcRenderer.invoke('validation:validate', data, schema),
  
  // Logging
  logActivity: (action, details) => ipcRenderer.invoke('log:activity', action, details),
  getActivityLogs: (filters) => ipcRenderer.invoke('log:getActivity', filters),
  
  // System info
  getSystemInfo: () => ipcRenderer.invoke('system:info'),
  getMemoryUsage: () => ipcRenderer.invoke('system:memory'),
  
  // Development tools
  isDevelopment: () => process.env.NODE_ENV === 'development',
  openDevTools: () => ipcRenderer.invoke('dev:openTools'),
  
  // Error handling
  reportError: (error, context) => ipcRenderer.invoke('error:report', error, context)
})

// Global error handler
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error)
  if (window.electronAPI && window.electronAPI.reportError) {
    window.electronAPI.reportError(event.error, 'global-error')
  }
})

// Unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason)
  if (window.electronAPI && window.electronAPI.reportError) {
    window.electronAPI.reportError(event.reason, 'unhandled-promise')
  }
})

console.log('Preload script loaded successfully with hybrid database support!')
