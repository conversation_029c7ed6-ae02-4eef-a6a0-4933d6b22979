<template>
  <div 
    v-if="showModal" 
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm"
    @click.self="preventClose"
  >
    <div class="neumorphic-card bg-white p-8 rounded-3xl shadow-2xl max-w-md w-full mx-4 transform transition-all duration-300">
      <!-- Header -->
      <div class="text-center mb-8">
        <div class="neumorphic-icon w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center">
          <i class="fas fa-shield-alt text-white text-3xl"></i>
        </div>
        <h2 class="text-2xl font-bold text-secondary-800 mb-2">تفعيل البرنامج</h2>
        <p class="text-secondary-600">يرجى إدخال رمز التفعيل للمتابعة</p>
      </div>

      <!-- Activation Form -->
      <form @submit.prevent="handleActivation" class="space-y-6">
        <!-- Activation Code Input -->
        <div>
          <label for="activationCode" class="block text-sm font-medium text-secondary-700 mb-2">
            رمز التفعيل
          </label>
          <div class="relative">
            <input
              id="activationCode"
              v-model="activationCode"
              type="text"
              class="neumorphic-input w-full px-4 py-3 text-center text-lg font-mono tracking-wider"
              placeholder="أدخل رمز التفعيل"
              :class="{ 'border-red-300 bg-red-50': hasError }"
              autocomplete="off"
              spellcheck="false"
              @input="clearError"
            />
            <div class="absolute inset-y-0 right-3 flex items-center">
              <i class="fas fa-key text-secondary-400"></i>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="hasError" class="text-red-600 text-sm text-center bg-red-50 p-3 rounded-xl">
          <i class="fas fa-exclamation-triangle mr-2"></i>
          {{ errorMessage }}
        </div>

        <!-- Activation Button -->
        <button
          type="submit"
          :disabled="!activationCode.trim() || isLoading"
          class="neumorphic-button w-full py-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white font-medium rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="!isLoading" class="flex items-center justify-center">
            <i class="fas fa-unlock-alt mr-2"></i>
            تفعيل البرنامج
          </span>
          <span v-else class="flex items-center justify-center">
            <i class="fas fa-spinner fa-spin mr-2"></i>
            جاري التحقق...
          </span>
        </button>
      </form>

      <!-- Footer -->
      <div class="mt-8 text-center">
        <p class="text-xs text-secondary-500">
          <i class="fas fa-info-circle mr-1"></i>
          يجب إدخال رمز التفعيل الصحيح لاستخدام البرنامج
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useActivationStore } from '@/stores/activation'

// Store
const activationStore = useActivationStore()

// Reactive data
const showModal = ref(true)
const activationCode = ref('')
const hasError = ref(false)
const errorMessage = ref('')
const isLoading = ref(false)

// Valid activation codes (stored securely)
const VALID_CODES = [
  '773$729#886',
  '777$3236#888', 
  '777$167#794'
]

// Methods
const handleActivation = async () => {
  if (!activationCode.value.trim()) {
    showError('يرجى إدخال رمز التفعيل')
    return
  }

  isLoading.value = true
  
  try {
    // Simulate verification delay for better UX
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Check if code is valid
    if (VALID_CODES.includes(activationCode.value.trim())) {
      // Save activation status
      await activationStore.activateApp(activationCode.value.trim())
      
      // Hide modal and proceed to login
      showModal.value = false
      
      // Emit success event
      emit('activated')
    } else {
      showError('رمز التفعيل غير صحيح، يرجى المحاولة مرة أخرى')
    }
  } catch (error) {
    console.error('Activation error:', error)
    showError('حدث خطأ أثناء التحقق من رمز التفعيل')
  } finally {
    isLoading.value = false
  }
}

const showError = (message: string) => {
  hasError.value = true
  errorMessage.value = message
  
  // Auto-clear error after 5 seconds
  setTimeout(() => {
    clearError()
  }, 5000)
}

const clearError = () => {
  hasError.value = false
  errorMessage.value = ''
}

const preventClose = () => {
  // Prevent modal from closing when clicking outside
  // This makes it truly modal and non-bypassable
}

// Events
const emit = defineEmits<{
  activated: []
}>()

// Lifecycle
onMounted(() => {
  // Focus on input when modal opens
  setTimeout(() => {
    const input = document.getElementById('activationCode')
    if (input) {
      input.focus()
    }
  }, 300)
})
</script>

<style scoped>
/* Additional styles for activation modal */
.neumorphic-card {
  box-shadow: 
    20px 20px 60px #d1d9e6,
    -20px -20px 60px #ffffff;
}

.neumorphic-icon {
  box-shadow: 
    inset 8px 8px 16px #d1d9e6,
    inset -8px -8px 16px #ffffff;
}

.neumorphic-input {
  box-shadow: 
    inset 8px 8px 16px #d1d9e6,
    inset -8px -8px 16px #ffffff;
  border: none;
  background: #f0f4f8;
}

.neumorphic-input:focus {
  outline: none;
  box-shadow: 
    inset 12px 12px 20px #d1d9e6,
    inset -12px -12px 20px #ffffff;
}

.neumorphic-button {
  box-shadow: 
    8px 8px 16px #d1d9e6,
    -8px -8px 16px #ffffff;
}

.neumorphic-button:hover:not(:disabled) {
  box-shadow: 
    12px 12px 20px #d1d9e6,
    -12px -12px 20px #ffffff;
  transform: translateY(-2px);
}

.neumorphic-button:active:not(:disabled) {
  box-shadow: 
    inset 8px 8px 16px #d1d9e6,
    inset -8px -8px 16px #ffffff;
  transform: translateY(0);
}

/* Animation for modal appearance */
@keyframes modalAppear {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-50px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.neumorphic-card {
  animation: modalAppear 0.3s ease-out;
}
</style>
