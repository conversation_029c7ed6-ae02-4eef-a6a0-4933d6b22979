import { computed } from 'vue'
import { useAuthStore } from '@/stores/auth'

export function usePermissions() {
  const authStore = useAuthStore()

  // الصلاحيات المحسوبة
  const permissions = computed(() => {
    const user = authStore.currentUser
    if (!user) return {}

    const isAdmin = user.role === 'admin'
    const isUser = user.role === 'user'
    const isViewer = user.role === 'viewer'

    return {
      // صلاحيات عامة
      isAuthenticated: authStore.isAuthenticated,
      isAdmin,
      isUser,
      isViewer,

      // صلاحيات بيانات المتهمين
      suspects: {
        read: isAdmin || isUser || isViewer,
        write: isAdmin || isUser,
        delete: isAdmin,
        export: isAdmin || isUser || isViewer
      },

      // صلاحيات قاعدة البيانات
      database: {
        read: isAdmin || isUser || isViewer,
        write: isAdmin || isUser,
        delete: isAdmin || isUser,
        import: isAdmin || isUser,
        export: isAdmin || isUser || isViewer,
        manage: isAdmin
      },

      // صلاحيات التقارير
      reports: {
        read: isAdmin || isUser || isViewer,
        create: isAdmin || isUser,
        export: isAdmin || isUser || isViewer
      },

      // صلاحيات الإعدادات
      settings: {
        read: isAdmin || isUser,
        write: isAdmin,
        users: isAdmin,
        system: isAdmin
      },

      // صلاحيات إدارة المستخدمين
      users: {
        read: isAdmin,
        write: isAdmin,
        create: isAdmin,
        delete: isAdmin,
        manage: isAdmin
      }
    }
  })

  // دوال مساعدة للتحقق من الصلاحيات
  function hasPermission(permission: string): boolean {
    if (!authStore.currentUser) return false
    
    // المدير لديه جميع الصلاحيات
    if (authStore.currentUser.role === 'admin') return true
    
    // التحقق من الصلاحيات المحددة
    return authStore.hasPermission(permission)
  }

  function canRead(module: string): boolean {
    const modulePermissions = permissions.value[module as keyof typeof permissions.value]
    return modulePermissions && (modulePermissions as any).read === true
  }

  function canWrite(module: string): boolean {
    const modulePermissions = permissions.value[module as keyof typeof permissions.value]
    return modulePermissions && (modulePermissions as any).write === true
  }

  function canDelete(module: string): boolean {
    const modulePermissions = permissions.value[module as keyof typeof permissions.value]
    return modulePermissions && (modulePermissions as any).delete === true
  }

  function canExport(module: string): boolean {
    const modulePermissions = permissions.value[module as keyof typeof permissions.value]
    return modulePermissions && (modulePermissions as any).export === true
  }

  function canImport(module: string): boolean {
    const modulePermissions = permissions.value[module as keyof typeof permissions.value]
    return modulePermissions && (modulePermissions as any).import === true
  }

  function canManage(module: string): boolean {
    const modulePermissions = permissions.value[module as keyof typeof permissions.value]
    return modulePermissions && (modulePermissions as any).manage === true
  }

  // دالة للتحقق من صلاحية عملية محددة
  function checkPermission(module: string, action: string): boolean {
    if (!authStore.isAuthenticated) return false
    
    const modulePermissions = permissions.value[module as keyof typeof permissions.value]
    if (!modulePermissions) return false
    
    return (modulePermissions as any)[action] === true
  }

  // دالة لإظهار رسالة عدم وجود صلاحية
  function showPermissionError(action: string = 'تنفيذ هذا الإجراء') {
    alert(`عذراً، ليس لديك صلاحية ${action}`)
  }

  // دالة للتحقق من الصلاحية مع إظهار رسالة خطأ
  function requirePermission(module: string, action: string, errorMessage?: string): boolean {
    const hasAccess = checkPermission(module, action)
    
    if (!hasAccess) {
      showPermissionError(errorMessage || `${action} في ${module}`)
    }
    
    return hasAccess
  }

  return {
    permissions,
    hasPermission,
    canRead,
    canWrite,
    canDelete,
    canExport,
    canImport,
    canManage,
    checkPermission,
    showPermissionError,
    requirePermission
  }
}
