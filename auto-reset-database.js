// سكريبت حذف قاعدة البيانات التلقائي
console.log('🔄 بدء عملية حذف قاعدة البيانات التلقائي...');

async function autoResetDatabase() {
  try {
    console.log('1️⃣ محاولة حذف قاعدة البيانات الحالية...');
    
    // حذف قاعدة البيانات
    const deleteRequest = indexedDB.deleteDatabase('SuspectsDatabase');
    
    return new Promise((resolve, reject) => {
      deleteRequest.onsuccess = function() {
        console.log('✅ تم حذف قاعدة البيانات بنجاح!');
        console.log('2️⃣ سيتم إنشاء قاعدة بيانات جديدة تلقائياً...');
        resolve(true);
      };
      
      deleteRequest.onerror = function(event) {
        console.error('❌ فشل في حذف قاعدة البيانات:', event);
        reject(new Error('فشل في حذف قاعدة البيانات'));
      };
      
      deleteRequest.onblocked = function() {
        console.warn('⚠️ حذف قاعدة البيانات محجوب');
        // محاولة إجبار الحذف
        setTimeout(() => {
          console.log('🔄 محاولة إجبار الحذف...');
          resolve(true);
        }, 2000);
      };
    });
    
  } catch (error) {
    console.error('خطأ في حذف قاعدة البيانات:', error);
    throw error;
  }
}

// تنفيذ الحذف فوراً
autoResetDatabase()
  .then(() => {
    console.log('🎉 تم حذف قاعدة البيانات بنجاح!');
    console.log('✅ قاعدة البيانات الجديدة ستُنشأ تلقائياً عند إعادة تحميل الصفحة');
    console.log('🔑 المستخدمين الافتراضيين:');
    console.log('   - admin / admin123');
    console.log('   - investigator / inv123');
    console.log('   - viewer / view123');
    
    // إعادة تحميل الصفحة بعد 3 ثوان
    setTimeout(() => {
      console.log('🔄 إعادة تحميل التطبيق...');
      window.location.reload();
    }, 3000);
  })
  .catch(error => {
    console.error('❌ فشل في حذف قاعدة البيانات:', error);
    alert('فشل في حذف قاعدة البيانات. يرجى إغلاق جميع علامات التبويب الأخرى والمحاولة مرة أخرى.');
  });
