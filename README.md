# برنامج بيانات المتهمين

برنامج متكامل لتنظيم وإدارة بيانات المتهمين مع واجهة عربية كاملة وتصميم Neumorphic عصري.

## المميزات

### 🎨 التصميم والواجهة
- واجهة عربية كاملة (RTL) مع دعم اللغة العربية
- تصميم Neumorphic عصري مع ظلال ناعمة وتدرجات لونية
- تجاوب تام مع جميع أحجام الشاشات (Responsive)
- تخصيص كامل للمظهر والألوان والخطوط

### 📊 إدارة البيانات
- حقول قابلة للتخصيص لبيانات المتهمين
- دعم أنواع مختلفة من البيانات (نص، صور، ملفات، تواريخ)
- نظام تحقق متقدم من صحة البيانات
- إمكانية إضافة حقول مخصصة حسب الحاجة

### 👥 إدارة المستخدمين
- نظام صلاحيات متقدم مع أدوار مختلفة
- تسجيل دخول آمن مع تشفير كلمات المرور
- سجل نشاط المستخدمين (Audit Log)
- إدارة المستخدمين والأدوار

### 💾 التخزين والنسخ الاحتياطي
- تخزين محلي باستخدام IndexedDB
- نسخ احتياطي تلقائي ويدوي
- استيراد وتصدير البيانات بصيغ متعددة
- عمل دون اتصال بالإنترنت (Offline-First)

### 📱 تطبيق ويب تقدمي (PWA)
- يعمل دون اتصال بالإنترنت
- قابل للتثبيت على الأجهزة المحمولة
- تحديثات تلقائية في الخلفية
- أداء سريع مع تخزين مؤقت ذكي

## التقنيات المستخدمة

- **Frontend**: Vue.js 3 + TypeScript
- **Styling**: TailwindCSS مع Neumorphic Design
- **State Management**: Pinia
- **Database**: IndexedDB (Dexie.js)
- **PWA**: Vite PWA Plugin
- **Build Tool**: Vite
- **Future**: Electron.js للتطبيق المكتبي

## متطلبات النظام

- Node.js 18+ 
- npm أو yarn
- متصفح حديث يدعم ES2020

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd البرنامج-الشامل-بيانات-وسجلات-المتهم
```

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تشغيل بيئة التطوير
```bash
npm run dev
```

### 4. بناء المشروع للإنتاج
```bash
npm run build
```

### 5. معاينة النسخة المبنية
```bash
npm run preview
```

## إعداد Electron (مستقبلاً)

### تشغيل كتطبيق سطح مكتب
```bash
npm run electron-dev
```

### بناء تطبيق سطح المكتب
```bash
npm run build-electron
```

## هيكل المشروع

```
src/
├── components/          # المكونات القابلة لإعادة الاستخدام
│   ├── common/         # المكونات المشتركة
│   └── settings/       # مكونات الإعدادات
├── views/              # صفحات التطبيق
├── stores/             # إدارة الحالة (Pinia)
├── types/              # تعريفات TypeScript
├── utils/              # الأدوات المساعدة
├── assets/             # الملفات الثابتة
└── locales/            # ملفات الترجمة
```

## الإعدادات المتاحة

### 1. تخصيص حقول البيانات
- إضافة وتعديل وحذف حقول البيانات
- تحديد نوع الحقل (نص، صورة، ملف، تاريخ، قائمة)
- تخصيص الأيقونات والتسميات
- قواعد التحقق من صحة البيانات

### 2. إدارة المستخدمين
- إضافة مستخدمين جدد
- تحديد الأدوار والصلاحيات
- تفعيل وإلغاء تفعيل الحسابات
- سجل نشاط المستخدمين

### 3. تخصيص المظهر
- تغيير الألوان الأساسية والثانوية
- اختيار نوع وحجم الخط
- تخصيص انحناء الحواف
- رفع وتخصيص الشعار

### 4. النسخ الاحتياطي
- نسخ احتياطي تلقائي ويدوي
- تصدير البيانات بصيغ مختلفة
- استيراد البيانات من ملفات خارجية
- إدارة سجل النسخ الاحتياطية

## الأمان

- تشفير كلمات المرور باستخدام bcrypt
- نظام JWT للمصادقة
- صلاحيات مرنة لكل مستخدم
- تسجيل جميع العمليات في سجل النشاط

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع جديد للميزة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push للفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى فتح issue في المستودع.

---

**ملاحظة**: هذا المشروع في مرحلة التطوير النشط. بعض الميزات قد تكون غير مكتملة.
