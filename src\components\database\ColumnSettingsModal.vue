<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-bold text-secondary-800">إعدادات العمود</h3>
        <button @click="$emit('close')" class="text-secondary-400 hover:text-secondary-600">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="space-y-6">
        <!-- Header Formatting -->
        <div class="neumorphic-card bg-secondary-50">
          <h4 class="text-md font-semibold text-secondary-800 mb-4">تنسيق العنوان</h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Header Text Color -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">لون النص</label>
              <div class="flex items-center gap-2">
                <input
                  v-model="formData.headerTextColor"
                  type="color"
                  class="w-12 h-10 rounded border border-secondary-300"
                />
                <input
                  v-model="formData.headerTextColor"
                  type="text"
                  class="neumorphic-input flex-1 text-sm"
                />
              </div>
            </div>

            <!-- Header Background Color -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">لون الخلفية</label>
              <div class="flex items-center gap-2">
                <input
                  v-model="formData.headerBackgroundColor"
                  type="color"
                  class="w-12 h-10 rounded border border-secondary-300"
                />
                <input
                  v-model="formData.headerBackgroundColor"
                  type="text"
                  class="neumorphic-input flex-1 text-sm"
                />
              </div>
            </div>

            <!-- Header Font Size -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">حجم الخط</label>
              <div class="flex items-center gap-2">
                <input
                  v-model.number="formData.headerFontSize"
                  type="range"
                  min="10"
                  max="24"
                  class="flex-1"
                />
                <span class="text-sm text-secondary-600 w-12">{{ formData.headerFontSize }}px</span>
              </div>
            </div>

            <!-- Header Font Weight -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">سُمك الخط</label>
              <select
                v-model="formData.headerFontWeight"
                class="neumorphic-select w-full text-sm"
              >
                <option value="normal">عادي</option>
                <option value="bold">غامق</option>
              </select>
            </div>

            <!-- Header Text Align -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-secondary-700 mb-2">محاذاة النص</label>
              <div class="flex items-center gap-2">
                <label class="flex items-center gap-1">
                  <input
                    v-model="formData.headerTextAlign"
                    type="radio"
                    value="right"
                    class="neumorphic-radio"
                  />
                  <span class="text-sm">يمين</span>
                </label>
                <label class="flex items-center gap-1">
                  <input
                    v-model="formData.headerTextAlign"
                    type="radio"
                    value="center"
                    class="neumorphic-radio"
                  />
                  <span class="text-sm">وسط</span>
                </label>
                <label class="flex items-center gap-1">
                  <input
                    v-model="formData.headerTextAlign"
                    type="radio"
                    value="left"
                    class="neumorphic-radio"
                  />
                  <span class="text-sm">يسار</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Cell Formatting -->
        <div class="neumorphic-card bg-secondary-50">
          <h4 class="text-md font-semibold text-secondary-800 mb-4">تنسيق الخلايا</h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Cell Text Color -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">لون النص</label>
              <div class="flex items-center gap-2">
                <input
                  v-model="formData.cellTextColor"
                  type="color"
                  class="w-12 h-10 rounded border border-secondary-300"
                />
                <input
                  v-model="formData.cellTextColor"
                  type="text"
                  class="neumorphic-input flex-1 text-sm"
                />
              </div>
            </div>

            <!-- Cell Background Color -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">لون الخلفية</label>
              <div class="flex items-center gap-2">
                <input
                  v-model="formData.cellBackgroundColor"
                  type="color"
                  class="w-12 h-10 rounded border border-secondary-300"
                />
                <input
                  v-model="formData.cellBackgroundColor"
                  type="text"
                  class="neumorphic-input flex-1 text-sm"
                />
              </div>
            </div>

            <!-- Cell Font Size -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">حجم الخط</label>
              <div class="flex items-center gap-2">
                <input
                  v-model.number="formData.cellFontSize"
                  type="range"
                  min="10"
                  max="20"
                  class="flex-1"
                />
                <span class="text-sm text-secondary-600 w-12">{{ formData.cellFontSize }}px</span>
              </div>
            </div>

            <!-- Cell Font Weight -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">سُمك الخط</label>
              <select
                v-model="formData.cellFontWeight"
                class="neumorphic-select w-full text-sm"
              >
                <option value="normal">عادي</option>
                <option value="bold">غامق</option>
              </select>
            </div>

            <!-- Cell Text Align -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">محاذاة النص</label>
              <div class="flex items-center gap-2">
                <label class="flex items-center gap-1">
                  <input
                    v-model="formData.cellTextAlign"
                    type="radio"
                    value="right"
                    class="neumorphic-radio"
                  />
                  <span class="text-sm">يمين</span>
                </label>
                <label class="flex items-center gap-1">
                  <input
                    v-model="formData.cellTextAlign"
                    type="radio"
                    value="center"
                    class="neumorphic-radio"
                  />
                  <span class="text-sm">وسط</span>
                </label>
                <label class="flex items-center gap-1">
                  <input
                    v-model="formData.cellTextAlign"
                    type="radio"
                    value="left"
                    class="neumorphic-radio"
                  />
                  <span class="text-sm">يسار</span>
                </label>
              </div>
            </div>

            <!-- Text Wrap -->
            <div>
              <label class="flex items-center gap-2">
                <input
                  v-model="formData.cellTextWrap"
                  type="checkbox"
                  class="neumorphic-checkbox"
                />
                <span class="text-sm">التفاف النص</span>
              </label>
            </div>

            <!-- Fit Content -->
            <div>
              <label class="flex items-center gap-2">
                <input
                  v-model="formData.cellFitContent"
                  type="checkbox"
                  class="neumorphic-checkbox"
                />
                <span class="text-sm">احتواء مناسب</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Column Behavior -->
        <div class="neumorphic-card bg-secondary-50">
          <h4 class="text-md font-semibold text-secondary-800 mb-4">سلوك العمود</h4>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Sortable -->
            <div>
              <label class="flex items-center gap-2">
                <input
                  v-model="formData.sortable"
                  type="checkbox"
                  class="neumorphic-checkbox"
                />
                <span class="text-sm">قابل للفرز</span>
              </label>
            </div>

            <!-- Sort Direction (if sortable) -->
            <div v-if="formData.sortable">
              <label class="block text-sm font-medium text-secondary-700 mb-2">اتجاه الفرز</label>
              <div class="flex items-center gap-4">
                <label class="flex items-center gap-1">
                  <input
                    v-model="formData.sortDirection"
                    type="radio"
                    value="asc"
                    class="neumorphic-radio"
                  />
                  <span class="text-sm">تصاعدي</span>
                </label>
                <label class="flex items-center gap-1">
                  <input
                    v-model="formData.sortDirection"
                    type="radio"
                    value="desc"
                    class="neumorphic-radio"
                  />
                  <span class="text-sm">تنازلي</span>
                </label>
                <label class="flex items-center gap-1">
                  <input
                    v-model="formData.sortDirection"
                    type="radio"
                    :value="undefined"
                    class="neumorphic-radio"
                  />
                  <span class="text-sm">بدون فرز</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Border Formatting -->
        <div class="neumorphic-card bg-secondary-50">
          <h4 class="text-md font-semibold text-secondary-800 mb-4">تنسيق الحدود</h4>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Border Color -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">لون الحدود</label>
              <div class="flex items-center gap-2">
                <input
                  v-model="formData.borderColor"
                  type="color"
                  class="w-12 h-10 rounded border border-secondary-300"
                />
                <input
                  v-model="formData.borderColor"
                  type="text"
                  class="neumorphic-input flex-1 text-sm"
                />
              </div>
            </div>

            <!-- Border Width -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">سُمك الحدود</label>
              <div class="flex items-center gap-2">
                <input
                  v-model.number="formData.borderWidth"
                  type="range"
                  min="0"
                  max="5"
                  class="flex-1"
                />
                <span class="text-sm text-secondary-600 w-12">{{ formData.borderWidth }}px</span>
              </div>
            </div>

            <!-- Border Style -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">نمط الحدود</label>
              <select
                v-model="formData.borderStyle"
                class="neumorphic-select w-full text-sm"
              >
                <option value="solid">مصمت</option>
                <option value="dashed">متقطع</option>
                <option value="dotted">منقط</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Preview -->
        <div class="neumorphic-card bg-white">
          <h4 class="text-md font-semibold text-secondary-800 mb-4">معاينة</h4>
          
          <div class="border border-secondary-200 rounded overflow-hidden">
            <table class="w-full border-collapse">
              <thead>
                <tr>
                  <th
                    :style="getPreviewHeaderStyle"
                    class="p-3 border-r border-secondary-200"
                  >
                    عنوان العمود
                  </th>
                  <th class="p-3 bg-secondary-100 text-secondary-700">عمود آخر</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td
                    :style="getPreviewCellStyle"
                    class="p-3 border-r border-secondary-200"
                  >
                    نموذج بيانات
                  </td>
                  <td class="p-3 border-r border-secondary-200">بيانات أخرى</td>
                </tr>
                <tr class="bg-secondary-25">
                  <td
                    :style="getPreviewCellStyle"
                    class="p-3 border-r border-secondary-200"
                  >
                    صف آخر
                  </td>
                  <td class="p-3 border-r border-secondary-200">المزيد من البيانات</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-end gap-3 pt-4 border-t border-secondary-200">
          <button
            @click="resetToDefaults"
            class="neumorphic-button text-warning-600 hover:text-warning-700"
          >
            <i class="fas fa-undo ml-2"></i>
            إعادة تعيين
          </button>
          <button
            @click="$emit('close')"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700"
          >
            إلغاء
          </button>
          <button
            @click="saveSettings"
            class="neumorphic-button text-success-600 hover:text-success-700"
          >
            <i class="fas fa-save ml-2"></i>
            حفظ الإعدادات
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'
import type { ColumnFormatting } from '@/types/database'

// Props
interface Props {
  columnId: string
  settings: ColumnFormatting
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  save: [settings: ColumnFormatting]
}>()

// Form data
const formData = reactive<ColumnFormatting>({
  ...props.settings
})

// Computed
const getPreviewHeaderStyle = computed(() => ({
  backgroundColor: formData.headerBackgroundColor,
  color: formData.headerTextColor,
  fontSize: `${formData.headerFontSize}px`,
  fontWeight: formData.headerFontWeight,
  textAlign: formData.headerTextAlign,
  borderColor: formData.borderColor,
  borderWidth: `${formData.borderWidth}px`,
  borderStyle: formData.borderStyle
}))

const getPreviewCellStyle = computed(() => ({
  backgroundColor: formData.cellBackgroundColor,
  color: formData.cellTextColor,
  fontSize: `${formData.cellFontSize}px`,
  fontWeight: formData.cellFontWeight,
  textAlign: formData.cellTextAlign,
  whiteSpace: formData.cellTextWrap ? 'normal' : 'nowrap',
  width: formData.cellFitContent ? 'fit-content' : 'auto',
  maxWidth: formData.cellFitContent ? 'none' : '100%',
  borderColor: formData.borderColor,
  borderWidth: `${formData.borderWidth}px`,
  borderStyle: formData.borderStyle
}))

// Methods
function resetToDefaults() {
  Object.assign(formData, {
    // Header formatting
    headerTextColor: '#334155',
    headerBackgroundColor: '#f1f5f9',
    headerFontSize: 14,
    headerFontWeight: 'bold',
    headerTextAlign: 'center',

    // Cell formatting
    cellTextColor: '#475569',
    cellBackgroundColor: 'transparent',
    cellFontSize: 14,
    cellFontWeight: 'normal',
    cellTextAlign: 'right',
    cellTextWrap: false,
    cellFitContent: false,

    // Column behavior
    sortable: true,
    sortDirection: undefined,

    // Border formatting
    borderColor: '#e2e8f0',
    borderWidth: 1,
    borderStyle: 'solid',

    // Number formatting
    numberFormat: {
      decimals: 0,
      thousandsSeparator: false
    },

    // Date formatting
    dateFormat: 'YYYY-MM-DD'
  })
}

function saveSettings() {
  emit('save', { ...formData })
}
</script>
