const SQLiteManager = require('../database/sqlite-manager')
const SearchService = require('../services/search-service')
const ImportService = require('../services/import-service')
const fs = require('fs').promises
const path = require('path')
const os = require('os')

class PerformanceTest {
  constructor() {
    this.sqliteManager = null
    this.searchService = null
    this.importService = null
    this.testResults = []
  }

  async initialize() {
    console.log('🔧 Initializing performance test environment...')
    
    // Create test database
    this.sqliteManager = new SQLiteManager()
    this.sqliteManager.dbPath = path.join(os.tmpdir(), 'test-suspects.db')
    
    await this.sqliteManager.initialize()
    
    this.searchService = new SearchService(this.sqliteManager)
    this.importService = new ImportService(this.sqliteManager)
    
    console.log('✅ Performance test environment initialized')
  }

  async runAllTests() {
    console.log('🚀 Starting comprehensive performance tests...')
    
    const startTime = Date.now()
    
    try {
      // Database performance tests
      await this.testDatabaseOperations()
      
      // Search performance tests
      await this.testSearchPerformance()
      
      // Import performance tests
      await this.testImportPerformance()
      
      // Memory usage tests
      await this.testMemoryUsage()
      
      // Concurrent operations tests
      await this.testConcurrentOperations()
      
      const totalTime = Date.now() - startTime
      
      console.log(`✅ All performance tests completed in ${totalTime}ms`)
      
      return this.generateReport()
      
    } catch (error) {
      console.error('❌ Performance tests failed:', error)
      throw error
    } finally {
      await this.cleanup()
    }
  }

  async testDatabaseOperations() {
    console.log('📊 Testing database operations...')
    
    const tests = [
      { name: 'Insert 1000 records', operation: () => this.testBulkInsert(1000) },
      { name: 'Insert 10000 records', operation: () => this.testBulkInsert(10000) },
      { name: 'Update 1000 records', operation: () => this.testBulkUpdate(1000) },
      { name: 'Delete 1000 records', operation: () => this.testBulkDelete(1000) },
      { name: 'Complex query with joins', operation: () => this.testComplexQuery() },
      { name: 'Index performance', operation: () => this.testIndexPerformance() }
    ]

    for (const test of tests) {
      const startTime = process.hrtime.bigint()
      await test.operation()
      const endTime = process.hrtime.bigint()
      const executionTime = Number(endTime - startTime) / 1000000 // Convert to milliseconds
      
      this.testResults.push({
        category: 'Database',
        test: test.name,
        executionTime,
        status: 'passed'
      })
      
      console.log(`  ✅ ${test.name}: ${executionTime.toFixed(2)}ms`)
    }
  }

  async testSearchPerformance() {
    console.log('🔍 Testing search performance...')
    
    // Ensure we have data to search
    await this.testBulkInsert(5000)
    
    const searchTests = [
      { name: 'Simple text search', query: 'محمد' },
      { name: 'Wildcard search', query: 'أحمد*' },
      { name: 'Multi-term search', query: 'محمد أحمد' },
      { name: 'Filtered search', query: 'محمد', filters: { nationality: 'سعودي' } },
      { name: 'Date range search', query: '', filters: { dateFrom: '2024-01-01', dateTo: '2024-12-31' } },
      { name: 'Complex search with pagination', query: 'أحمد', options: { limit: 50, offset: 100 } }
    ]

    for (const test of searchTests) {
      const startTime = process.hrtime.bigint()
      
      const result = await this.searchService.advancedSearch({
        query: test.query,
        filters: test.filters || {},
        pagination: { page: 1, limit: test.options?.limit || 50 },
        sorting: { field: 'created_at', order: 'DESC' }
      })
      
      const endTime = process.hrtime.bigint()
      const executionTime = Number(endTime - startTime) / 1000000
      
      this.testResults.push({
        category: 'Search',
        test: test.name,
        executionTime,
        resultsCount: result.data?.length || 0,
        status: result.success ? 'passed' : 'failed'
      })
      
      console.log(`  ✅ ${test.name}: ${executionTime.toFixed(2)}ms (${result.data?.length || 0} results)`)
    }
  }

  async testImportPerformance() {
    console.log('📥 Testing import performance...')
    
    // Create test CSV files of different sizes
    const testFiles = [
      { name: 'Small dataset (100 records)', size: 100 },
      { name: 'Medium dataset (1000 records)', size: 1000 },
      { name: 'Large dataset (10000 records)', size: 10000 }
    ]

    for (const testFile of testFiles) {
      const csvPath = await this.createTestCSV(testFile.size)
      
      const startTime = process.hrtime.bigint()
      
      const result = await this.importService.importLargeDataset(csvPath, {
        batchSize: 1000,
        validateData: true,
        skipDuplicates: false
      })
      
      const endTime = process.hrtime.bigint()
      const executionTime = Number(endTime - startTime) / 1000000
      
      this.testResults.push({
        category: 'Import',
        test: testFile.name,
        executionTime,
        recordsProcessed: result.summary?.successfulRows || 0,
        throughput: Math.round((result.summary?.successfulRows || 0) / (executionTime / 1000)),
        status: result.success ? 'passed' : 'failed'
      })
      
      console.log(`  ✅ ${testFile.name}: ${executionTime.toFixed(2)}ms (${result.summary?.successfulRows || 0} records, ${Math.round((result.summary?.successfulRows || 0) / (executionTime / 1000))} records/sec)`)
      
      // Cleanup test file
      await fs.unlink(csvPath).catch(() => {})
    }
  }

  async testMemoryUsage() {
    console.log('💾 Testing memory usage...')
    
    const initialMemory = process.memoryUsage()
    
    // Perform memory-intensive operations
    await this.testBulkInsert(20000)
    
    const afterInsertMemory = process.memoryUsage()
    
    // Perform search operations
    for (let i = 0; i < 100; i++) {
      await this.searchService.advancedSearch({
        query: `test${i}`,
        pagination: { page: 1, limit: 100 }
      })
    }
    
    const afterSearchMemory = process.memoryUsage()
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc()
    }
    
    const afterGCMemory = process.memoryUsage()
    
    this.testResults.push({
      category: 'Memory',
      test: 'Memory usage analysis',
      initialMemory: Math.round(initialMemory.heapUsed / 1024 / 1024),
      afterInsertMemory: Math.round(afterInsertMemory.heapUsed / 1024 / 1024),
      afterSearchMemory: Math.round(afterSearchMemory.heapUsed / 1024 / 1024),
      afterGCMemory: Math.round(afterGCMemory.heapUsed / 1024 / 1024),
      memoryIncrease: Math.round((afterSearchMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024),
      status: 'passed'
    })
    
    console.log(`  ✅ Memory usage: Initial ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB, Peak ${Math.round(afterSearchMemory.heapUsed / 1024 / 1024)}MB, After GC ${Math.round(afterGCMemory.heapUsed / 1024 / 1024)}MB`)
  }

  async testConcurrentOperations() {
    console.log('⚡ Testing concurrent operations...')
    
    const concurrentTests = [
      {
        name: 'Concurrent searches',
        operation: () => Promise.all(Array(10).fill().map((_, i) => 
          this.searchService.advancedSearch({ query: `test${i}` })
        ))
      },
      {
        name: 'Concurrent inserts',
        operation: () => Promise.all(Array(5).fill().map(() => 
          this.testBulkInsert(100)
        ))
      },
      {
        name: 'Mixed operations',
        operation: () => Promise.all([
          this.searchService.advancedSearch({ query: 'test' }),
          this.testBulkInsert(50),
          this.testBulkUpdate(25),
          this.searchService.quickSearch('محمد', 10)
        ])
      }
    ]

    for (const test of concurrentTests) {
      const startTime = process.hrtime.bigint()
      
      try {
        await test.operation()
        const endTime = process.hrtime.bigint()
        const executionTime = Number(endTime - startTime) / 1000000
        
        this.testResults.push({
          category: 'Concurrency',
          test: test.name,
          executionTime,
          status: 'passed'
        })
        
        console.log(`  ✅ ${test.name}: ${executionTime.toFixed(2)}ms`)
      } catch (error) {
        this.testResults.push({
          category: 'Concurrency',
          test: test.name,
          error: error.message,
          status: 'failed'
        })
        
        console.log(`  ❌ ${test.name}: Failed - ${error.message}`)
      }
    }
  }

  async testBulkInsert(count) {
    const transaction = this.sqliteManager.db.transaction((records) => {
      const stmt = this.sqliteManager.db.prepare(`
        INSERT INTO suspects (
          file_number, full_name, id_number, address, phone, age,
          nationality, profession, marital_status, arrest_date,
          notes, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      for (const record of records) {
        stmt.run(
          record.file_number, record.full_name, record.id_number,
          record.address, record.phone, record.age, record.nationality,
          record.profession, record.marital_status, record.arrest_date,
          record.notes, 1, 1
        )
      }
    })

    const records = Array(count).fill().map((_, i) => ({
      file_number: `TEST-${Date.now()}-${i}`,
      full_name: `محمد أحمد ${i}`,
      id_number: `${1000000000 + i}`,
      address: `الرياض، حي النموذجي ${i}`,
      phone: `+966${500000000 + i}`,
      age: 25 + (i % 50),
      nationality: i % 3 === 0 ? 'سعودي' : i % 3 === 1 ? 'مصري' : 'سوري',
      profession: i % 4 === 0 ? 'مهندس' : i % 4 === 1 ? 'طبيب' : i % 4 === 2 ? 'معلم' : 'محاسب',
      marital_status: i % 2 === 0 ? 'متزوج' : 'أعزب',
      arrest_date: new Date(2024, i % 12, (i % 28) + 1).toISOString().split('T')[0],
      notes: `ملاحظات تجريبية للسجل رقم ${i}`
    }))

    transaction(records)
  }

  async testBulkUpdate(count) {
    const stmt = this.sqliteManager.db.prepare(`
      UPDATE suspects 
      SET notes = ?, updated_at = CURRENT_TIMESTAMP 
      WHERE id IN (SELECT id FROM suspects LIMIT ?)
    `)
    
    stmt.run(`تم التحديث في ${new Date().toISOString()}`, count)
  }

  async testBulkDelete(count) {
    const stmt = this.sqliteManager.db.prepare(`
      DELETE FROM suspects 
      WHERE id IN (SELECT id FROM suspects ORDER BY id DESC LIMIT ?)
    `)
    
    stmt.run(count)
  }

  async testComplexQuery() {
    const sql = `
      SELECT s.*, u.name as created_by_name, COUNT(a.id) as attachments_count
      FROM suspects s
      LEFT JOIN users u ON s.created_by = u.id
      LEFT JOIN attachments a ON s.id = a.suspect_id
      WHERE s.nationality IN ('سعودي', 'مصري')
        AND s.age BETWEEN 20 AND 50
        AND s.arrest_date >= '2024-01-01'
      GROUP BY s.id
      HAVING attachments_count >= 0
      ORDER BY s.created_at DESC
      LIMIT 100
    `
    
    const stmt = this.sqliteManager.db.prepare(sql)
    stmt.all()
  }

  async testIndexPerformance() {
    // Test query performance with and without indexes
    const queries = [
      'SELECT * FROM suspects WHERE full_name LIKE ?',
      'SELECT * FROM suspects WHERE nationality = ?',
      'SELECT * FROM suspects WHERE arrest_date BETWEEN ? AND ?'
    ]
    
    for (const query of queries) {
      const stmt = this.sqliteManager.db.prepare(query)
      if (query.includes('full_name')) {
        stmt.all('%محمد%')
      } else if (query.includes('nationality')) {
        stmt.all('سعودي')
      } else {
        stmt.all('2024-01-01', '2024-12-31')
      }
    }
  }

  async createTestCSV(recordCount) {
    const csvPath = path.join(os.tmpdir(), `test-data-${recordCount}.csv`)
    
    const headers = 'file_number,full_name,id_number,address,phone,age,nationality,profession,marital_status,arrest_date,notes\n'
    
    let csvContent = headers
    
    for (let i = 0; i < recordCount; i++) {
      const record = [
        `CSV-${Date.now()}-${i}`,
        `محمد أحمد ${i}`,
        `${2000000000 + i}`,
        `الرياض، حي التجريبي ${i}`,
        `+966${600000000 + i}`,
        25 + (i % 50),
        i % 3 === 0 ? 'سعودي' : i % 3 === 1 ? 'مصري' : 'سوري',
        i % 4 === 0 ? 'مهندس' : i % 4 === 1 ? 'طبيب' : i % 4 === 2 ? 'معلم' : 'محاسب',
        i % 2 === 0 ? 'متزوج' : 'أعزب',
        new Date(2024, i % 12, (i % 28) + 1).toISOString().split('T')[0],
        `ملاحظات CSV للسجل رقم ${i}`
      ]
      
      csvContent += record.map(field => `"${field}"`).join(',') + '\n'
    }
    
    await fs.writeFile(csvPath, csvContent)
    return csvPath
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      systemInfo: {
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024) + 'GB',
        freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024) + 'GB',
        cpus: os.cpus().length
      },
      summary: {
        totalTests: this.testResults.length,
        passedTests: this.testResults.filter(t => t.status === 'passed').length,
        failedTests: this.testResults.filter(t => t.status === 'failed').length,
        averageExecutionTime: this.testResults
          .filter(t => t.executionTime)
          .reduce((sum, t) => sum + t.executionTime, 0) / 
          this.testResults.filter(t => t.executionTime).length
      },
      categories: {
        database: this.testResults.filter(t => t.category === 'Database'),
        search: this.testResults.filter(t => t.category === 'Search'),
        import: this.testResults.filter(t => t.category === 'Import'),
        memory: this.testResults.filter(t => t.category === 'Memory'),
        concurrency: this.testResults.filter(t => t.category === 'Concurrency')
      },
      recommendations: this.generateRecommendations()
    }
    
    console.log('\n📊 Performance Test Report:')
    console.log(`  Total Tests: ${report.summary.totalTests}`)
    console.log(`  Passed: ${report.summary.passedTests}`)
    console.log(`  Failed: ${report.summary.failedTests}`)
    console.log(`  Average Execution Time: ${report.summary.averageExecutionTime.toFixed(2)}ms`)
    
    return report
  }

  generateRecommendations() {
    const recommendations = []
    
    // Analyze results and generate recommendations
    const slowTests = this.testResults.filter(t => t.executionTime > 1000)
    if (slowTests.length > 0) {
      recommendations.push('Consider optimizing slow operations (>1000ms)')
    }
    
    const memoryTest = this.testResults.find(t => t.category === 'Memory')
    if (memoryTest && memoryTest.memoryIncrease > 100) {
      recommendations.push('High memory usage detected, consider memory optimization')
    }
    
    const failedTests = this.testResults.filter(t => t.status === 'failed')
    if (failedTests.length > 0) {
      recommendations.push('Address failed tests before production deployment')
    }
    
    return recommendations
  }

  async cleanup() {
    try {
      if (this.sqliteManager) {
        this.sqliteManager.close()
      }
      
      // Remove test database
      const testDbPath = path.join(os.tmpdir(), 'test-suspects.db')
      await fs.unlink(testDbPath).catch(() => {})
      
      console.log('🧹 Test cleanup completed')
    } catch (error) {
      console.error('Cleanup error:', error)
    }
  }
}

module.exports = PerformanceTest

// Run tests if this file is executed directly
if (require.main === module) {
  const test = new PerformanceTest()
  test.initialize()
    .then(() => test.runAllTests())
    .then(report => {
      console.log('\n✅ Performance tests completed successfully')
      console.log(JSON.stringify(report, null, 2))
    })
    .catch(error => {
      console.error('❌ Performance tests failed:', error)
      process.exit(1)
    })
}
