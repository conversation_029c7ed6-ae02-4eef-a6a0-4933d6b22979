<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-4xl w-full max-h-[90vh] overflow-y-auto relative">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-bold text-secondary-800">استيراد ملف Excel</h3>
        <button @click="$emit('close')" class="text-secondary-400 hover:text-secondary-600">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Step 1: File Selection -->
      <div v-if="currentStep === 1" class="space-y-6">
        <div class="text-center">
          <h4 class="text-lg font-semibold text-secondary-800 mb-4">اختيار الملف</h4>
          
          <!-- File Drop Zone -->
          <div
            @click="triggerFileInput"
            @dragover.prevent
            @drop.prevent="handleFileDrop"
            class="border-2 border-dashed border-secondary-300 rounded-neumorphic p-8 cursor-pointer hover:border-primary-400 transition-colors"
          >
            <div v-if="!selectedFile">
              <i class="fas fa-file-excel text-4xl text-success-500 mb-4"></i>
              <p class="text-lg text-secondary-700 mb-2">اسحب ملف Excel هنا أو اضغط للاختيار</p>
              <p class="text-sm text-secondary-500">يدعم ملفات .xlsx, .xls</p>
            </div>
            <div v-else class="text-success-600">
              <i class="fas fa-check-circle text-2xl mb-2"></i>
              <p class="font-semibold">{{ selectedFile.name }}</p>
              <p class="text-sm">{{ formatFileSize(selectedFile.size) }}</p>
            </div>
          </div>
          
          <input
            ref="fileInput"
            type="file"
            accept=".xlsx,.xls"
            @change="handleFileSelect"
            class="hidden"
          />
        </div>

        <!-- Import Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              خيارات الاستيراد
            </label>
            <div class="space-y-2">
              <label class="flex items-center gap-2">
                <input
                  v-model="importOptions.preserveFormatting"
                  type="checkbox"
                  class="neumorphic-checkbox"
                />
                <span class="text-sm">الحفاظ على التنسيقات</span>
              </label>
              <label class="flex items-center gap-2">
                <input
                  v-model="importOptions.detectDataTypes"
                  type="checkbox"
                  class="neumorphic-checkbox"
                />
                <span class="text-sm">اكتشاف أنواع البيانات تلقائياً</span>
              </label>
              <label class="flex items-center gap-2">
                <input
                  v-model="importOptions.createNewTab"
                  type="checkbox"
                  class="neumorphic-checkbox"
                />
                <span class="text-sm">إنشاء تبويب جديد</span>
              </label>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              إعدادات البداية
            </label>
            <div class="space-y-3">
              <div>
                <label class="text-xs text-secondary-600">الصف الأول</label>
                <input
                  v-model.number="importOptions.startRow"
                  type="number"
                  min="1"
                  class="neumorphic-input w-full text-sm"
                />
              </div>
              <div>
                <label class="text-xs text-secondary-600">العمود الأول</label>
                <input
                  v-model.number="importOptions.startColumn"
                  type="number"
                  min="1"
                  class="neumorphic-input w-full text-sm"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- Tab Selection (if not creating new) -->
        <div v-if="!importOptions.createNewTab">
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            اختيار التبويب الموجود
          </label>

          <!-- Debug info -->
          <div v-if="existingTabs.length === 0" class="p-3 bg-warning-50 border border-warning-200 rounded mb-3">
            <p class="text-sm text-warning-700">⚠️ لا توجد تبويبات متاحة. يرجى إنشاء تبويب أولاً.</p>
          </div>

          <select
            v-model="importOptions.existingTabId"
            class="neumorphic-select w-full"
            :disabled="existingTabs.length === 0"
          >
            <option value="">{{ existingTabs.length > 0 ? 'اختر تبويب' : 'لا توجد تبويبات متاحة' }}</option>
            <option
              v-for="tab in existingTabs"
              :key="tab.id"
              :value="tab.id"
            >
              {{ tab.name }}
            </option>
          </select>

          <!-- Debug info -->
          <div class="mt-2 text-xs text-secondary-500">
            التبويبات المتاحة: {{ existingTabs.length }}
            {{ existingTabs.length > 0 ? `(${existingTabs.map(t => t.name).join(', ')})` : '' }}
          </div>
        </div>

        <!-- Tab Name (if creating new) -->
        <div v-if="importOptions.createNewTab">
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            اسم التبويب الجديد
          </label>
          <input
            v-model="importOptions.tabName"
            type="text"
            class="neumorphic-input w-full"
            placeholder="سيتم استخدام اسم الملف إذا ترك فارغاً"
          />
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-end gap-3 pt-4 border-t border-secondary-200">
          <button
            @click="$emit('close')"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700"
          >
            إلغاء
          </button>
          <button
            @click="processFile"
            :disabled="!selectedFile"
            class="neumorphic-button text-primary-600 hover:text-primary-700 disabled:opacity-50"
          >
            <i class="fas fa-eye ml-2"></i>
            معاينة البيانات
          </button>
        </div>
      </div>

      <!-- Step 2: Data Preview -->
      <div v-if="currentStep === 2" class="space-y-6">
        <div class="flex items-center justify-between">
          <h4 class="text-lg font-semibold text-secondary-800">معاينة البيانات</h4>
          <div class="text-sm text-secondary-600">
            {{ previewData.rows.length }} صف، {{ previewData.columns.length }} عمود
          </div>
        </div>

        <!-- Preview Table -->
        <div class="border border-secondary-200 rounded-neumorphic overflow-hidden">
          <div class="overflow-x-auto max-h-96">
            <table class="w-full border-collapse">
              <thead class="bg-secondary-100">
                <tr>
                  <th
                    v-for="(column, index) in previewData.columns"
                    :key="index"
                    class="p-3 text-right border-r border-secondary-200 last:border-r-0"
                  >
                    <div class="space-y-1">
                      <div class="font-semibold text-secondary-800">{{ column.name }}</div>
                      <div class="text-xs text-secondary-600">{{ column.type }}</div>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(row, rowIndex) in previewData.rows.slice(0, 10)"
                  :key="rowIndex"
                  class="border-b border-secondary-200 hover:bg-secondary-50"
                >
                  <td
                    v-for="(column, colIndex) in previewData.columns"
                    :key="colIndex"
                    class="p-3 border-r border-secondary-200 last:border-r-0"
                  >
                    {{ row[column.name] || '' }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div v-if="previewData.rows.length > 10" class="p-3 bg-secondary-50 text-center text-sm text-secondary-600">
            عرض أول 10 صفوف من {{ previewData.rows.length }} صف
          </div>
        </div>

        <!-- Column Type Adjustments -->
        <div>
          <h5 class="font-semibold text-secondary-800 mb-3">تعديل أنواع الأعمدة</h5>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="(column, index) in previewData.columns"
              :key="index"
              class="p-3 bg-secondary-50 rounded-neumorphic-sm"
            >
              <label class="block text-sm font-medium text-secondary-700 mb-1">
                {{ column.name }}
              </label>
              <select
                v-model="column.type"
                class="neumorphic-select w-full text-sm"
              >
                <option value="text">نص</option>
                <option value="number">رقم</option>
                <option value="date">تاريخ</option>
                <option value="time">وقت</option>
                <option value="datetime">تاريخ ووقت</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between pt-4 border-t border-secondary-200">
          <button
            @click="currentStep = 1"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700"
          >
            <i class="fas fa-arrow-right ml-2"></i>
            السابق
          </button>
          
          <div class="flex items-center gap-3">
            <button
              @click="$emit('close')"
              class="neumorphic-button text-secondary-600 hover:text-secondary-700"
            >
              إلغاء
            </button>
            <button
              @click="importData"
              class="neumorphic-button text-success-600 hover:text-success-700"
            >
              <i class="fas fa-check ml-2"></i>
              استيراد البيانات
            </button>
          </div>
        </div>
      </div>

      <!-- Loading Overlay with Progress Bar -->
      <div v-if="isLoading" class="absolute inset-0 bg-white bg-opacity-95 flex items-center justify-center z-10">
        <div class="max-w-md w-full mx-4">
          <div class="relative">
            <!-- Close button for emergency cases -->
            <button
              v-if="progressData.status === 'error'"
              @click="isLoading = false"
              class="absolute -top-2 -right-2 w-8 h-8 bg-danger-500 text-white rounded-full hover:bg-danger-600 flex items-center justify-center z-20"
              title="إغلاق"
            >
              <i class="fas fa-times text-sm"></i>
            </button>

            <ProgressBar
              :progress="progressData.progress"
              :status="progressData.status"
              :title="progressData.title"
              :current-message="progressData.message"
              :total-items="progressData.totalItems"
              :processed-items="progressData.processedItems"
              :steps="progressData.steps"
              :show-details="true"
              :show-stripes="true"
              :details="progressData.details"
              :error-message="progressData.errorMessage"
              :success-message="progressData.successMessage"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import * as XLSX from 'xlsx'
import type { DatabaseTab, ExcelImportOptions } from '@/types/database'
import { useDatabaseStore } from '@/stores/database'
import ProgressBar from '@/components/common/ProgressBar.vue'

// Props
interface Props {
  existingTabs?: DatabaseTab[]
}

const props = withDefaults(defineProps<Props>(), {
  existingTabs: () => []
})

// Emits
const emit = defineEmits<{
  close: []
  import: [data: { file: File, tabName?: string, tabId?: string }]
}>()

// Store
const databaseStore = useDatabaseStore()

// Reactive data
const currentStep = ref(1)
const selectedFile = ref<File | null>(null)
const fileInput = ref<HTMLInputElement>()
const isLoading = ref(false)

const importOptions = reactive<ExcelImportOptions>({
  startRow: 1,
  startColumn: 1,
  preserveFormatting: true,
  detectDataTypes: true,
  createNewTab: true,
  tabName: '',
  existingTabId: ''
})

const previewData = reactive({
  columns: [] as Array<{ name: string, type: string }>,
  rows: [] as Array<Record<string, any>>
})

// Progress tracking
const progressData = reactive({
  progress: 0,
  status: 'idle' as 'idle' | 'loading' | 'success' | 'error',
  title: 'استيراد ملف Excel',
  message: 'يرجى الانتظار...',
  totalItems: 0,
  processedItems: 0,
  steps: [
    { name: 'قراءة الملف', completed: false },
    { name: 'تحليل البيانات', completed: false },
    { name: 'معالجة الصفوف', completed: false },
    { name: 'حفظ البيانات', completed: false }
  ],
  details: {
    fileSize: 0,
    processingSpeed: '',
    estimatedTime: '',
    currentOperation: ''
  },
  errorMessage: '',
  successMessage: ''
})

// Computed
const existingTabs = computed(() => props.existingTabs || databaseStore.tabs)

// Methods
function triggerFileInput() {
  fileInput.value?.click()
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    selectedFile.value = file
    if (!importOptions.tabName) {
      importOptions.tabName = file.name.replace(/\.[^/.]+$/, '')
    }
  }
}

function handleFileDrop(event: DragEvent) {
  const file = event.dataTransfer?.files[0]
  if (file && (file.name.endsWith('.xlsx') || file.name.endsWith('.xls'))) {
    selectedFile.value = file
    if (!importOptions.tabName) {
      importOptions.tabName = file.name.replace(/\.[^/.]+$/, '')
    }
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

async function processFile() {
  if (!selectedFile.value) return

  try {
    // Initialize progress
    isLoading.value = true
    progressData.status = 'loading'
    progressData.progress = 0
    progressData.title = 'استيراد ملف Excel'
    progressData.message = 'بدء معالجة الملف...'
    progressData.details.fileSize = selectedFile.value.size
    progressData.details.currentOperation = 'قراءة الملف'
    progressData.errorMessage = ''
    progressData.successMessage = ''

    // Reset steps
    progressData.steps.forEach(step => step.completed = false)

    // Step 1: Read Excel file
    progressData.progress = 10
    progressData.message = 'جاري قراءة ملف Excel...'
    progressData.details.currentOperation = 'قراءة محتوى الملف'

    const startTime = Date.now()
    const arrayBuffer = await selectedFile.value.arrayBuffer()

    progressData.progress = 25
    progressData.steps[0].completed = true

    const workbook = XLSX.read(arrayBuffer, {
      type: 'array',
      cellStyles: importOptions.preserveFormatting,
      cellDates: true
    })

    // Get first sheet or specified sheet
    const sheetName = importOptions.sheetName || workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]

    if (!worksheet) {
      throw new Error('لم يتم العثور على ورقة العمل المحددة')
    }

    // Step 2: Analyze data
    progressData.progress = 40
    progressData.message = 'جاري تحليل البيانات...'
    progressData.details.currentOperation = 'تحليل هيكل البيانات'

    // Convert to JSON with header row
    const jsonData = XLSX.utils.sheet_to_json(worksheet, {
      header: 1,
      range: importOptions.startRow - 1,
      raw: false,
      dateNF: 'yyyy-mm-dd'
    }) as any[][]

    if (jsonData.length === 0) {
      throw new Error('الملف فارغ أو لا يحتوي على بيانات')
    }

    progressData.progress = 55
    progressData.steps[1].completed = true
    progressData.totalItems = jsonData.length - 1 // Exclude header
    progressData.details.currentOperation = 'معالجة العناوين والأعمدة'

    // Extract headers (first row)
    const headers = jsonData[0] || []
    const dataRows = jsonData.slice(1)

    // Step 3: Process rows
    progressData.progress = 65
    progressData.message = 'جاري معالجة الصفوف...'
    progressData.details.currentOperation = 'تحويل البيانات'

    // Auto-detect column types
    const columns = headers.map((header: string, index: number) => {
      const columnData = dataRows.map(row => row[index]).filter(val => val != null && val !== '')
      const detectedType = detectColumnType(columnData)

      return {
        name: header || `عمود ${index + 1}`,
        type: detectedType
      }
    })

    progressData.progress = 75
    progressData.processedItems = Math.floor(dataRows.length * 0.5)

    // Convert rows to object format with progress tracking
    const rows = dataRows.map((row, rowIndex) => {
      const rowObj: Record<string, any> = {}
      headers.forEach((header: string, index: number) => {
        const value = row[index]
        rowObj[header || `عمود ${index + 1}`] = value || ''
      })

      // Update progress for large files
      if (rowIndex % 100 === 0) {
        progressData.processedItems = rowIndex
        const progressPercent = 75 + (rowIndex / dataRows.length) * 15
        progressData.progress = Math.min(progressPercent, 90)
      }

      return rowObj
    })

    progressData.progress = 90
    progressData.steps[2].completed = true
    progressData.processedItems = dataRows.length

    // Calculate processing speed
    const processingTime = (Date.now() - startTime) / 1000
    const rowsPerSecond = Math.round(dataRows.length / processingTime)
    progressData.details.processingSpeed = `${rowsPerSecond} صف/ثانية`

    // Update preview data
    previewData.columns = columns
    previewData.rows = rows

    // Step 4: Finalize
    progressData.progress = 100
    progressData.steps[3].completed = true
    progressData.status = 'success'
    progressData.message = 'تم تحليل البيانات بنجاح'
    progressData.details.currentOperation = 'اكتمل'
    progressData.successMessage = `تم معالجة ${dataRows.length} صف و ${columns.length} عمود بنجاح`

    // Add small delay to show success message
    await new Promise(resolve => setTimeout(resolve, 800))

    // Move to next step and hide loading
    currentStep.value = 2
    isLoading.value = false
  } catch (error: any) {
    console.error('Error processing file:', error)
    progressData.status = 'error'
    progressData.errorMessage = error?.message || 'حدث خطأ غير معروف'
    progressData.message = 'فشل في معالجة الملف'

    // Show error for a moment then allow retry
    setTimeout(() => {
      isLoading.value = false
    }, 2000)
  }
}

function detectColumnType(columnData: any[]): string {
  if (columnData.length === 0) return 'text'

  let dateCount = 0
  let numberCount = 0
  let timeCount = 0

  for (const value of columnData.slice(0, 10)) { // Check first 10 values
    const str = String(value).trim()

    // Check for date patterns
    if (isDateLike(str)) {
      dateCount++
    }
    // Check for time patterns
    else if (isTimeLike(str)) {
      timeCount++
    }
    // Check for numbers
    else if (!isNaN(Number(str)) && str !== '') {
      numberCount++
    }
  }

  const total = Math.min(columnData.length, 10)

  if (dateCount / total > 0.6) return 'date'
  if (timeCount / total > 0.6) return 'time'
  if (numberCount / total > 0.6) return 'number'

  return 'text'
}

function isDateLike(str: string): boolean {
  // Check various date patterns
  const datePatterns = [
    /^\d{4}-\d{2}-\d{2}$/,           // YYYY-MM-DD
    /^\d{2}\/\d{2}\/\d{4}$/,         // DD/MM/YYYY
    /^\d{2}-\d{2}-\d{4}$/,           // DD-MM-YYYY
    /^\d{1,2}\/\d{1,2}\/\d{4}$/,     // D/M/YYYY
    /^\d{4}\/\d{2}\/\d{2}$/          // YYYY/MM/DD
  ]

  return datePatterns.some(pattern => pattern.test(str)) || !isNaN(Date.parse(str))
}

function isTimeLike(str: string): boolean {
  // Check time patterns
  const timePatterns = [
    /^\d{1,2}:\d{2}$/,               // H:MM or HH:MM
    /^\d{1,2}:\d{2}:\d{2}$/,         // H:MM:SS or HH:MM:SS
    /^\d{1,2}:\d{2}\s*(AM|PM)$/i     // H:MM AM/PM
  ]

  return timePatterns.some(pattern => pattern.test(str))
}

async function importData() {
  if (!selectedFile.value || previewData.columns.length === 0) return

  // Validate import options
  if (importOptions.createNewTab) {
    if (!importOptions.tabName?.trim()) {
      alert('يرجى إدخال اسم التبويب الجديد')
      return
    }
  } else {
    if (!importOptions.existingTabId || importOptions.existingTabId.trim() === '') {
      alert('يرجى اختيار التبويب المراد الاستيراد إليه')
      return
    }
  }

  try {
    // Initialize import progress
    isLoading.value = true
    progressData.status = 'loading'
    progressData.progress = 0
    progressData.title = 'استيراد البيانات إلى قاعدة البيانات'
    progressData.message = 'بدء عملية الاستيراد...'
    progressData.totalItems = previewData.rows.length
    progressData.processedItems = 0
    progressData.details.currentOperation = 'التحقق من البيانات'
    progressData.errorMessage = ''
    progressData.successMessage = ''

    // Reset steps for import
    progressData.steps = [
      { name: 'التحقق من البيانات', completed: false },
      { name: 'إنشاء/تحديد التبويب', completed: false },
      { name: 'حفظ البيانات', completed: false },
      { name: 'تطبيق التنسيقات', completed: false }
    ]

    // Debug logging
    console.log('📊 Import Options:', {
      createNewTab: importOptions.createNewTab,
      tabName: importOptions.tabName,
      existingTabId: importOptions.existingTabId,
      existingTabIdLength: importOptions.existingTabId?.length,
      columnsCount: previewData.columns.length,
      rowsCount: previewData.rows.length
    })

    // Step 1: Additional validation
    progressData.progress = 25
    progressData.message = 'جاري التحقق من صحة البيانات...'

    console.log('📊 Available tabs:', existingTabs.value.map(t => ({ id: t.id, name: t.name })))

    if (!importOptions.createNewTab) {
      const selectedTab = existingTabs.value.find(t => t.id === importOptions.existingTabId)
      if (!selectedTab) {
        throw new Error(`التبويب المحدد غير موجود: ${importOptions.existingTabId}`)
      }
      console.log('✅ Selected tab found:', selectedTab.name)
    }

    progressData.progress = 40
    progressData.steps[0].completed = true
    progressData.details.currentOperation = 'تحضير التبويب'

    // Step 2: Prepare parameters
    progressData.progress = 50
    progressData.message = 'جاري تحضير التبويب...'

    const tabNameParam = importOptions.createNewTab ? importOptions.tabName : undefined
    const tabIdParam = !importOptions.createNewTab ? importOptions.existingTabId : undefined

    console.log('📤 Calling importProcessedData with:', {
      tabNameParam,
      tabIdParam,
      hasTabName: !!tabNameParam,
      hasTabId: !!tabIdParam
    })

    progressData.progress = 60
    progressData.steps[1].completed = true
    progressData.details.currentOperation = 'حفظ البيانات'

    // Step 3: Import data
    progressData.progress = 70
    progressData.message = 'جاري حفظ البيانات...'

    const result = await databaseStore.importProcessedData(
      {
        columns: previewData.columns,
        rows: previewData.rows
      },
      tabNameParam,
      tabIdParam
    )

    if (!result.success) {
      throw new Error(result.errors[0]?.message || 'فشل في استيراد البيانات')
    }

    progressData.progress = 90
    progressData.steps[2].completed = true
    progressData.processedItems = previewData.rows.length
    progressData.details.currentOperation = 'تطبيق التنسيقات'

    // Step 4: Finalize
    progressData.progress = 100
    progressData.steps[3].completed = true
    progressData.status = 'success'
    progressData.message = 'تم استيراد البيانات بنجاح'
    progressData.details.currentOperation = 'اكتمل'
    progressData.successMessage = `تم استيراد ${result.rowsImported} صف و ${result.columnsImported} عمود بنجاح`

    // Show success message for a moment
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Emit success and close modal
    emit('import', {
      file: selectedFile.value,
      tabName: importOptions.createNewTab ? importOptions.tabName : undefined,
      tabId: result.tabId
    })

    // Close modal after successful import
    setTimeout(() => {
      emit('close')
    }, 200)

  } catch (error: any) {
    console.error('Import error:', error)
    progressData.status = 'error'
    progressData.errorMessage = error?.message || 'حدث خطأ غير معروف أثناء الاستيراد'
    progressData.message = 'فشل في استيراد البيانات'

    // Show error for a moment then allow retry
    setTimeout(() => {
      isLoading.value = false
    }, 3000)
  } finally {
    // Always reset loading state after success or error
    if (progressData.status === 'success') {
      // Small delay to show success then close
      setTimeout(() => {
        isLoading.value = false
      }, 500)
    } else if (progressData.status !== 'error') {
      isLoading.value = false
    }
  }
}
</script>
