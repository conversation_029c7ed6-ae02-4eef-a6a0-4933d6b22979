<template>
  <div class="neumorphic-card">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-3">
        <div class="neumorphic-icon">
          <i class="fas fa-list-alt text-primary-600"></i>
        </div>
        <div>
          <h2 class="text-xl font-bold text-secondary-800">تخصيص حقول بيانات المتهمين</h2>
          <p class="text-secondary-600">إدارة وتخصيص حقول إدخال البيانات</p>
        </div>
      </div>
      <button 
        @click="showAddFieldModal = true"
        class="neumorphic-button text-primary-600 hover:text-primary-700"
        :disabled="loading"
      >
        <i class="fas fa-plus ml-2"></i>
        إضافة حقل جديد
      </button>
    </div>

    <!-- Fields List -->
    <div class="space-y-4">
      <div
        v-for="(field, index) in sortedFields"
        :key="field.id"
        class="neumorphic-card bg-white p-4 hover:shadow-neumorphic-hover transition-all duration-300"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4 flex-1">
            <!-- Drag Handle -->
            <div class="cursor-move text-secondary-400 hover:text-secondary-600">
              <i class="fas fa-grip-vertical"></i>
            </div>

            <!-- Field Icon -->
            <div class="neumorphic-icon text-sm">
              <i :class="field.icon"></i>
            </div>

            <!-- Field Info -->
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-1">
                <h3 class="font-semibold text-secondary-800">{{ field.label }}</h3>
                <span :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  getInputTypeClass(field.inputType)
                ]">
                  {{ getInputTypeLabel(field.inputType) }}
                </span>
                <span v-if="field.isRequired" class="text-danger-500 text-xs">
                  <i class="fas fa-asterisk"></i>
                  مطلوب
                </span>
              </div>
              <div class="flex items-center gap-4 text-sm text-secondary-600">
                <span>الترتيب: {{ field.order }}</span>
                <span :class="field.isVisible ? 'text-success-600' : 'text-secondary-400'">
                  <i :class="field.isVisible ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                  {{ field.isVisible ? 'مرئي' : 'مخفي' }}
                </span>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center gap-2">
            <button
              @click="toggleFieldVisibility(field)"
              :class="[
                'neumorphic-button p-2 text-sm',
                field.isVisible ? 'text-success-600' : 'text-secondary-400'
              ]"
            >
              <i :class="field.isVisible ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
            </button>
            <button
              @click="editField(field)"
              class="neumorphic-button p-2 text-sm text-primary-600"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button
              @click="confirmDeleteField(field)"
              class="neumorphic-button p-2 text-sm text-danger-600"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- Field Options (for select type) -->
        <div v-if="field.inputType === 'select' && field.options?.length" class="mt-3 pt-3 border-t border-secondary-200">
          <div class="flex flex-wrap gap-2">
            <span
              v-for="option in field.options"
              :key="option"
              class="px-2 py-1 bg-secondary-100 rounded-neumorphic-sm text-xs text-secondary-700"
            >
              {{ option }}
            </span>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!fields.length" class="text-center py-12">
        <div class="neumorphic-icon mx-auto mb-4 text-secondary-400">
          <i class="fas fa-list-alt text-2xl"></i>
        </div>
        <h3 class="text-lg font-semibold text-secondary-600 mb-2">لا توجد حقول</h3>
        <p class="text-secondary-500 mb-4">ابدأ بإضافة حقول لبيانات المتهمين</p>
        <button 
          @click="showAddFieldModal = true"
          class="neumorphic-button text-primary-600"
        >
          <i class="fas fa-plus ml-2"></i>
          إضافة حقل جديد
        </button>
      </div>
    </div>

    <!-- Add/Edit Field Modal -->
    <FieldModal
      v-if="showAddFieldModal || editingField"
      :field="editingField"
      :is-editing="!!editingField"
      @save="handleSaveField"
      @cancel="handleCancelField"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmModal
      v-if="fieldToDelete"
      title="تأكيد الحذف"
      :message="`هل أنت متأكد من حذف الحقل '${fieldToDelete.label}'؟`"
      confirm-text="حذف"
      confirm-class="text-danger-600"
      @confirm="handleDeleteField"
      @cancel="fieldToDelete = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { SuspectField } from '@/types'

// Components (will be created)
import FieldModal from './FieldModal.vue'
import ConfirmModal from '../common/ConfirmModal.vue'

// Props
interface Props {
  fields: SuspectField[]
  loading: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  addField: [field: Omit<SuspectField, 'id'>]
  updateField: [fieldId: string, updates: Partial<SuspectField>]
  deleteField: [fieldId: string]
  reorderFields: [fields: SuspectField[]]
}>()

// Reactive data
const showAddFieldModal = ref(false)
const editingField = ref<SuspectField | null>(null)
const fieldToDelete = ref<SuspectField | null>(null)

// Computed
const sortedFields = computed(() => {
  return [...props.fields].sort((a, b) => a.order - b.order)
})

// Methods
function getInputTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    text: 'نص',
    textarea: 'نص طويل',
    image: 'صورة',
    file: 'ملف',
    date: 'تاريخ',
    select: 'قائمة اختيار'
  }
  return labels[type] || type
}

function getInputTypeClass(type: string): string {
  const classes: Record<string, string> = {
    text: 'bg-blue-100 text-blue-800',
    textarea: 'bg-green-100 text-green-800',
    image: 'bg-purple-100 text-purple-800',
    file: 'bg-orange-100 text-orange-800',
    date: 'bg-pink-100 text-pink-800',
    select: 'bg-indigo-100 text-indigo-800'
  }
  return classes[type] || 'bg-secondary-100 text-secondary-800'
}

async function toggleFieldVisibility(field: SuspectField) {
  emit('updateField', field.id!, { isVisible: !field.isVisible })
}

function editField(field: SuspectField) {
  editingField.value = { ...field }
}

function confirmDeleteField(field: SuspectField) {
  fieldToDelete.value = field
}

function handleSaveField(fieldData: Omit<SuspectField, 'id'> | SuspectField) {
  if (editingField.value && 'id' in fieldData) {
    // Update existing field
    const { id, ...updates } = fieldData as SuspectField
    emit('updateField', id!, updates)
  } else {
    // Add new field
    const newField = fieldData as Omit<SuspectField, 'id'>
    // Set order to be last
    newField.order = Math.max(...props.fields.map(f => f.order), 0) + 1
    emit('addField', newField)
  }
  
  handleCancelField()
}

function handleCancelField() {
  showAddFieldModal.value = false
  editingField.value = null
}

function handleDeleteField() {
  if (fieldToDelete.value) {
    emit('deleteField', fieldToDelete.value.id!)
    fieldToDelete.value = null
  }
}
</script>
