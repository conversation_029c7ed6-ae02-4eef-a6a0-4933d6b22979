<template>
  <div class="login-container">
    <!-- خلفية متحركة -->
    <div class="animated-background">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
        <div class="shape shape-5"></div>
      </div>
    </div>

    <!-- محتوى تسجيل الدخول -->
    <div class="login-content">
      <!-- شعار النظام -->
      <div class="system-logo">
        <div class="logo-icon">
          <i class="fas fa-shield-alt"></i>
        </div>
        <h1 class="system-title">البرنامج الشامل</h1>
        <p class="system-subtitle">بيانات وسجلات المتهمين</p>
      </div>

      <!-- نموذج تسجيل الدخول -->
      <div class="login-form-container">
        <div class="login-form-header">
          <h2>تسجيل الدخول</h2>
          <p>أدخل بيانات الاعتماد للوصول إلى النظام</p>
        </div>

        <form @submit.prevent="handleLogin" class="login-form">
          <!-- حقل اسم المستخدم -->
          <div class="form-group">
            <label for="username" class="form-label">
              <i class="fas fa-user"></i>
              اسم المستخدم
            </label>
            <div class="input-container">
              <input
                id="username"
                v-model="formData.username"
                type="text"
                class="form-input"
                placeholder="أدخل اسم المستخدم"
                :disabled="authStore.isLoading"
                required
                autocomplete="username"
              />
            </div>
          </div>

          <!-- حقل كلمة المرور -->
          <div class="form-group">
            <label for="password" class="form-label">
              <i class="fas fa-lock"></i>
              كلمة المرور
            </label>
            <div class="input-container">
              <input
                id="password"
                v-model="formData.password"
                :type="showPassword ? 'text' : 'password'"
                class="form-input"
                placeholder="أدخل كلمة المرور"
                :disabled="authStore.isLoading"
                required
                autocomplete="current-password"
              />
              <button
                type="button"
                class="password-toggle"
                @click="showPassword = !showPassword"
                :disabled="authStore.isLoading"
              >
                <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
              </button>
            </div>
          </div>

          <!-- رسالة الخطأ -->
          <div v-if="authStore.loginError" class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ authStore.loginError }}
          </div>

          <!-- زر تسجيل الدخول -->
          <button
            type="submit"
            class="login-button"
            :disabled="authStore.isLoading || !formData.username || !formData.password"
          >
            <span v-if="authStore.isLoading" class="loading-spinner">
              <i class="fas fa-spinner fa-spin"></i>
            </span>
            <span v-else>
              <i class="fas fa-sign-in-alt"></i>
            </span>
            {{ authStore.isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول' }}
          </button>
        </form>

        <!-- معلومات الحسابات التجريبية -->
        <div class="demo-accounts">
          <h3>الحسابات التجريبية:</h3>
          <div class="demo-account-list">
            <div class="demo-account" @click="fillCredentials('admin', 'admin123')">
              <div class="demo-account-info">
                <strong>المدير العام</strong>
                <span>admin / admin123</span>
              </div>
              <i class="fas fa-crown text-red-500"></i>
            </div>
            <div class="demo-account" @click="fillCredentials('investigator', 'inv123')">
              <div class="demo-account-info">
                <strong>محقق رئيسي</strong>
                <span>investigator / inv123</span>
              </div>
              <i class="fas fa-user-tie text-blue-500"></i>
            </div>
            <div class="demo-account" @click="fillCredentials('viewer', 'view123')">
              <div class="demo-account-info">
                <strong>مراقب</strong>
                <span>viewer / view123</span>
              </div>
              <i class="fas fa-eye text-green-500"></i>
            </div>
          </div>
          <p class="demo-hint">انقر على أي حساب لملء البيانات تلقائياً</p>
        </div>
      </div>
    </div>

    <!-- معلومات النظام -->
    <div class="system-info">
      <p>© 2024 البرنامج الشامل لبيانات وسجلات المتهمين</p>
      <p>الإصدار 1.0.0</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// بيانات النموذج
const formData = ref({
  username: '',
  password: ''
})

const showPassword = ref(false)

// دالة تسجيل الدخول
async function handleLogin() {
  authStore.clearError()

  const success = await authStore.login(formData.value.username, formData.value.password)

  if (success) {
    // إعادة توجيه إلى الصفحة الرئيسية
    router.push('/')
  }
}

// دالة ملء بيانات الاعتماد
function fillCredentials(username: string, password: string) {
  formData.value.username = username
  formData.value.password = password
  authStore.clearError()
}

// التحقق من حالة تسجيل الدخول عند تحميل الصفحة
onMounted(() => {
  if (authStore.isAuthenticated) {
    router.push('/')
  }
})
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  padding: 2rem;
}

/* خلفية متحركة */
.animated-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 20%;
  right: 10%;
  animation-delay: 1s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  right: 20%;
  animation-delay: 3s;
}

.shape-5 {
  width: 40px;
  height: 40px;
  top: 50%;
  left: 50%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* محتوى تسجيل الدخول */
.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 450px;
  margin-bottom: 2rem;
}

/* شعار النظام */
.system-logo {
  text-align: center;
  margin-bottom: 3rem;
  color: white;
}

.logo-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 1rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  backdrop-filter: blur(10px);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.system-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.system-subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

/* نموذج تسجيل الدخول */
.login-form-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 2.5rem;
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.login-form-header h2 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
}

.login-form-header p {
  color: #718096;
  margin: 0;
}

/* حقول النموذج */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #4a5568;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.input-container {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  font-size: 1rem;
  background: #f7fafc;
  transition: all 0.3s ease;
  box-shadow: 
    inset 2px 2px 5px rgba(0, 0, 0, 0.1),
    inset -2px -2px 5px rgba(255, 255, 255, 0.8);
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 
    0 0 0 3px rgba(102, 126, 234, 0.1),
    inset 1px 1px 3px rgba(0, 0, 0, 0.1);
}

.form-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.password-toggle {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.password-toggle:hover {
  color: #4a5568;
  background: rgba(0, 0, 0, 0.05);
}

/* رسالة الخطأ */
.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #e53e3e;
  background: #fed7d7;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  border: 1px solid #feb2b2;
}

/* زر تسجيل الدخول */
.login-button {
  width: 100%;
  padding: 1rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 
    0 4px 15px rgba(102, 126, 234, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 
    0 6px 20px rgba(102, 126, 234, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.login-button:active:not(:disabled) {
  transform: translateY(0);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* معلومات الحسابات التجريبية */
.demo-accounts {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.demo-accounts h3 {
  font-size: 0.9rem;
  color: #4a5568;
  margin: 0 0 1rem 0;
  text-align: center;
}

.demo-account-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.demo-account {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background: #f7fafc;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.demo-account:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-account-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  text-align: right;
}

.demo-account-info strong {
  font-size: 0.85rem;
  color: #2d3748;
  font-weight: 600;
}

.demo-account-info span {
  font-size: 0.75rem;
  color: #718096;
  font-family: 'Courier New', monospace;
}

.demo-hint {
  font-size: 0.75rem;
  color: #a0aec0;
  text-align: center;
  margin-top: 0.75rem;
  font-style: italic;
}

/* معلومات النظام */
.system-info {
  position: relative;
  z-index: 2;
  text-align: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
}

.system-info p {
  margin: 0.25rem 0;
}

/* استجابة للشاشات الصغيرة */
@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }
  
  .login-form-container {
    padding: 2rem 1.5rem;
  }
  
  .system-title {
    font-size: 2rem;
  }
}
</style>
