// User and Authentication Types
export interface User {
  id: string
  username: string
  name: string
  email: string
  role: UserRole
  permissions: Permission[]
  createdAt: Date
  updatedAt: Date
  lastLogin?: Date
  isActive: boolean
  avatar?: string
}

export interface UserRole {
  id: string
  name: string
  displayName: string
  permissions: Permission[]
  description?: string
}

export interface Permission {
  id: string
  name: string
  resource: string
  action: 'view' | 'create' | 'update' | 'delete'
  description?: string
}

// Application Sections and Features for Permissions
export interface AppSection {
  id: string
  name: string
  displayName: string
  icon: string
  tabs: AppTab[]
}

export interface AppTab {
  id: string
  name: string
  displayName: string
  icon: string
  actions: AppAction[]
}

export interface AppAction {
  id: string
  name: string
  displayName: string
  icon: string
  type: 'button' | 'menu' | 'feature'
}

// Detailed Permission System
export interface DetailedPermission {
  sectionId: string
  tabId?: string
  actionId?: string
  allowed: boolean
}

export interface UserPermissions {
  userId: string
  sections: {
    [sectionId: string]: {
      allowed: boolean
      tabs: {
        [tabId: string]: {
          allowed: boolean
          actions: {
            [actionId: string]: boolean
          }
        }
      }
    }
  }
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

// Application Structure Definition
export const APP_SECTIONS: AppSection[] = [
  {
    id: 'suspects',
    name: 'suspects',
    displayName: 'بيانات المتهمين',
    icon: 'fas fa-users',
    tabs: [
      {
        id: 'add-suspect',
        name: 'add-suspect',
        displayName: 'إضافة بيانات متهم',
        icon: 'fas fa-user-plus',
        actions: [
          { id: 'add-suspect-btn', name: 'add-suspect', displayName: 'إضافة متهم', icon: 'fas fa-plus', type: 'button' },
          { id: 'save-suspect-btn', name: 'save-suspect', displayName: 'حفظ البيانات', icon: 'fas fa-save', type: 'button' },
          { id: 'clear-form-btn', name: 'clear-form', displayName: 'مسح النموذج', icon: 'fas fa-eraser', type: 'button' }
        ]
      },
      {
        id: 'review-status',
        name: 'review-status',
        displayName: 'مراجعة الحالة الراهنة',
        icon: 'fas fa-clipboard-check',
        actions: [
          { id: 'edit-suspect-btn', name: 'edit-suspect', displayName: 'تعديل البيانات', icon: 'fas fa-edit', type: 'button' },
          { id: 'delete-suspect-btn', name: 'delete-suspect', displayName: 'حذف المتهم', icon: 'fas fa-trash', type: 'button' },
          { id: 'export-suspect-btn', name: 'export-suspect', displayName: 'تصدير البيانات', icon: 'fas fa-download', type: 'button' }
        ]
      },
      {
        id: 'summary-charts',
        name: 'summary-charts',
        displayName: 'الرسوم البيانية التلخيصية',
        icon: 'fas fa-chart-bar',
        actions: [
          { id: 'view-charts', name: 'view-charts', displayName: 'عرض الرسوم البيانية', icon: 'fas fa-eye', type: 'feature' },
          { id: 'export-charts', name: 'export-charts', displayName: 'تصدير الرسوم البيانية', icon: 'fas fa-download', type: 'button' }
        ]
      },
      {
        id: 'card-view',
        name: 'card-view',
        displayName: 'عرض البطاقات',
        icon: 'fas fa-id-card',
        actions: [
          { id: 'view-cards', name: 'view-cards', displayName: 'عرض البطاقات', icon: 'fas fa-eye', type: 'feature' },
          { id: 'export-cards', name: 'export-cards', displayName: 'تصدير البطاقات', icon: 'fas fa-download', type: 'button' }
        ]
      }
    ]
  },
  {
    id: 'database',
    name: 'database',
    displayName: 'قاعدة البيانات',
    icon: 'fas fa-database',
    tabs: [
      {
        id: 'database-tabs',
        name: 'database-tabs',
        displayName: 'تبويبات قاعدة البيانات',
        icon: 'fas fa-table',
        actions: [
          { id: 'import-data', name: 'import-data', displayName: 'استيراد البيانات', icon: 'fas fa-upload', type: 'button' },
          { id: 'export-data', name: 'export-data', displayName: 'تصدير البيانات', icon: 'fas fa-download', type: 'button' },
          { id: 'edit-cells', name: 'edit-cells', displayName: 'تعديل محتوى الخلايا', icon: 'fas fa-edit', type: 'feature' },
          { id: 'delete-tabs', name: 'delete-tabs', displayName: 'حذف التبويبات', icon: 'fas fa-times', type: 'button' },
          { id: 'search-matching', name: 'search-matching', displayName: 'البحث والمطابقة', icon: 'fas fa-search', type: 'button' },
          { id: 'format-table', name: 'format-table', displayName: 'تنسيق الجداول', icon: 'fas fa-palette', type: 'button' },
          { id: 'highlight-content', name: 'highlight-content', displayName: 'تمييز المحتوى المهم', icon: 'fas fa-highlighter', type: 'button' }
        ]
      }
    ]
  },
  {
    id: 'settings',
    name: 'settings',
    displayName: 'الإعدادات',
    icon: 'fas fa-cog',
    tabs: [
      {
        id: 'theme-customization',
        name: 'theme-customization',
        displayName: 'تخصيص المظهر',
        icon: 'fas fa-palette',
        actions: [
          { id: 'change-theme', name: 'change-theme', displayName: 'تغيير المظهر', icon: 'fas fa-paint-brush', type: 'feature' }
        ]
      },
      {
        id: 'fields-configuration',
        name: 'fields-configuration',
        displayName: 'إعدادات الحقول',
        icon: 'fas fa-list',
        actions: [
          { id: 'add-field', name: 'add-field', displayName: 'إضافة حقل', icon: 'fas fa-plus', type: 'button' },
          { id: 'edit-field', name: 'edit-field', displayName: 'تعديل حقل', icon: 'fas fa-edit', type: 'button' },
          { id: 'delete-field', name: 'delete-field', displayName: 'حذف حقل', icon: 'fas fa-trash', type: 'button' }
        ]
      },
      {
        id: 'backup-restore',
        name: 'backup-restore',
        displayName: 'النسخ الاحتياطي والاستعادة',
        icon: 'fas fa-shield-alt',
        actions: [
          { id: 'create-backup', name: 'create-backup', displayName: 'إنشاء نسخة احتياطية', icon: 'fas fa-save', type: 'button' },
          { id: 'restore-backup', name: 'restore-backup', displayName: 'استعادة نسخة احتياطية', icon: 'fas fa-upload', type: 'button' },
          { id: 'auto-backup-settings', name: 'auto-backup-settings', displayName: 'إعدادات النسخ التلقائي', icon: 'fas fa-cog', type: 'feature' }
        ]
      },
      {
        id: 'user-management',
        name: 'user-management',
        displayName: 'إدارة المستخدمين',
        icon: 'fas fa-users-cog',
        actions: [
          { id: 'add-user', name: 'add-user', displayName: 'إضافة مستخدم', icon: 'fas fa-user-plus', type: 'button' },
          { id: 'edit-user', name: 'edit-user', displayName: 'تعديل مستخدم', icon: 'fas fa-user-edit', type: 'button' },
          { id: 'toggle-user-status', name: 'toggle-user-status', displayName: 'تفعيل/إلغاء تفعيل المستخدم', icon: 'fas fa-user-slash', type: 'button' },
          { id: 'reset-password', name: 'reset-password', displayName: 'إعادة تعيين كلمة المرور', icon: 'fas fa-key', type: 'button' },
          { id: 'manage-permissions', name: 'manage-permissions', displayName: 'إدارة الصلاحيات', icon: 'fas fa-shield-alt', type: 'feature' }
        ]
      },
      {
        id: 'developer-settings',
        name: 'developer-settings',
        displayName: 'إعدادات المطور',
        icon: 'fas fa-code',
        actions: [
          { id: 'edit-developer-info', name: 'edit-developer-info', displayName: 'تعديل معلومات المطور', icon: 'fas fa-edit', type: 'button' }
        ]
      }
    ]
  }
]

// Default Roles with Permissions
export const DEFAULT_ROLES: UserRole[] = [
  {
    id: 'admin',
    name: 'admin',
    displayName: 'مدير عام',
    description: 'صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات',
    permissions: [
      { id: '1', name: 'all', resource: '*', action: 'view' },
      { id: '2', name: 'all', resource: '*', action: 'create' },
      { id: '3', name: 'all', resource: '*', action: 'update' },
      { id: '4', name: 'all', resource: '*', action: 'delete' }
    ]
  },
  {
    id: 'supervisor',
    name: 'supervisor',
    displayName: 'مشرف',
    description: 'صلاحيات إشرافية مع إمكانية إدارة البيانات والتقارير',
    permissions: [
      { id: '5', name: 'suspects', resource: 'suspects', action: 'view' },
      { id: '6', name: 'suspects', resource: 'suspects', action: 'create' },
      { id: '7', name: 'suspects', resource: 'suspects', action: 'update' },
      { id: '8', name: 'database', resource: 'database', action: 'view' },
      { id: '9', name: 'database', resource: 'database', action: 'create' },
      { id: '10', name: 'database', resource: 'database', action: 'update' }
    ]
  },
  {
    id: 'officer',
    name: 'officer',
    displayName: 'ضابط',
    description: 'صلاحيات تشغيلية لإدارة بيانات المتهمين وإنشاء التقارير',
    permissions: [
      { id: '11', name: 'suspects', resource: 'suspects', action: 'view' },
      { id: '12', name: 'suspects', resource: 'suspects', action: 'create' },
      { id: '13', name: 'suspects', resource: 'suspects', action: 'update' },
      { id: '14', name: 'database', resource: 'database', action: 'view' },
      { id: '15', name: 'database', resource: 'database', action: 'create' }
    ]
  },
  {
    id: 'investigator',
    name: 'investigator',
    displayName: 'محقق رئيسي',
    description: 'صلاحيات التحقيق مع إمكانية الوصول للبيانات والتحليل',
    permissions: [
      { id: '16', name: 'suspects', resource: 'suspects', action: 'view' },
      { id: '17', name: 'suspects', resource: 'suspects', action: 'update' },
      { id: '18', name: 'database', resource: 'database', action: 'view' }
    ]
  },
  {
    id: 'viewer',
    name: 'viewer',
    displayName: 'مراقب',
    description: 'صلاحيات القراءة فقط لعرض البيانات والتقارير',
    permissions: [
      { id: '19', name: 'suspects', resource: 'suspects', action: 'view' },
      { id: '20', name: 'database', resource: 'database', action: 'view' }
    ]
  }
]

// Suspect Data Types
export interface SuspectField {
  id: string
  label: string
  icon: string
  inputType: 'text' | 'image' | 'file' | 'date' | 'select' | 'textarea'
  isRequired: boolean
  isVisible: boolean
  order: number
  options?: string[] // For select type
  validation?: FieldValidation
}

export interface FieldValidation {
  minLength?: number
  maxLength?: number
  pattern?: string
  fileTypes?: string[]
  maxFileSize?: number
}

export interface SuspectData {
  id: string
  fields: Record<string, any> & {
    fileNumber?: string
    fullName?: string
    idNumber?: string
    address?: string
    phone?: string
    age?: string
    nationality?: string
    profession?: string
    maritalStatus?: string
    seizures?: string
    arrestDate?: Date
    notes?: string
    isReleased?: boolean
    releaseDate?: Date
    isTransferred?: boolean
    transferDate?: Date
  }
  attachments: SuspectAttachment[]
  createdAt: Date
  updatedAt: Date
  createdBy: string
  updatedBy: string
}

export interface SuspectAttachment {
  id: string
  fieldId: string
  fileName: string
  originalName: string
  fileType: string
  fileSize: number
  filePath: string
  uploadedAt: Date
}

// Settings Types
export interface DeveloperInfo {
  title: string
  developerName: string
  supervisorName: string
  icon?: string // Font Awesome icon class
  customIcon?: string // Custom image as data URL
}

export interface AppSettings {
  theme: ThemeSettings
  organization: OrganizationSettings
  fields: SuspectField[]
  users: User[]
  backup: BackupSettings
  developerInfo: DeveloperInfo
}

export interface ThemeSettings {
  primaryColor: string
  secondaryColor: string
  backgroundColor: string
  fontFamily: string
  fontSize: number
  borderRadius: number
  customLogo?: string
  logoPosition: 'top-left' | 'top-center' | 'top-right'
  logoSize: number
}

export interface OrganizationSettings {
  name: string
  nameEn?: string
  address: string
  phone: string
  email: string
  website?: string
  logo?: string
  stamp?: string
  signature?: string
}

export interface BackupSettings {
  autoBackup: boolean
  backupInterval: number // in hours
  maxBackups: number
  lastBackup?: Date
  // New fields for enhanced backup system
  encryptBackups?: boolean
  backupPassword?: string
  compressionEnabled?: boolean
  backupLocation?: string
}

// New interfaces for backup management
export interface BackupRecord {
  id: string
  type: 'auto' | 'manual'
  date: Date
  size: number
  filePath?: string
  fileName: string
  checksum?: string
  status: 'completed' | 'failed' | 'in_progress'
  encrypted?: boolean
  compressed?: boolean
  description?: string
}

export interface BackupProgress {
  stage: 'preparing' | 'exporting' | 'compressing' | 'encrypting' | 'saving' | 'completed' | 'error'
  progress: number // 0-100
  message: string
  details?: string
}

// Report Types
export interface ReportTemplate {
  id: string
  name: string
  type: 'pdf' | 'excel' | 'csv'
  fields: string[]
  filters: ReportFilter[]
  formatting: ReportFormatting
  createdAt: Date
  updatedAt: Date
}

export interface ReportFilter {
  field: string
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'between' | 'in'
  value: any
}

export interface ReportFormatting {
  header: {
    showLogo: boolean
    showOrganization: boolean
    showDate: boolean
    customText?: string
  }
  footer: {
    showPageNumbers: boolean
    showSignature: boolean
    customText?: string
  }
  table: {
    showBorders: boolean
    alternateRowColors: boolean
    fontSize: number
    headerBackgroundColor: string
    headerTextColor: string
  }
}

// Database Types
export interface DatabaseConfig {
  name: string
  version: number
  stores: DatabaseStore[]
}

export interface DatabaseStore {
  name: string
  keyPath: string
  autoIncrement: boolean
  indexes: DatabaseIndex[]
}

export interface DatabaseIndex {
  name: string
  keyPath: string | string[]
  unique: boolean
}

// Import/Export Types
export interface ImportResult {
  success: boolean
  totalRows: number
  successfulRows: number
  failedRows: number
  errors: ImportError[]
}

export interface ImportError {
  row: number
  field: string
  message: string
  value: any
}

export interface ExportOptions {
  format: 'pdf' | 'excel' | 'csv'
  fields: string[]
  filters: ReportFilter[]
  includeAttachments: boolean
  template?: ReportTemplate
}

// UI Component Types
export interface TabItem {
  id: string
  label: string
  icon: string
  component: any
  isActive: boolean
}

export interface MenuItem {
  id: string
  label: string
  icon: string
  route?: string
  children?: MenuItem[]
  permission?: string
}

export interface NotificationItem {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  actions?: NotificationAction[]
  createdAt: Date
}

export interface NotificationAction {
  label: string
  action: () => void
  style?: 'primary' | 'secondary' | 'danger'
}

// Form Types
export interface FormField {
  name: string
  label: string
  type: string
  value: any
  rules: ValidationRule[]
  props?: Record<string, any>
}

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
  message: string
}

export interface FormState {
  fields: Record<string, FormField>
  errors: Record<string, string>
  isValid: boolean
  isSubmitting: boolean
}
