import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useActivationStore } from '@/stores/activation'
import Login from '@/views/Login.vue'
import Suspects from '@/views/Suspects.vue'
import Database from '@/views/Database.vue'
import Settings from '@/views/Settings.vue'
import Reports from '@/views/Reports.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: Login,
      meta: {
        title: 'تسجيل الدخول',
        requiresAuth: false
      }
    },
    {
      path: '/',
      redirect: '/suspects'
    },
    {
      path: '/suspects',
      name: 'suspects',
      component: Suspects,
      meta: {
        title: 'بيانات المتهمين',
        icon: 'fas fa-users',
        requiresAuth: true
      }
    },
    {
      path: '/database',
      name: 'database',
      component: Database,
      meta: {
        title: 'قاعدة بيانات وسجلات المتهمين',
        icon: 'fas fa-database',
        requiresAuth: true
      }
    },
    {
      path: '/reports',
      name: 'reports',
      component: Reports,
      meta: {
        title: 'التقارير',
        icon: 'fas fa-chart-bar',
        requiresAuth: true
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: Settings,
      meta: {
        title: 'الإعدادات',
        icon: 'fas fa-cog',
        requiresAuth: true
      }
    }
  ]
})

// حماية الصفحات - التحقق من التفعيل وتسجيل الدخول
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const activationStore = useActivationStore()

  // التحقق من حالة التفعيل أولاً
  if (activationStore.needsActivation) {
    // إذا لم يكن التطبيق مفعلاً، السماح بالوصول للصفحة الرئيسية فقط لعرض شاشة التفعيل
    if (to.path !== '/') {
      next('/')
      return
    }
  }

  // إذا كان التطبيق مفعلاً، التحقق من تسجيل الدخول
  if (!activationStore.needsActivation) {
    // إذا كانت الصفحة تتطلب تسجيل دخول
    if (to.meta.requiresAuth !== false) {
      if (!authStore.isAuthenticated) {
        // إعادة توجيه إلى صفحة تسجيل الدخول
        next('/login')
        return
      }
    }

    // إذا كان المستخدم مسجل دخول ويحاول الوصول لصفحة تسجيل الدخول
    if (to.name === 'login' && authStore.isAuthenticated) {
      next('/')
      return
    }
  }

  next()
})

export default router
