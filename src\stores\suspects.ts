import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { SuspectData, SuspectField } from '@/types'
import { db } from '@/utils/database'
import { useSettingsStore } from './settings'

export const useSuspectsStore = defineStore('suspects', () => {
  // State
  const suspects = ref<SuspectData[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const searchQuery = ref('')
  const statusFilter = ref<'all' | 'detained' | 'released' | 'transferred'>('all')

  // Getters
  const filteredSuspects = computed(() => {
    let filtered = suspects.value

    // Apply search filter
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase().trim()
      filtered = filtered.filter(suspect => {
        return Object.values(suspect.fields).some(value => {
          if (typeof value === 'string') {
            return value.toLowerCase().includes(query)
          }
          return false
        })
      })
    }

    // Apply status filter
    if (statusFilter.value !== 'all') {
      filtered = filtered.filter(suspect => {
        const status = getStatus(suspect)
        return status === statusFilter.value
      })
    }

    return filtered
  })

  const totalSuspects = computed(() => suspects.value.length)
  const detainedCount = computed(() => suspects.value.filter(s => getStatus(s) === 'detained').length)
  const releasedCount = computed(() => suspects.value.filter(s => getStatus(s) === 'released').length)
  const transferredCount = computed(() => suspects.value.filter(s => getStatus(s) === 'transferred').length)

  const statistics = computed(() => ({
    total: totalSuspects.value,
    detained: detainedCount.value,
    released: releasedCount.value,
    transferred: transferredCount.value,
    detainedPercentage: totalSuspects.value > 0 ? Math.round((detainedCount.value / totalSuspects.value) * 100) : 0,
    releasedPercentage: totalSuspects.value > 0 ? Math.round((releasedCount.value / totalSuspects.value) * 100) : 0,
    transferredPercentage: totalSuspects.value > 0 ? Math.round((transferredCount.value / totalSuspects.value) * 100) : 0
  }))

  // Actions
  async function loadSuspects() {
    try {
      isLoading.value = true
      error.value = null
      suspects.value = await db.suspects.orderBy('createdAt').reverse().toArray()
    } catch (err) {
      error.value = 'فشل في تحميل بيانات المتهمين'
      console.error('Error loading suspects:', err)
    } finally {
      isLoading.value = false
    }
  }

  async function addSuspect(suspectData: Omit<SuspectData, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      isLoading.value = true
      error.value = null

      // Generate file number
      const fileNumber = await generateFileNumber()
      
      const newSuspect: Omit<SuspectData, 'id'> = {
        ...suspectData,
        fields: {
          ...suspectData.fields,
          fileNumber: fileNumber
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const id = await db.suspects.add(newSuspect)
      const suspect = { ...newSuspect, id: id.toString() }
      suspects.value.unshift(suspect)

      // Log action
      await db.logAction(suspectData.createdBy, 'create', 'suspect', { suspectId: id })
      
      return suspect
    } catch (err) {
      error.value = 'فشل في إضافة المتهم'
      console.error('Error adding suspect:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function updateSuspect(id: string, updates: Partial<SuspectData>) {
    try {
      isLoading.value = true
      error.value = null

      const updatedData = {
        ...updates,
        updatedAt: new Date()
      }

      await db.suspects.update(id, updatedData)
      
      const index = suspects.value.findIndex(s => s.id === id)
      if (index !== -1) {
        suspects.value[index] = { ...suspects.value[index], ...updatedData }
      }

      // Log action
      await db.logAction(updates.updatedBy || 'system', 'update', 'suspect', { suspectId: id })
    } catch (err) {
      error.value = 'فشل في تحديث بيانات المتهم'
      console.error('Error updating suspect:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function deleteSuspect(id: string, userId: string) {
    try {
      isLoading.value = true
      error.value = null

      await db.suspects.delete(id)
      suspects.value = suspects.value.filter(s => s.id !== id)

      // Log action
      await db.logAction(userId, 'delete', 'suspect', { suspectId: id })
    } catch (err) {
      error.value = 'فشل في حذف المتهم'
      console.error('Error deleting suspect:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  async function updateSuspectStatus(id: string, status: 'released' | 'transferred', date?: Date, userId?: string) {
    try {
      const suspect = suspects.value.find(s => s.id === id)
      if (!suspect) return

      const updates: any = {
        updatedBy: userId || 'system'
      }

      if (status === 'released') {
        updates.fields = {
          ...suspect.fields,
          isReleased: true,
          releaseDate: date || new Date(),
          isTransferred: false,
          transferDate: null
        }
      } else if (status === 'transferred') {
        updates.fields = {
          ...suspect.fields,
          isTransferred: true,
          transferDate: date || new Date(),
          isReleased: false,
          releaseDate: null
        }
      }

      await updateSuspect(id, updates)
    } catch (err) {
      console.error('Error updating suspect status:', err)
      throw err
    }
  }

  function setSearchQuery(query: string) {
    searchQuery.value = query
  }

  function setStatusFilter(filter: typeof statusFilter.value) {
    statusFilter.value = filter
  }

  // Helper functions
  function getStatus(suspect: SuspectData): 'detained' | 'released' | 'transferred' {
    if (suspect.fields.isReleased) return 'released'
    if (suspect.fields.isTransferred) return 'transferred'
    return 'detained'
  }

  function getDaysDetained(suspect: SuspectData): number {
    const arrestDate = suspect.fields.arrestDate ? new Date(suspect.fields.arrestDate) : suspect.createdAt
    const endDate = suspect.fields.isReleased 
      ? new Date(suspect.fields.releaseDate) 
      : suspect.fields.isTransferred 
        ? new Date(suspect.fields.transferDate)
        : new Date()
    
    const diffTime = Math.abs(endDate.getTime() - arrestDate.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  async function generateFileNumber(): Promise<string> {
    const count = await db.suspects.count()
    return String(count + 1).padStart(2, '0')
  }

  function getSuspectById(id: string): SuspectData | undefined {
    return suspects.value.find(s => s.id === id)
  }

  // Initialize
  loadSuspects()

  return {
    // State
    suspects,
    isLoading,
    error,
    searchQuery,
    statusFilter,
    
    // Getters
    filteredSuspects,
    totalSuspects,
    detainedCount,
    releasedCount,
    transferredCount,
    statistics,
    
    // Actions
    loadSuspects,
    addSuspect,
    updateSuspect,
    deleteSuspect,
    updateSuspectStatus,
    setSearchQuery,
    setStatusFilter,
    getStatus,
    getDaysDetained,
    getSuspectById
  }
})
