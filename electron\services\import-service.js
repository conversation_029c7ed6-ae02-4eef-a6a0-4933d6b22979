const fs = require('fs')
const path = require('path')
const csv = require('csv-parser')
const { Transform } = require('stream')
const { pipeline } = require('stream/promises')

class ImportService {
  constructor(sqliteManager) {
    this.db = sqliteManager
    this.isImporting = false
    this.currentImport = null
  }

  // Main import method with streaming support
  async importLargeDataset(filePath, options = {}) {
    if (this.isImporting) {
      throw new Error('Import already in progress')
    }

    const {
      batchSize = 1000,
      progressCallback = null,
      validateData = true,
      skipDuplicates = true,
      encoding = 'utf8'
    } = options

    this.isImporting = true
    this.currentImport = {
      filePath,
      startTime: Date.now(),
      totalRows: 0,
      processedRows: 0,
      successfulRows: 0,
      failedRows: 0,
      errors: [],
      status: 'preparing'
    }

    try {
      // Get file size for progress calculation
      const fileStats = fs.statSync(filePath)
      const fileSize = fileStats.size

      console.log(`📁 Starting import of file: ${filePath}`)
      console.log(`📊 File size: ${(fileSize / 1024 / 1024).toFixed(2)} MB`)

      // Update status
      this.currentImport.status = 'counting'
      if (progressCallback) {
        progressCallback({
          stage: 'counting',
          progress: 0,
          message: 'جاري حساب عدد السجلات...',
          details: `حجم الملف: ${(fileSize / 1024 / 1024).toFixed(2)} ميجابايت`
        })
      }

      // Count total rows first for accurate progress
      const totalRows = await this.countRows(filePath)
      this.currentImport.totalRows = totalRows

      console.log(`📊 Total rows to process: ${totalRows}`)

      // Update status
      this.currentImport.status = 'processing'
      if (progressCallback) {
        progressCallback({
          stage: 'processing',
          progress: 0,
          message: 'جاري معالجة البيانات...',
          details: `إجمالي السجلات: ${totalRows.toLocaleString()}`
        })
      }

      // Process data in batches using streams
      await this.processDataStream(filePath, {
        batchSize,
        totalRows,
        validateData,
        skipDuplicates,
        encoding,
        progressCallback
      })

      // Final status
      this.currentImport.status = 'completed'
      const duration = Date.now() - this.currentImport.startTime

      const result = {
        success: true,
        summary: {
          totalRows: this.currentImport.totalRows,
          processedRows: this.currentImport.processedRows,
          successfulRows: this.currentImport.successfulRows,
          failedRows: this.currentImport.failedRows,
          duration: duration,
          throughput: Math.round(this.currentImport.successfulRows / (duration / 1000))
        },
        errors: this.currentImport.errors
      }

      console.log('✅ Import completed successfully')
      console.log(`📊 Summary:`, result.summary)

      if (progressCallback) {
        progressCallback({
          stage: 'completed',
          progress: 100,
          message: 'تم الانتهاء من الاستيراد بنجاح',
          details: `تم استيراد ${result.summary.successfulRows.toLocaleString()} سجل في ${(duration / 1000).toFixed(1)} ثانية`
        })
      }

      return result

    } catch (error) {
      console.error('❌ Import failed:', error)
      
      this.currentImport.status = 'failed'
      
      if (progressCallback) {
        progressCallback({
          stage: 'error',
          progress: 0,
          message: 'فشل في عملية الاستيراد',
          details: error.message
        })
      }

      return {
        success: false,
        error: error.message,
        summary: {
          totalRows: this.currentImport.totalRows,
          processedRows: this.currentImport.processedRows,
          successfulRows: this.currentImport.successfulRows,
          failedRows: this.currentImport.failedRows,
          duration: Date.now() - this.currentImport.startTime
        },
        errors: this.currentImport.errors
      }
    } finally {
      this.isImporting = false
      this.currentImport = null
    }
  }

  // Count rows in file efficiently
  async countRows(filePath) {
    return new Promise((resolve, reject) => {
      let rowCount = 0
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', () => {
          rowCount++
        })
        .on('end', () => {
          resolve(rowCount)
        })
        .on('error', reject)
    })
  }

  // Process data using streams for memory efficiency
  async processDataStream(filePath, options) {
    const {
      batchSize,
      totalRows,
      validateData,
      skipDuplicates,
      encoding,
      progressCallback
    } = options

    let batch = []
    let rowIndex = 0

    // Create transform stream for batch processing
    const batchProcessor = new Transform({
      objectMode: true,
      transform: async (chunk, encoding, callback) => {
        try {
          rowIndex++
          this.currentImport.processedRows = rowIndex

          // Validate data if required
          if (validateData) {
            const validation = this.validateRow(chunk, rowIndex)
            if (!validation.isValid) {
              this.currentImport.failedRows++
              this.currentImport.errors.push({
                row: rowIndex,
                errors: validation.errors,
                data: chunk
              })
              callback()
              return
            }
          }

          // Transform data to database format
          const transformedData = this.transformRowData(chunk)
          
          // Check for duplicates if required
          if (skipDuplicates && await this.isDuplicate(transformedData)) {
            this.currentImport.failedRows++
            this.currentImport.errors.push({
              row: rowIndex,
              errors: ['Duplicate record'],
              data: chunk
            })
            callback()
            return
          }

          batch.push(transformedData)

          // Process batch when it reaches the specified size
          if (batch.length >= batchSize) {
            await this.processBatch(batch)
            batch = []
          }

          // Update progress
          if (progressCallback && rowIndex % 100 === 0) {
            const progress = Math.round((rowIndex / totalRows) * 100)
            progressCallback({
              stage: 'processing',
              progress,
              message: 'جاري معالجة البيانات...',
              details: `تم معالجة ${rowIndex.toLocaleString()} من ${totalRows.toLocaleString()} سجل`
            })
          }

          callback()
        } catch (error) {
          console.error('Error processing row:', error)
          this.currentImport.failedRows++
          this.currentImport.errors.push({
            row: rowIndex,
            errors: [error.message],
            data: chunk
          })
          callback()
        }
      },
      flush: async (callback) => {
        // Process remaining batch
        if (batch.length > 0) {
          await this.processBatch(batch)
        }
        callback()
      }
    })

    // Create pipeline
    await pipeline(
      fs.createReadStream(filePath, { encoding }),
      csv(),
      batchProcessor
    )
  }

  // Process a batch of records
  async processBatch(batch) {
    if (batch.length === 0) return

    try {
      // Use transaction for better performance
      const transaction = this.db.db.transaction((records) => {
        const stmt = this.db.db.prepare(`
          INSERT INTO suspects (
            file_number, full_name, id_number, address, phone, age,
            nationality, profession, marital_status, seizures, arrest_date,
            notes, created_by, updated_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `)

        for (const record of records) {
          try {
            stmt.run(
              record.file_number, record.full_name, record.id_number,
              record.address, record.phone, record.age, record.nationality,
              record.profession, record.marital_status, record.seizures,
              record.arrest_date, record.notes, record.created_by, record.updated_by
            )
            this.currentImport.successfulRows++
          } catch (error) {
            console.error('Error inserting record:', error)
            this.currentImport.failedRows++
            this.currentImport.errors.push({
              row: this.currentImport.processedRows,
              errors: [error.message],
              data: record
            })
          }
        }
      })

      transaction(batch)

    } catch (error) {
      console.error('Batch processing error:', error)
      this.currentImport.failedRows += batch.length
      this.currentImport.errors.push({
        row: this.currentImport.processedRows,
        errors: [error.message],
        data: 'Batch processing failed'
      })
    }
  }

  // Validate row data
  validateRow(row, rowIndex) {
    const errors = []

    // Required fields validation
    if (!row.file_number || row.file_number.trim() === '') {
      errors.push('رقم الملف مطلوب')
    }

    if (!row.full_name || row.full_name.trim() === '') {
      errors.push('الاسم الكامل مطلوب')
    }

    // Data type validation
    if (row.age && isNaN(parseInt(row.age))) {
      errors.push('العمر يجب أن يكون رقماً')
    }

    // Date validation
    if (row.arrest_date && !this.isValidDate(row.arrest_date)) {
      errors.push('تاريخ القبض غير صحيح')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  // Transform CSV row to database format
  transformRowData(row) {
    return {
      file_number: row.file_number?.trim() || '',
      full_name: row.full_name?.trim() || '',
      id_number: row.id_number?.trim() || null,
      address: row.address?.trim() || null,
      phone: row.phone?.trim() || null,
      age: row.age ? parseInt(row.age) : null,
      nationality: row.nationality?.trim() || null,
      profession: row.profession?.trim() || null,
      marital_status: row.marital_status?.trim() || null,
      seizures: row.seizures?.trim() || null,
      arrest_date: row.arrest_date ? this.parseDate(row.arrest_date) : null,
      notes: row.notes?.trim() || null,
      created_by: 1, // Default admin user
      updated_by: 1
    }
  }

  // Check for duplicate records
  async isDuplicate(data) {
    try {
      const stmt = this.db.db.prepare('SELECT id FROM suspects WHERE file_number = ? OR (full_name = ? AND id_number = ?)')
      const existing = stmt.get(data.file_number, data.full_name, data.id_number)
      return !!existing
    } catch (error) {
      console.error('Duplicate check error:', error)
      return false
    }
  }

  // Utility methods
  isValidDate(dateString) {
    const date = new Date(dateString)
    return date instanceof Date && !isNaN(date)
  }

  parseDate(dateString) {
    try {
      const date = new Date(dateString)
      return date.toISOString().split('T')[0] // Return YYYY-MM-DD format
    } catch (error) {
      return null
    }
  }

  // Get current import status
  getImportStatus() {
    return this.currentImport
  }

  // Cancel current import
  cancelImport() {
    if (this.isImporting && this.currentImport) {
      this.currentImport.status = 'cancelled'
      this.isImporting = false
      return true
    }
    return false
  }
}

module.exports = ImportService
