// Migration: Initial Schema
// Description: Create initial database schema with all required tables
// Created: 2024-01-01T00:00:00.000Z

module.exports = {
  // Run the migration
  up: (db) => {
    console.log('🔄 Applying initial schema migration...')

    // Enable foreign keys
    db.exec('PRAGMA foreign_keys = ON')

    // Users table
    db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT NOT NULL,
        permissions TEXT, -- JSON string
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
      )
    `)

    // Suspects table
    db.exec(`
      CREATE TABLE IF NOT EXISTS suspects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_number TEXT UNIQUE NOT NULL,
        full_name TEXT NOT NULL,
        id_number TEXT,
        address TEXT,
        phone TEXT,
        age INTEGER,
        nationality TEXT,
        profession TEXT,
        marital_status TEXT,
        seizures TEXT,
        arrest_date DATE,
        notes TEXT,
        is_released BOOLEAN DEFAULT 0,
        release_date DATE,
        is_transferred BOOLEAN DEFAULT 0,
        transfer_date DATE,
        created_by INTEGER,
        updated_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id),
        FOREIGN KEY (updated_by) REFERENCES users(id)
      )
    `)

    // Attachments table
    db.exec(`
      CREATE TABLE IF NOT EXISTS attachments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suspect_id INTEGER NOT NULL,
        field_name TEXT NOT NULL,
        file_name TEXT NOT NULL,
        original_name TEXT NOT NULL,
        file_type TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        file_path TEXT NOT NULL,
        uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (suspect_id) REFERENCES suspects(id) ON DELETE CASCADE
      )
    `)

    // Fields configuration table
    db.exec(`
      CREATE TABLE IF NOT EXISTS fields (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        label TEXT NOT NULL,
        icon TEXT,
        input_type TEXT NOT NULL,
        is_required BOOLEAN DEFAULT 0,
        is_visible BOOLEAN DEFAULT 1,
        field_order INTEGER DEFAULT 0,
        options TEXT, -- JSON string for select options
        validation TEXT, -- JSON string for validation rules
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Settings table
    db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL, -- JSON string
        category TEXT DEFAULT 'general',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Audit log table
    db.exec(`
      CREATE TABLE IF NOT EXISTS audit_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action TEXT NOT NULL,
        resource TEXT NOT NULL,
        resource_id INTEGER,
        details TEXT, -- JSON string
        ip_address TEXT,
        user_agent TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )
    `)

    // Backup records table
    db.exec(`
      CREATE TABLE IF NOT EXISTS backup_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_path TEXT,
        backup_type TEXT DEFAULT 'manual', -- 'auto' or 'manual'
        file_size INTEGER,
        checksum TEXT,
        status TEXT DEFAULT 'completed', -- 'completed', 'failed', 'in_progress'
        encrypted BOOLEAN DEFAULT 0,
        compressed BOOLEAN DEFAULT 0,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create indexes for performance
    console.log('🔄 Creating database indexes...')

    // Users indexes
    db.exec('CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)')

    // Suspects indexes
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_file_number ON suspects(file_number)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_full_name ON suspects(full_name)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_id_number ON suspects(id_number)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_nationality ON suspects(nationality)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_arrest_date ON suspects(arrest_date)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_is_released ON suspects(is_released)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_is_transferred ON suspects(is_transferred)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_created_at ON suspects(created_at)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_updated_at ON suspects(updated_at)')
    
    // Composite indexes for common queries
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_status ON suspects(is_released, is_transferred)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_suspects_name_file ON suspects(full_name, file_number)')

    // Attachments indexes
    db.exec('CREATE INDEX IF NOT EXISTS idx_attachments_suspect_id ON attachments(suspect_id)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_attachments_field_name ON attachments(field_name)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_attachments_file_type ON attachments(file_type)')

    // Audit log indexes
    db.exec('CREATE INDEX IF NOT EXISTS idx_audit_user_id ON audit_log(user_id)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_audit_resource ON audit_log(resource)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)')

    // Backup records indexes
    db.exec('CREATE INDEX IF NOT EXISTS idx_backup_type ON backup_records(backup_type)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_backup_status ON backup_records(status)')
    db.exec('CREATE INDEX IF NOT EXISTS idx_backup_created_at ON backup_records(created_at)')

    // Create Full-Text Search virtual table
    console.log('🔄 Creating Full-Text Search table...')
    
    db.exec(`
      CREATE VIRTUAL TABLE IF NOT EXISTS suspects_fts USING fts5(
        file_number,
        full_name,
        id_number,
        address,
        phone,
        nationality,
        profession,
        notes,
        content='suspects',
        content_rowid='id'
      )
    `)

    // Create FTS triggers
    console.log('🔄 Creating FTS triggers...')

    // Insert trigger
    db.exec(`
      CREATE TRIGGER IF NOT EXISTS suspects_fts_insert AFTER INSERT ON suspects BEGIN
        INSERT INTO suspects_fts(rowid, file_number, full_name, id_number, address, phone, nationality, profession, notes)
        VALUES (new.id, new.file_number, new.full_name, new.id_number, new.address, new.phone, new.nationality, new.profession, new.notes);
      END
    `)

    // Update trigger
    db.exec(`
      CREATE TRIGGER IF NOT EXISTS suspects_fts_update AFTER UPDATE ON suspects BEGIN
        UPDATE suspects_fts SET 
          file_number = new.file_number,
          full_name = new.full_name,
          id_number = new.id_number,
          address = new.address,
          phone = new.phone,
          nationality = new.nationality,
          profession = new.profession,
          notes = new.notes
        WHERE rowid = new.id;
      END
    `)

    // Delete trigger
    db.exec(`
      CREATE TRIGGER IF NOT EXISTS suspects_fts_delete AFTER DELETE ON suspects BEGIN
        DELETE FROM suspects_fts WHERE rowid = old.id;
      END
    `)

    // Insert default admin user
    console.log('🔄 Creating default admin user...')
    
    const bcrypt = require('bcryptjs')
    const defaultPassword = bcrypt.hashSync('admin123', 10)
    
    db.exec(`
      INSERT OR IGNORE INTO users (
        username, name, email, password_hash, role, permissions, is_active
      ) VALUES (
        'admin',
        'المدير العام',
        '<EMAIL>',
        '${defaultPassword}',
        'admin',
        '[]',
        1
      )
    `)

    // Insert default settings
    console.log('🔄 Creating default settings...')
    
    const defaultSettings = {
      theme: {
        primaryColor: '#3b82f6',
        secondaryColor: '#64748b',
        backgroundColor: '#f8fafc',
        fontFamily: 'Cairo',
        fontSize: 14,
        borderRadius: 20
      },
      organization: {
        name: 'اسم المؤسسة',
        address: 'عنوان المؤسسة',
        phone: '+966xxxxxxxxx',
        email: '<EMAIL>'
      },
      backup: {
        autoBackup: false,
        backupInterval: 24,
        maxBackups: 10
      }
    }

    db.exec(`
      INSERT OR IGNORE INTO settings (key, value, category) VALUES 
      ('theme', '${JSON.stringify(defaultSettings.theme)}', 'appearance'),
      ('organization', '${JSON.stringify(defaultSettings.organization)}', 'general'),
      ('backup', '${JSON.stringify(defaultSettings.backup)}', 'system')
    `)

    console.log('✅ Initial schema migration completed')
  },

  // Rollback the migration
  down: (db) => {
    console.log('🔄 Rolling back initial schema migration...')

    // Drop triggers first
    db.exec('DROP TRIGGER IF EXISTS suspects_fts_insert')
    db.exec('DROP TRIGGER IF EXISTS suspects_fts_update')
    db.exec('DROP TRIGGER IF EXISTS suspects_fts_delete')

    // Drop FTS table
    db.exec('DROP TABLE IF EXISTS suspects_fts')

    // Drop indexes
    db.exec('DROP INDEX IF EXISTS idx_users_username')
    db.exec('DROP INDEX IF EXISTS idx_users_email')
    db.exec('DROP INDEX IF EXISTS idx_users_role')
    db.exec('DROP INDEX IF EXISTS idx_users_active')
    db.exec('DROP INDEX IF EXISTS idx_suspects_file_number')
    db.exec('DROP INDEX IF EXISTS idx_suspects_full_name')
    db.exec('DROP INDEX IF EXISTS idx_suspects_id_number')
    db.exec('DROP INDEX IF EXISTS idx_suspects_nationality')
    db.exec('DROP INDEX IF EXISTS idx_suspects_arrest_date')
    db.exec('DROP INDEX IF EXISTS idx_suspects_is_released')
    db.exec('DROP INDEX IF EXISTS idx_suspects_is_transferred')
    db.exec('DROP INDEX IF EXISTS idx_suspects_created_at')
    db.exec('DROP INDEX IF EXISTS idx_suspects_updated_at')
    db.exec('DROP INDEX IF EXISTS idx_suspects_status')
    db.exec('DROP INDEX IF EXISTS idx_suspects_name_file')
    db.exec('DROP INDEX IF EXISTS idx_attachments_suspect_id')
    db.exec('DROP INDEX IF EXISTS idx_attachments_field_name')
    db.exec('DROP INDEX IF EXISTS idx_attachments_file_type')
    db.exec('DROP INDEX IF EXISTS idx_audit_user_id')
    db.exec('DROP INDEX IF EXISTS idx_audit_action')
    db.exec('DROP INDEX IF EXISTS idx_audit_resource')
    db.exec('DROP INDEX IF EXISTS idx_audit_timestamp')
    db.exec('DROP INDEX IF EXISTS idx_backup_type')
    db.exec('DROP INDEX IF EXISTS idx_backup_status')
    db.exec('DROP INDEX IF EXISTS idx_backup_created_at')

    // Drop tables in reverse order (respecting foreign keys)
    db.exec('DROP TABLE IF EXISTS backup_records')
    db.exec('DROP TABLE IF EXISTS audit_log')
    db.exec('DROP TABLE IF EXISTS settings')
    db.exec('DROP TABLE IF EXISTS fields')
    db.exec('DROP TABLE IF EXISTS attachments')
    db.exec('DROP TABLE IF EXISTS suspects')
    db.exec('DROP TABLE IF EXISTS users')

    console.log('✅ Initial schema migration rolled back')
  }
}
