<template>
  <div class="neumorphic-card">
    <!-- Main Toolbar -->
    <div class="flex flex-wrap items-center gap-4 p-4">
      <!-- Advanced Search Toggle -->
      <div class="flex items-center gap-3">
        <button
          @click="showAdvancedSearch = !showAdvancedSearch"
          :class="[
            'neumorphic-button p-2',
            showAdvancedSearch ? 'text-primary-600' : 'text-secondary-600'
          ]"
          title="البحث المتقدم"
        >
          <i class="fas fa-filter"></i>
          <span class="hidden sm:inline ml-1">البحث المتقدم</span>
        </button>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center gap-2">
        <!-- Column Visibility -->
        <div class="relative">
          <button
            @click="showColumnMenu = !showColumnMenu"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700"
            title="إظهار/إخفاء الأعمدة"
          >
            <i class="fas fa-columns ml-1"></i>
            الأعمدة
          </button>
          
          <!-- Column Menu -->
          <div v-if="showColumnMenu" class="absolute top-full left-0 mt-2 w-64 neumorphic-card bg-white shadow-neumorphic z-10">
            <div class="p-3">
              <h4 class="font-semibold text-secondary-800 mb-3">إدارة الأعمدة</h4>
              <div class="space-y-2 max-h-64 overflow-y-auto">
                <label
                  v-for="column in tabData.columns"
                  :key="column.id"
                  class="flex items-center gap-2 cursor-pointer hover:bg-secondary-50 p-2 rounded"
                >
                  <input
                    type="checkbox"
                    :checked="column.isVisible"
                    @change="toggleColumn(column.id)"
                    class="neumorphic-checkbox"
                  />
                  <span class="text-sm">{{ column.name }}</span>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- Format Actions -->
        <button
          @click="saveFormat"
          class="neumorphic-button text-success-600 hover:text-success-700"
          title="حفظ التنسيقات"
        >
          <i class="fas fa-save ml-1"></i>
          حفظ
        </button>

        <button
          @click="applyFormat"
          class="neumorphic-button text-warning-600 hover:text-warning-700"
          title="تطبيق التنسيقات المحفوظة"
        >
          <i class="fas fa-magic ml-1"></i>
          تطبيق
        </button>

        <!-- Group Headers Settings -->
        <button
          @click="showGroupHeaderSettings"
          class="neumorphic-button text-info-600 hover:text-info-700"
          title="إعدادات عناوين المجموعات"
        >
          <i class="fas fa-layer-group ml-1"></i>
          المجموعات
        </button>



        <!-- Add Row Button -->
        <button
          v-if="permissions.database?.write"
          @click="addRow"
          class="neumorphic-button text-success-600 hover:text-success-700"
        >
          <i class="fas fa-plus ml-1"></i>
          إضافة صف
        </button>

        <!-- Delete Selected Rows Button -->
        <button
          v-if="permissions.database?.delete"
          @click="deleteSelectedRows"
          :disabled="!hasSelectedRows"
          class="neumorphic-button text-danger-600 hover:text-danger-700 disabled:opacity-50 disabled:cursor-not-allowed"
          title="حذف الصفوف المحددة"
        >
          <i class="fas fa-trash ml-1"></i>
          حذف المحدد
        </button>

        <!-- Add Column Button -->
        <button
          v-if="permissions.database?.write"
          @click="addColumn"
          class="neumorphic-button text-info-600 hover:text-info-700"
          title="إضافة عمود جديد"
        >
          <i class="fas fa-columns ml-1"></i>
          إضافة عمود
        </button>
      </div>
    </div>

    <!-- Advanced Search Panel -->
    <div v-if="showAdvancedSearch" class="border-t border-secondary-200 p-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Search Options -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">خيارات البحث</label>
          <div class="space-y-2">
            <label class="flex items-center gap-2">
              <input
                v-model="searchOptions.caseSensitive"
                type="checkbox"
                class="neumorphic-checkbox"
              />
              <span class="text-sm">حساس لحالة الأحرف</span>
            </label>
            <label class="flex items-center gap-2">
              <input
                v-model="searchOptions.wholeWord"
                type="checkbox"
                class="neumorphic-checkbox"
              />
              <span class="text-sm">كلمة كاملة فقط</span>
            </label>
            <label class="flex items-center gap-2">
              <input
                v-model="searchOptions.regex"
                type="checkbox"
                class="neumorphic-checkbox"
              />
              <span class="text-sm">تعبير نمطي</span>
            </label>
          </div>
        </div>

        <!-- Column Filters -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">البحث في الأعمدة</label>
          <div class="space-y-2 max-h-32 overflow-y-auto">
            <label
              v-for="column in visibleColumns"
              :key="column.id"
              class="flex items-center gap-2"
            >
              <input
                v-model="searchOptions.columns"
                :value="column.id"
                type="checkbox"
                class="neumorphic-checkbox"
              />
              <span class="text-sm">{{ column.name }}</span>
            </label>
          </div>
        </div>

        <!-- Quick Filters -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">فلاتر سريعة</label>
          <div class="space-y-2">
            <select
              v-model="quickFilter.column"
              class="neumorphic-select w-full text-sm"
            >
              <option value="">اختر عمود</option>
              <option
                v-for="column in visibleColumns"
                :key="column.id"
                :value="column.id"
              >
                {{ column.name }}
              </option>
            </select>
            <select
              v-model="quickFilter.operator"
              class="neumorphic-select w-full text-sm"
            >
              <option value="contains">يحتوي على</option>
              <option value="equals">يساوي</option>
              <option value="startsWith">يبدأ بـ</option>
              <option value="endsWith">ينتهي بـ</option>
              <option value="greaterThan">أكبر من</option>
              <option value="lessThan">أصغر من</option>
            </select>
            <input
              v-model="quickFilter.value"
              type="text"
              placeholder="القيمة"
              class="neumorphic-input w-full text-sm"
            />
          </div>
        </div>
      </div>

      <!-- Filter Actions -->
      <div class="flex items-center justify-end gap-3 mt-4 pt-4 border-t border-secondary-200">
        <button
          @click="clearFilters"
          class="neumorphic-button text-secondary-600 hover:text-secondary-700 text-sm"
        >
          <i class="fas fa-times ml-1"></i>
          مسح الفلاتر
        </button>
        <button
          @click="applyFilters"
          class="neumorphic-button text-primary-600 hover:text-primary-700 text-sm"
        >
          <i class="fas fa-check ml-1"></i>
          تطبيق الفلاتر
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { DatabaseTab, DatabaseColumn, SearchOptions, FilterOptions } from '@/types/database'
import { usePermissions } from '@/composables/usePermissions'

// Props
interface Props {
  tabData: DatabaseTab
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  search: [query: string]
  filter: [filters: Record<string, any>]
  toggleColumn: [columnId: string]
  saveFormat: []
  applyFormat: []
  groupHeaderSettings: []
  addRow: []
  deleteSelectedRows: []
  addColumn: [columnName: string]
}>()

// Permissions
const { permissions } = usePermissions()

// Reactive data
const showAdvancedSearch = ref(false)
const showColumnMenu = ref(false)

const searchOptions = ref<SearchOptions>({
  query: '',
  columns: [],
  caseSensitive: false,
  wholeWord: false,
  regex: false
})

const quickFilter = ref<FilterOptions>({
  column: '',
  operator: 'contains',
  value: ''
})

// Computed
const visibleColumns = computed(() =>
  props.tabData.columns.filter(col => col.isVisible)
)

const hasSelectedRows = computed(() =>
  props.tabData.rows.some(row => row.isSelected && !row.isGroupHeader)
)

// Methods

function toggleColumn(columnId: string) {
  emit('toggleColumn', columnId)
}

function saveFormat() {
  emit('saveFormat')
  showColumnMenu.value = false
}

function applyFormat() {
  emit('applyFormat')
  showColumnMenu.value = false
}



function addRow() {
  emit('addRow')
}

function showGroupHeaderSettings() {
  emit('groupHeaderSettings')
}

function deleteSelectedRows() {
  const selectedCount = props.tabData.rows.filter(row => row.isSelected && !row.isGroupHeader).length
  if (selectedCount > 0 && confirm(`هل أنت متأكد من حذف ${selectedCount} صف؟`)) {
    emit('deleteSelectedRows')
  }
}

function addColumn() {
  const columnName = prompt('اسم العمود الجديد:')
  if (columnName && columnName.trim()) {
    emit('addColumn', columnName.trim())
  }
}

function applyFilters() {
  const filters: Record<string, any> = {
    search: searchOptions.value,
    quickFilter: quickFilter.value.column ? quickFilter.value : null
  }
  emit('filter', filters)
}

function clearFilters() {
  searchOptions.value = {
    query: '',
    columns: [],
    caseSensitive: false,
    wholeWord: false,
    regex: false
  }
  quickFilter.value = {
    column: '',
    operator: 'contains',
    value: ''
  }
  emit('search', '')
  emit('filter', {})
}



// Close menus when clicking outside
function handleClickOutside(event: Event) {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showColumnMenu.value = false
  }
}

// Watch for changes
watch(() => props.tabData.columns, () => {
  // Initialize search columns if empty
  if (searchOptions.value.columns.length === 0) {
    searchOptions.value.columns = visibleColumns.value.map(col => col.id)
  }
}, { immediate: true })

// Add click outside listener
document.addEventListener('click', handleClickOutside)
</script>
