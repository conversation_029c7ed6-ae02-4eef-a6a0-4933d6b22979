<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>👥 إنشاء المستخدمين - نسخة مبسطة</title>
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      direction: rtl;
      text-align: center;
      margin: 50px;
      background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
      min-height: 100vh;
      color: white;
    }

    .container {
      max-width: 700px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.1);
      padding: 40px;
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .title {
      font-size: 36px;
      font-weight: bold;
      margin-bottom: 20px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .create-button {
      background: linear-gradient(135deg, #3b82f6, #2563eb);
      color: white;
      padding: 20px 40px;
      border: none;
      border-radius: 25px;
      font-weight: bold;
      font-size: 18px;
      cursor: pointer;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
      transition: all 0.3s;
      margin: 20px;
    }

    .create-button:hover {
      transform: scale(1.1);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4);
    }

    .log {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      font-family: monospace;
      text-align: right;
      max-height: 300px;
      overflow-y: auto;
      display: none;
    }

    .success {
      background: linear-gradient(135deg, #10b981, #059669);
      border-radius: 15px;
      padding: 25px;
      margin: 25px 0;
      font-size: 18px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">👥 إنشاء المستخدمين - نسخة مبسطة</h1>
    
    <div style="background: rgba(255, 255, 255, 0.2); border-radius: 15px; padding: 25px; margin: 25px 0;">
      <h3>🔧 حل مشكلة قاعدة البيانات</h3>
      <p>هذه النسخة ستقوم بإنشاء قاعدة البيانات من الصفر مع جميع الجداول والمستخدمين.</p>
    </div>

    <button class="create-button" onclick="createEverything()">
      🚀 إنشاء قاعدة البيانات والمستخدمين
    </button>

    <div class="log" id="log"></div>

    <div class="success" id="success">
      <h3>✅ تم بنجاح!</h3>
      <p>تم إنشاء قاعدة البيانات والمستخدمين الافتراضيين بنجاح.</p>
      <p><strong>يمكنك الآن تسجيل الدخول بـ:</strong></p>
      <ul style="text-align: right; margin: 15px 0;">
        <li><strong>admin / admin123</strong></li>
        <li><strong>investigator / inv123</strong></li>
        <li><strong>viewer / view123</strong></li>
      </ul>
      <a href="http://localhost:5175" style="color: white; text-decoration: underline; font-weight: bold; font-size: 18px;">🚀 العودة للتطبيق</a>
    </div>
  </div>

  <script>
    function log(message) {
      const logDiv = document.getElementById('log');
      logDiv.style.display = 'block';
      logDiv.innerHTML += message + '<br>';
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    async function createEverything() {
      document.querySelector('.create-button').style.display = 'none';

      try {
        log('🔄 بدء إنشاء قاعدة البيانات من الصفر...');

        // حذف قاعدة البيانات الحالية أولاً
        await new Promise((resolve, reject) => {
          const deleteRequest = indexedDB.deleteDatabase('SuspectsDatabase');
          deleteRequest.onsuccess = () => {
            log('✅ تم حذف قاعدة البيانات القديمة');
            resolve();
          };
          deleteRequest.onerror = () => resolve(); // متابعة حتى لو فشل الحذف
          deleteRequest.onblocked = () => {
            log('⚠️ قاعدة البيانات محجوبة - سيتم المتابعة');
            setTimeout(resolve, 1000);
          };
        });

        // إنشاء قاعدة البيانات الجديدة
        const db = await new Promise((resolve, reject) => {
          log('🔄 إنشاء قاعدة البيانات الجديدة...');
          const dbRequest = indexedDB.open('SuspectsDatabase', 3);
          
          dbRequest.onupgradeneeded = function(event) {
            const db = event.target.result;
            log('🔄 إنشاء الجداول...');
            
            // إنشاء جدول المستخدمين
            const userStore = db.createObjectStore('users', { keyPath: 'id', autoIncrement: true });
            userStore.createIndex('username', 'username', { unique: true });
            userStore.createIndex('email', 'email', { unique: true });
            log('✅ تم إنشاء جدول المستخدمين');
            
            // إنشاء باقي الجداول
            const tables = [
              'suspects', 'fields', 'attachments', 'settings', 
              'reports', 'notifications', 'auditLog', 'backupRecords'
            ];
            
            tables.forEach(tableName => {
              db.createObjectStore(tableName, { keyPath: 'id', autoIncrement: true });
              log(`✅ تم إنشاء جدول ${tableName}`);
            });
          };
          
          dbRequest.onsuccess = function(event) {
            log('✅ تم إنشاء قاعدة البيانات بنجاح');
            resolve(event.target.result);
          };
          
          dbRequest.onerror = function(event) {
            log('❌ فشل في إنشاء قاعدة البيانات: ' + event.target.error);
            reject(event.target.error);
          };
        });

        // إنشاء المستخدمين الافتراضيين
        log('🔄 إنشاء المستخدمين الافتراضيين...');
        
        const transaction = db.transaction(['users'], 'readwrite');
        const userStore = transaction.objectStore('users');

        const defaultUsers = [
          {
            username: 'admin',
            name: 'المدير العام',
            email: '<EMAIL>',
            role: {
              id: 'admin',
              name: 'admin',
              displayName: 'مدير عام',
              description: 'صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات',
              permissions: []
            },
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            username: 'investigator',
            name: 'محقق رئيسي',
            email: '<EMAIL>',
            role: {
              id: 'investigator',
              name: 'investigator',
              displayName: 'محقق رئيسي',
              description: 'صلاحيات التحقيق مع إمكانية الوصول للبيانات والتحليل',
              permissions: []
            },
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            username: 'viewer',
            name: 'مراقب',
            email: '<EMAIL>',
            role: {
              id: 'viewer',
              name: 'viewer',
              displayName: 'مراقب',
              description: 'صلاحيات القراءة فقط لعرض البيانات والتقارير',
              permissions: []
            },
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ];

        // إضافة المستخدمين وحفظ كلمات المرور
        const passwords = {
          'admin': 'admin123',
          'investigator': 'inv123',
          'viewer': 'view123'
        };

        for (const user of defaultUsers) {
          const addRequest = userStore.add(user);
          const userId = await new Promise((resolve, reject) => {
            addRequest.onsuccess = () => resolve(addRequest.result);
            addRequest.onerror = () => reject(addRequest.error);
          });

          log(`✅ تم إنشاء المستخدم: ${user.username} (ID: ${userId})`);

          // حفظ كلمة المرور مشفرة
          const password = passwords[user.username];
          const salt = 'suspects_app_salt';
          const combined = password + salt;
          let hashedPassword = btoa(combined);
          
          // إضافة طبقة تشفير إضافية
          for (let i = 0; i < 3; i++) {
            hashedPassword = btoa(hashedPassword + salt);
          }
          
          localStorage.setItem(`user_password_${userId}`, hashedPassword);
          log(`🔑 تم حفظ كلمة المرور للمستخدم: ${user.username}`);
        }

        db.close();
        log('🎉 تم الانتهاء من إنشاء كل شيء بنجاح!');
        
        // إظهار رسالة النجاح
        document.getElementById('success').style.display = 'block';

      } catch (error) {
        log('❌ خطأ عام: ' + error.message);
        console.error('Error:', error);
        document.querySelector('.create-button').style.display = 'block';
      }
    }

    console.log('👥 صفحة إنشاء المستخدمين المبسطة جاهزة');
  </script>
</body>
</html>
