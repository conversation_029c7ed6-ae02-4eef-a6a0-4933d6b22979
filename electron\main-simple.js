const { app, BrowserWindow, Menu, ipc<PERSON>ain, dialog } = require('electron')
const path = require('path')
const isDev = process.env.NODE_ENV === 'development'

// Keep a global reference of the window object
let mainWindow

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    icon: path.join(__dirname, '../public/icon.ico'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload-simple.js'),
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false
    },
    show: false,
    titleBarStyle: 'default',
    frame: true,
    backgroundColor: '#f8fafc'
  })

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:5176')
    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
    
    if (isDev) {
      mainWindow.webContents.openDevTools()
    }
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url)
    return { action: 'deny' }
  })

  // Security: Prevent new window creation
  mainWindow.webContents.on('new-window', (event, url) => {
    event.preventDefault()
    require('electron').shell.openExternal(url)
  })
}

// App event handlers
app.whenReady().then(() => {
  createWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Security: Prevent navigation to external websites
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    if (parsedUrl.origin !== 'http://localhost:5176' && parsedUrl.origin !== 'http://localhost:5175') {
      event.preventDefault()
    }
  })
})

// IPC Handlers for basic functionality
ipcMain.handle('app:getVersion', () => {
  return app.getVersion()
})

ipcMain.handle('dialog:openFile', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options)
  return result
})

ipcMain.handle('dialog:saveFile', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options)
  return result
})

// Basic activation system (simplified)
let isActivated = false

ipcMain.handle('activation:check', () => {
  return isActivated
})

ipcMain.handle('activation:activate', (event, code) => {
  const validCodes = ['773$729#886', '777$3236#888', '777$167#794']
  if (validCodes.includes(code)) {
    isActivated = true
    return { success: true }
  }
  return { success: false, error: 'كود التفعيل غير صحيح' }
})

// Mock database operations for testing
ipcMain.handle('db:query', async (event, query, params) => {
  // Return mock data for testing
  return {
    success: true,
    data: [],
    message: 'تم تنفيذ الاستعلام بنجاح (وضع تجريبي)'
  }
})

ipcMain.handle('db:search', async (event, searchParams) => {
  // Return mock search results
  return {
    success: true,
    data: [],
    total: 0,
    message: 'تم البحث بنجاح (وضع تجريبي)'
  }
})

console.log('Electron app started successfully!')
