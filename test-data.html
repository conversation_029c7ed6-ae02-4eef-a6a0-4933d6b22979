<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء ملف اختبار Excel</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 إنشاء ملف اختبار Excel</h1>
        
        <div class="info">
            <strong>ملاحظة:</strong> هذه الأداة تنشئ ملف Excel للاختبار مع بيانات وهمية لاختبار شريط التحميل.
        </div>

        <div class="form-group">
            <label for="rowCount">عدد الصفوف:</label>
            <select id="rowCount">
                <option value="100">100 صف (صغير)</option>
                <option value="500">500 صف (متوسط)</option>
                <option value="1000" selected>1000 صف (كبير)</option>
                <option value="5000">5000 صف (كبير جداً)</option>
                <option value="10000">10000 صف (ضخم)</option>
            </select>
        </div>

        <div class="form-group">
            <label for="fileName">اسم الملف:</label>
            <input type="text" id="fileName" value="بيانات_اختبار_شريط_التحميل" placeholder="اسم الملف بدون امتداد">
        </div>

        <button onclick="generateExcel()">📊 إنشاء ملف Excel</button>
        <button onclick="generateLargeExcel()" style="background: #28a745; margin-top: 10px;">🚀 إنشاء ملف كبير (لاختبار الأداء)</button>
    </div>

    <script>
        function generateExcel() {
            const rowCount = parseInt(document.getElementById('rowCount').value);
            const fileName = document.getElementById('fileName').value || 'test_data';
            
            console.log(`إنشاء ملف Excel مع ${rowCount} صف...`);
            
            // إنشاء البيانات
            const data = [];
            
            // إضافة العناوين
            data.push([
                'الرقم التسلسلي',
                'الاسم الكامل', 
                'رقم الهوية',
                'رقم الملف',
                'تاريخ الميلاد',
                'الجنسية',
                'المهنة',
                'العنوان',
                'رقم الهاتف',
                'البريد الإلكتروني',
                'تاريخ الاعتقال',
                'سبب الاعتقال',
                'الحالة',
                'ملاحظات'
            ]);
            
            // أسماء وهمية
            const firstNames = ['أحمد', 'محمد', 'علي', 'حسن', 'خالد', 'عبدالله', 'سعد', 'فهد', 'عبدالرحمن', 'يوسف'];
            const lastNames = ['الأحمد', 'المحمد', 'العلي', 'الحسن', 'الخالد', 'السعد', 'الفهد', 'اليوسف', 'العبدالله'];
            const cities = ['الرياض', 'جدة', 'الدمام', 'مكة', 'المدينة', 'الطائف', 'أبها', 'تبوك', 'بريدة', 'خميس مشيط'];
            const professions = ['مهندس', 'طبيب', 'معلم', 'محاسب', 'فني', 'موظف', 'تاجر', 'عامل', 'سائق', 'طالب'];
            const reasons = ['مخالفة مرورية', 'قضية مالية', 'نزاع تجاري', 'مخالفة نظام', 'قضية عمالية', 'نزاع شخصي'];
            const statuses = ['محتجز', 'مفرج عنه', 'محال للنيابة', 'تحت التحقيق'];
            
            // إنشاء الصفوف
            for (let i = 1; i <= rowCount; i++) {
                const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
                const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
                const fullName = `${firstName} ${lastName}`;
                
                const birthDate = new Date(1970 + Math.floor(Math.random() * 50), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
                const arrestDate = new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
                
                data.push([
                    i,
                    fullName,
                    `1${Math.floor(Math.random() * 900000000) + 100000000}`, // رقم هوية وهمي
                    `F${String(i).padStart(6, '0')}`, // رقم ملف
                    birthDate.toLocaleDateString('ar-SA'),
                    'سعودي',
                    professions[Math.floor(Math.random() * professions.length)],
                    cities[Math.floor(Math.random() * cities.length)],
                    `05${Math.floor(Math.random() * 90000000) + 10000000}`, // رقم هاتف وهمي
                    `${firstName.toLowerCase()}${i}@example.com`,
                    arrestDate.toLocaleDateString('ar-SA'),
                    reasons[Math.floor(Math.random() * reasons.length)],
                    statuses[Math.floor(Math.random() * statuses.length)],
                    i % 10 === 0 ? 'ملاحظة خاصة' : ''
                ]);
            }
            
            // إنشاء workbook
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.aoa_to_sheet(data);
            
            // تنسيق العرض
            const range = XLSX.utils.decode_range(ws['!ref']);
            ws['!cols'] = [];
            for (let i = 0; i <= range.e.c; i++) {
                ws['!cols'][i] = { wch: 15 };
            }
            
            XLSX.utils.book_append_sheet(wb, ws, 'بيانات المتهمين');
            
            // تحميل الملف
            XLSX.writeFile(wb, `${fileName}.xlsx`);
            
            alert(`تم إنشاء ملف Excel بنجاح!\nعدد الصفوف: ${rowCount}\nاسم الملف: ${fileName}.xlsx`);
        }
        
        function generateLargeExcel() {
            document.getElementById('rowCount').value = '10000';
            document.getElementById('fileName').value = 'ملف_كبير_لاختبار_الأداء';
            generateExcel();
        }
    </script>
</body>
</html>
