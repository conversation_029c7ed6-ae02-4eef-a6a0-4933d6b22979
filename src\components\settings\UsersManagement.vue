<template>
  <div class="neumorphic-card">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-3">
        <div class="neumorphic-icon">
          <i class="fas fa-users text-primary-600"></i>
        </div>
        <div>
          <h2 class="text-xl font-bold text-secondary-800">إدارة المستخدمين والصلاحيات</h2>
          <p class="text-secondary-600">إضافة وتعديل المستخدمين وتحديد صلاحياتهم</p>
        </div>
      </div>
      <button 
        @click="showAddUserModal = true"
        class="neumorphic-button text-primary-600 hover:text-primary-700"
        :disabled="loading"
      >
        <i class="fas fa-user-plus ml-2"></i>
        إضافة مستخدم جديد
      </button>
    </div>

    <!-- Users List -->
    <div class="space-y-4">
      <div
        v-for="user in users"
        :key="user.id"
        class="neumorphic-card bg-white p-4 hover:shadow-neumorphic-hover transition-all duration-300"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <!-- User Avatar -->
            <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
              <i class="fas fa-user text-primary-600"></i>
            </div>

            <!-- User Info -->
            <div>
              <div class="flex items-center gap-3 mb-1">
                <h3 class="font-semibold text-secondary-800">{{ user.name }}</h3>
                <span :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  getRoleClass(user.role.name)
                ]">
                  {{ user.role.displayName }}
                </span>
                <span :class="[
                  'px-2 py-1 rounded-full text-xs font-medium',
                  user.isActive ? 'bg-success-100 text-success-800' : 'bg-danger-100 text-danger-800'
                ]">
                  {{ user.isActive ? 'نشط' : 'معطل' }}
                </span>
              </div>
              <div class="text-sm text-secondary-600">
                <div class="flex items-center gap-4">
                  <span>
                    <i class="fas fa-envelope ml-1"></i>
                    {{ user.email }}
                  </span>
                  <span>
                    <i class="fas fa-calendar ml-1"></i>
                    انضم في {{ formatDate(user.createdAt) }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center gap-2">
            <button
              @click="toggleUserStatus(user)"
              :class="[
                'neumorphic-button p-2 text-sm',
                user.isActive ? 'text-warning-600' : 'text-success-600'
              ]"
            >
              <i :class="user.isActive ? 'fas fa-user-slash' : 'fas fa-user-check'"></i>
            </button>
            <button
              @click="editUser(user)"
              class="neumorphic-button p-2 text-sm text-primary-600"
            >
              <i class="fas fa-edit"></i>
            </button>
            <button
              @click="confirmDeleteUser(user)"
              class="neumorphic-button p-2 text-sm text-danger-600"
              :disabled="user.role.name === 'admin'"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- Permissions Summary -->
        <div class="mt-3 pt-3 border-t border-secondary-200">
          <div class="flex items-center gap-2 text-sm text-secondary-600">
            <i class="fas fa-shield-alt"></i>
            <span>الصلاحيات:</span>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="permission in getPermissionsSummary(user)"
                :key="permission"
                class="px-2 py-1 bg-secondary-100 rounded-neumorphic-sm text-xs"
              >
                {{ permission }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="!users.length" class="text-center py-12">
        <div class="neumorphic-icon mx-auto mb-4 text-secondary-400">
          <i class="fas fa-users text-2xl"></i>
        </div>
        <h3 class="text-lg font-semibold text-secondary-600 mb-2">لا يوجد مستخدمون</h3>
        <p class="text-secondary-500 mb-4">ابدأ بإضافة مستخدمين للنظام</p>
        <button 
          @click="showAddUserModal = true"
          class="neumorphic-button text-primary-600"
        >
          <i class="fas fa-user-plus ml-2"></i>
          إضافة مستخدم جديد
        </button>
      </div>
    </div>

    <!-- Add/Edit User Modal -->
    <UserModal
      v-if="showAddUserModal || editingUser"
      :user="editingUser"
      :is-editing="!!editingUser"
      :roles="availableRoles"
      @save="handleSaveUser"
      @cancel="handleCancelUser"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmModal
      v-if="userToDelete"
      title="تأكيد الحذف"
      :message="`هل أنت متأكد من حذف المستخدم '${userToDelete.name}'؟`"
      confirm-text="حذف"
      confirm-class="text-danger-600"
      @confirm="handleDeleteUser"
      @cancel="userToDelete = null"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { User, UserRole } from '@/types'
import { db } from '@/utils/database'

// Components
import UserModal from './UserModal.vue'
import ConfirmModal from '../common/ConfirmModal.vue'

// Props
interface Props {
  loading: boolean
}

const props = defineProps<Props>()

// Reactive data
const users = ref<User[]>([])
const showAddUserModal = ref(false)
const editingUser = ref<User | null>(null)
const userToDelete = ref<User | null>(null)

// Available roles
const availableRoles: UserRole[] = [
  {
    id: 'admin',
    name: 'admin',
    displayName: 'مدير النظام',
    permissions: [
      { id: '1', name: 'all', resource: '*', action: 'view' },
      { id: '2', name: 'all', resource: '*', action: 'create' },
      { id: '3', name: 'all', resource: '*', action: 'update' },
      { id: '4', name: 'all', resource: '*', action: 'delete' }
    ]
  },
  {
    id: 'officer',
    name: 'officer',
    displayName: 'ضابط',
    permissions: [
      { id: '5', name: 'suspects', resource: 'suspects', action: 'view' },
      { id: '6', name: 'suspects', resource: 'suspects', action: 'create' },
      { id: '7', name: 'suspects', resource: 'suspects', action: 'update' },
      { id: '8', name: 'reports', resource: 'reports', action: 'view' }
    ]
  },
  {
    id: 'viewer',
    name: 'viewer',
    displayName: 'قارئ',
    permissions: [
      { id: '9', name: 'suspects', resource: 'suspects', action: 'view' },
      { id: '10', name: 'reports', resource: 'reports', action: 'view' }
    ]
  }
]

// Methods
async function loadUsers() {
  try {
    users.value = await db.users.toArray()
  } catch (error) {
    console.error('Error loading users:', error)
  }
}

function getRoleClass(roleName: string): string {
  const classes: Record<string, string> = {
    admin: 'bg-red-100 text-red-800',
    officer: 'bg-blue-100 text-blue-800',
    viewer: 'bg-green-100 text-green-800'
  }
  return classes[roleName] || 'bg-secondary-100 text-secondary-800'
}

function getPermissionsSummary(user: User): string[] {
  const permissions = user.role.permissions
  const summary: string[] = []
  
  if (permissions.some(p => p.resource === '*')) {
    summary.push('جميع الصلاحيات')
  } else {
    const resources = [...new Set(permissions.map(p => p.resource))]
    resources.forEach(resource => {
      const resourcePermissions = permissions.filter(p => p.resource === resource)
      const actions = resourcePermissions.map(p => p.action)
      
      let resourceName = resource
      if (resource === 'suspects') resourceName = 'المتهمين'
      else if (resource === 'reports') resourceName = 'التقارير'
      else if (resource === 'settings') resourceName = 'الإعدادات'
      
      if (actions.includes('view') && actions.includes('create') && actions.includes('update') && actions.includes('delete')) {
        summary.push(`${resourceName} (كامل)`)
      } else {
        const actionNames = actions.map(a => {
          if (a === 'view') return 'عرض'
          if (a === 'create') return 'إضافة'
          if (a === 'update') return 'تعديل'
          if (a === 'delete') return 'حذف'
          return a
        })
        summary.push(`${resourceName} (${actionNames.join(', ')})`)
      }
    })
  }
  
  return summary
}

function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-GB', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).format(new Date(date))
}

async function toggleUserStatus(user: User) {
  try {
    await db.users.update(user.id!, { isActive: !user.isActive })
    await loadUsers()
  } catch (error) {
    console.error('Error toggling user status:', error)
  }
}

function editUser(user: User) {
  editingUser.value = { ...user }
}

function confirmDeleteUser(user: User) {
  userToDelete.value = user
}

async function handleSaveUser(userData: Omit<User, 'id'> | User) {
  try {
    if (editingUser.value && 'id' in userData) {
      // Update existing user
      const { id, ...updates } = userData as User
      await db.users.update(id!, updates)
    } else {
      // Add new user
      const newUser = userData as Omit<User, 'id'>
      await db.users.add(newUser)
    }
    
    await loadUsers()
    handleCancelUser()
  } catch (error) {
    console.error('Error saving user:', error)
  }
}

function handleCancelUser() {
  showAddUserModal.value = false
  editingUser.value = null
}

async function handleDeleteUser() {
  if (userToDelete.value) {
    try {
      await db.users.delete(userToDelete.value.id!)
      await loadUsers()
      userToDelete.value = null
    } catch (error) {
      console.error('Error deleting user:', error)
    }
  }
}

// Lifecycle
onMounted(() => {
  loadUsers()
})
</script>
