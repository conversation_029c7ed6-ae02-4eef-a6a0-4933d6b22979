<template>
  <div class="neumorphic-card">
    <div class="flex items-center gap-3 mb-6">
      <div class="neumorphic-icon">
        <i class="fas fa-code text-primary-600"></i>
      </div>
      <div>
        <h2 class="text-xl font-bold text-secondary-800">إعدادات المطور</h2>
        <p class="text-secondary-600">تخصيص معلومات المطور والمشرف</p>
      </div>
    </div>

    <!-- Developer Info Form -->
    <form @submit.prevent="saveDeveloperInfo" class="space-y-6">
      <!-- Title -->
      <div>
        <label class="block text-sm font-medium text-secondary-700 mb-2">
          <i class="fas fa-heading ml-1"></i>
          العنوان الرئيسي
        </label>
        <input
          v-model="formData.title"
          type="text"
          class="neumorphic-input w-full"
          placeholder="تطوير وتصميم"
          required
        />
      </div>

      <!-- Developer Name -->
      <div>
        <label class="block text-sm font-medium text-secondary-700 mb-2">
          <i class="fas fa-user-cog ml-1"></i>
          اسم المطور
        </label>
        <input
          v-model="formData.developerName"
          type="text"
          class="neumorphic-input w-full"
          placeholder="م- محرم اليفرسي"
          required
        />
      </div>

      <!-- Supervisor Name -->
      <div>
        <label class="block text-sm font-medium text-secondary-700 mb-2">
          <i class="fas fa-user-tie ml-1"></i>
          اسم المشرف
        </label>
        <input
          v-model="formData.supervisorName"
          type="text"
          class="neumorphic-input w-full"
          placeholder="ق/عبدالرحمن اليفرسي"
          required
        />
      </div>

      <!-- Icon Selection -->
      <div>
        <label class="block text-sm font-medium text-secondary-700 mb-2">
          <i class="fas fa-icons ml-1"></i>
          أيقونة المطور
        </label>
        
        <!-- Icon Type Selection -->
        <div class="flex gap-4 mb-4">
          <label class="flex items-center gap-2">
            <input
              v-model="iconType"
              type="radio"
              value="fontawesome"
              class="neumorphic-radio"
            />
            <span>أيقونة Font Awesome</span>
          </label>
          <label class="flex items-center gap-2">
            <input
              v-model="iconType"
              type="radio"
              value="custom"
              class="neumorphic-radio"
            />
            <span>صورة مخصصة</span>
          </label>
        </div>

        <!-- Font Awesome Icon Selection -->
        <div v-if="iconType === 'fontawesome'" class="space-y-3">
          <select
            v-model="formData.icon"
            class="neumorphic-select w-full"
          >
            <option value="">اختر أيقونة</option>
            <option value="fas fa-code">كود (fas fa-code)</option>
            <option value="fas fa-laptop-code">لابتوب كود (fas fa-laptop-code)</option>
            <option value="fas fa-terminal">تيرمينال (fas fa-terminal)</option>
            <option value="fas fa-user-cog">مطور (fas fa-user-cog)</option>
            <option value="fas fa-tools">أدوات (fas fa-tools)</option>
            <option value="fas fa-cogs">إعدادات (fas fa-cogs)</option>
            <option value="fas fa-rocket">صاروخ (fas fa-rocket)</option>
            <option value="fas fa-star">نجمة (fas fa-star)</option>
            <option value="fas fa-heart">قلب (fas fa-heart)</option>
            <option value="fas fa-crown">تاج (fas fa-crown)</option>
          </select>
          
          <!-- Icon Preview -->
          <div v-if="formData.icon" class="flex items-center gap-2 p-3 bg-secondary-50 rounded-neumorphic">
            <i :class="formData.icon + ' text-primary-600 text-xl'"></i>
            <span class="text-secondary-700">معاينة الأيقونة</span>
          </div>
        </div>

        <!-- Custom Icon Upload -->
        <div v-if="iconType === 'custom'" class="space-y-3">
          <div
            @click="triggerFileUpload"
            @dragover.prevent
            @drop.prevent="handleFileDrop"
            class="border-2 border-dashed border-secondary-300 rounded-neumorphic p-6 text-center cursor-pointer hover:border-primary-400 transition-colors"
          >
            <div v-if="formData.customIcon" class="mb-4">
              <img 
                :src="formData.customIcon" 
                alt="أيقونة مخصصة" 
                class="max-w-16 max-h-16 mx-auto rounded-neumorphic object-cover"
              />
              <p class="text-sm text-secondary-600 mt-2">{{ customIconName }}</p>
            </div>
            <div v-else class="text-secondary-500">
              <i class="fas fa-upload text-2xl mb-2"></i>
              <p>اضغط أو اسحب الصورة هنا</p>
              <p class="text-sm">PNG, JPG, SVG (حد أقصى 1MB)</p>
            </div>
          </div>
          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            @change="handleFileUpload"
            class="hidden"
          />
        </div>
      </div>

      <!-- Preview -->
      <div class="border-t border-secondary-200 pt-6">
        <h3 class="text-lg font-semibold text-secondary-800 mb-4">معاينة</h3>
        <div class="neumorphic-card bg-secondary-50 p-4">
          <div class="flex items-center gap-3">
            <!-- Icon Preview -->
            <div class="neumorphic-icon-sm">
              <i v-if="iconType === 'fontawesome' && formData.icon" 
                 :class="formData.icon + ' text-primary-600'"></i>
              <img v-else-if="iconType === 'custom' && formData.customIcon"
                   :src="formData.customIcon" 
                   alt="أيقونة مخصصة"
                   class="w-6 h-6 object-cover rounded" />
              <i v-else class="fas fa-code text-primary-600"></i>
            </div>
            
            <!-- Text Preview -->
            <div class="text-sm">
              <div class="font-semibold text-secondary-800">{{ formData.title || 'تطوير وتصميم' }}</div>
              <div class="text-secondary-600">{{ formData.developerName || 'م- محرم اليفرسي' }}</div>
              <div class="text-secondary-600">اشراف: {{ formData.supervisorName || 'ق/عبدالرحمن اليفرسي' }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end gap-4 pt-6 border-t border-secondary-200">
        <button
          type="button"
          @click="resetToDefault"
          class="neumorphic-button text-secondary-600 hover:text-secondary-700"
        >
          <i class="fas fa-undo ml-2"></i>
          إعادة تعيين افتراضي
        </button>
        <button
          type="submit"
          class="neumorphic-button text-success-600 hover:text-success-700"
          :disabled="isLoading"
        >
          <i class="fas fa-save ml-2"></i>
          حفظ الإعدادات
        </button>
      </div>
    </form>

    <!-- Success/Error Messages -->
    <div v-if="message" :class="[
      'mt-4 p-4 rounded-neumorphic text-center',
      messageType === 'success' ? 'bg-success-100 text-success-700' : 'bg-danger-100 text-danger-700'
    ]">
      {{ message }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useSettingsStore } from '@/stores/settings'
import type { DeveloperInfo } from '@/types'

// Store
const settingsStore = useSettingsStore()

// Reactive data
const isLoading = ref(false)
const message = ref('')
const messageType = ref<'success' | 'error'>('success')
const iconType = ref<'fontawesome' | 'custom'>('fontawesome')
const customIconName = ref('')
const fileInput = ref<HTMLInputElement>()

const formData = reactive<DeveloperInfo>({
  title: 'تطوير وتصميم',
  developerName: 'م- محرم اليفرسي',
  supervisorName: 'ق/عبدالرحمن اليفرسي',
  icon: 'fas fa-code',
  customIcon: undefined
})

// Methods
function triggerFileUpload() {
  fileInput.value?.click()
}

function handleFileUpload(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFile(file)
  }
}

function handleFileDrop(event: DragEvent) {
  const file = event.dataTransfer?.files[0]
  if (file) {
    processFile(file)
  }
}

function processFile(file: File) {
  // Validate file
  if (!file.type.startsWith('image/')) {
    showMessage('يرجى اختيار ملف صورة صحيح', 'error')
    return
  }

  if (file.size > 1024 * 1024) { // 1MB
    showMessage('حجم الملف كبير جداً. الحد الأقصى 1MB', 'error')
    return
  }

  // Read file as data URL
  const reader = new FileReader()
  reader.onload = (e) => {
    formData.customIcon = e.target?.result as string
    customIconName.value = file.name
    formData.icon = undefined // Clear font awesome icon
  }
  reader.readAsDataURL(file)
}

function resetToDefault() {
  formData.title = 'تطوير وتصميم'
  formData.developerName = 'م- محرم اليفرسي'
  formData.supervisorName = 'ق/عبدالرحمن اليفرسي'
  formData.icon = 'fas fa-code'
  formData.customIcon = undefined
  iconType.value = 'fontawesome'
  customIconName.value = ''
}

async function saveDeveloperInfo() {
  try {
    isLoading.value = true
    await settingsStore.updateDeveloperInfo(formData)
    showMessage('تم حفظ إعدادات المطور بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في حفظ إعدادات المطور', 'error')
    console.error('Error saving developer info:', error)
  } finally {
    isLoading.value = false
  }
}

function showMessage(text: string, type: 'success' | 'error') {
  message.value = text
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

// Load current settings
onMounted(async () => {
  await settingsStore.loadSettings()
  if (settingsStore.settings?.developerInfo) {
    Object.assign(formData, settingsStore.settings.developerInfo)
    
    // Set icon type based on what's available
    if (formData.customIcon) {
      iconType.value = 'custom'
    } else {
      iconType.value = 'fontawesome'
    }
  }
})
</script>
