const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  openFile: (options) => ipcRenderer.invoke('dialog:openFile', options),
  saveFile: (options) => ipcRenderer.invoke('dialog:saveFile', options),

  // Import operations
  importLargeDataset: (filePath, options) => ipcRenderer.invoke('import:largeDataset', filePath, options),
  getImportStatus: () => ipcRenderer.invoke('import:getStatus'),
  cancelImport: () => ipcRenderer.invoke('import:cancel'),
  onImportProgress: (callback) => ipcRenderer.on('import:progress', callback),
  removeImportProgressListener: () => ipcRenderer.removeAllListeners('import:progress'),
  
  // App info
  getVersion: () => ipcRenderer.invoke('app:getVersion'),
  
  // Window controls
  minimize: () => ipcRenderer.invoke('window:minimize'),
  maximize: () => ipcRenderer.invoke('window:maximize'),
  close: () => ipcRenderer.invoke('window:close'),
  
  // Database operations - Enhanced SQLite integration
  // Search operations
  dbSearch: (searchParams) => ipcRenderer.invoke('db:search', searchParams),
  dbQuickSearch: (query, limit) => ipcRenderer.invoke('db:quickSearch', query, limit),
  dbSearchWithHighlight: (query, options) => ipcRenderer.invoke('db:searchWithHighlight', query, options),
  dbFuzzySearch: (query, threshold) => ipcRenderer.invoke('db:fuzzySearch', query, threshold),
  dbGetStats: () => ipcRenderer.invoke('db:getStats'),

  // CRUD operations
  dbGetSuspects: (options) => ipcRenderer.invoke('db:getSuspects', options),
  dbGetSuspect: (id) => ipcRenderer.invoke('db:getSuspect', id),
  dbCreateSuspect: (suspectData) => ipcRenderer.invoke('db:createSuspect', suspectData),
  dbUpdateSuspect: (id, suspectData) => ipcRenderer.invoke('db:updateSuspect', id, suspectData),
  dbDeleteSuspect: (id) => ipcRenderer.invoke('db:deleteSuspect', id),

  // Database info
  dbGetInfo: () => ipcRenderer.invoke('db:getInfo'),

  // Activation system
  getActivationStatus: () => ipcRenderer.invoke('activation:getStatus'),
  saveActivationStatus: (status) => ipcRenderer.invoke('activation:saveStatus', status),
  clearActivationStatus: () => ipcRenderer.invoke('activation:clearStatus'),

  // Updater operations
  checkForUpdates: () => ipcRenderer.invoke('updater:checkForUpdates'),
  downloadUpdate: () => ipcRenderer.invoke('updater:downloadUpdate'),
  installUpdate: () => ipcRenderer.invoke('updater:installUpdate'),
  getUpdaterStatus: () => ipcRenderer.invoke('updater:getStatus'),
  onUpdaterEvent: (callback) => {
    ipcRenderer.on('updater:checking', callback)
    ipcRenderer.on('updater:available', callback)
    ipcRenderer.on('updater:not-available', callback)
    ipcRenderer.on('updater:error', callback)
    ipcRenderer.on('updater:download-progress', callback)
    ipcRenderer.on('updater:downloaded', callback)
    ipcRenderer.on('updater:prepare-shutdown', callback)
  },
  removeUpdaterListeners: () => {
    ipcRenderer.removeAllListeners('updater:checking')
    ipcRenderer.removeAllListeners('updater:available')
    ipcRenderer.removeAllListeners('updater:not-available')
    ipcRenderer.removeAllListeners('updater:error')
    ipcRenderer.removeAllListeners('updater:download-progress')
    ipcRenderer.removeAllListeners('updater:downloaded')
    ipcRenderer.removeAllListeners('updater:prepare-shutdown')
  },

  // Migrations operations
  getMigrationsStatus: () => ipcRenderer.invoke('migrations:getStatus'),
  runMigrations: () => ipcRenderer.invoke('migrations:run'),
  rollbackMigration: (migrationName) => ipcRenderer.invoke('migrations:rollback', migrationName),
  validateMigrations: () => ipcRenderer.invoke('migrations:validate'),
  
  // System info
  getPlatform: () => process.platform,
  getArch: () => process.arch,
  
  // Event listeners
  onMenuAction: (callback) => ipcRenderer.on('menu:action', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
})

// Prevent the renderer process from accessing Node.js
delete window.require
delete window.exports
delete window.module
