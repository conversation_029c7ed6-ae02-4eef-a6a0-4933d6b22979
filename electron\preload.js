const { contextBridge, ipc<PERSON>enderer } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  openFile: () => ipcRenderer.invoke('dialog:openFile'),
  saveFile: (data) => ipcRenderer.invoke('dialog:saveFile', data),
  
  // App info
  getVersion: () => ipcRenderer.invoke('app:getVersion'),
  
  // Window controls
  minimize: () => ipcRenderer.invoke('window:minimize'),
  maximize: () => ipcRenderer.invoke('window:maximize'),
  close: () => ipcRenderer.invoke('window:close'),
  
  // Database operations - Enhanced SQLite integration
  // Search operations
  dbSearch: (searchParams) => ipcRenderer.invoke('db:search', searchParams),
  dbQuickSearch: (query, limit) => ipcRenderer.invoke('db:quickSearch', query, limit),
  dbSearchWithHighlight: (query, options) => ipcRenderer.invoke('db:searchWithHighlight', query, options),
  dbFuzzySearch: (query, threshold) => ipcRenderer.invoke('db:fuzzySearch', query, threshold),
  dbGetStats: () => ipcRenderer.invoke('db:getStats'),

  // CRUD operations
  dbGetSuspects: (options) => ipcRenderer.invoke('db:getSuspects', options),
  dbGetSuspect: (id) => ipcRenderer.invoke('db:getSuspect', id),
  dbCreateSuspect: (suspectData) => ipcRenderer.invoke('db:createSuspect', suspectData),
  dbUpdateSuspect: (id, suspectData) => ipcRenderer.invoke('db:updateSuspect', id, suspectData),
  dbDeleteSuspect: (id) => ipcRenderer.invoke('db:deleteSuspect', id),

  // Database info
  dbGetInfo: () => ipcRenderer.invoke('db:getInfo'),

  // Activation system
  getActivationStatus: () => ipcRenderer.invoke('activation:getStatus'),
  saveActivationStatus: (status) => ipcRenderer.invoke('activation:saveStatus', status),
  clearActivationStatus: () => ipcRenderer.invoke('activation:clearStatus'),
  
  // System info
  getPlatform: () => process.platform,
  getArch: () => process.arch,
  
  // Event listeners
  onMenuAction: (callback) => ipcRenderer.on('menu:action', callback),
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel)
})

// Prevent the renderer process from accessing Node.js
delete window.require
delete window.exports
delete window.module
