<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار ميزات قاعدة البيانات</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f8fafc;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #1e40af;
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        .test-step {
            margin: 10px 0;
            padding: 10px;
            background: #f1f5f9;
            border-radius: 5px;
            border-right: 4px solid #3b82f6;
        }
        .success {
            border-right-color: #10b981;
            background: #ecfdf5;
        }
        .warning {
            border-right-color: #f59e0b;
            background: #fffbeb;
        }
        .error {
            border-right-color: #ef4444;
            background: #fef2f2;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #10b981;
            font-weight: bold;
        }
        .code {
            background: #1f2937;
            color: #f9fafb;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #1e40af;">🧪 دليل اختبار ميزات قاعدة البيانات</h1>

    <div class="test-section">
        <div class="test-title">📋 الميزات المطلوب اختبارها</div>
        <ul class="feature-list">
            <li>إنشاء تبويب جديد</li>
            <li>إضافة صف جديد</li>
            <li>إضافة عمود جديد</li>
            <li>التحرير المباشر للخلايا</li>
            <li>تحديد وحذف الصفوف</li>
            <li>الفرز بالنقر على العناوين</li>
            <li>البحث في البيانات</li>
            <li>استيراد Excel/CSV</li>
            <li>تصدير البيانات</li>
            <li>إعدادات عناوين المجموعات</li>
        </ul>
    </div>

    <div class="test-section">
        <div class="test-title">🚀 الاختبار 1: إنشاء تبويب جديد</div>
        <div class="test-step">
            <strong>الخطوة 1:</strong> اذهب إلى صفحة قاعدة البيانات
            <div class="code">http://localhost:5173/database</div>
        </div>
        <div class="test-step">
            <strong>الخطوة 2:</strong> اضغط على زر "إضافة تبويب"
        </div>
        <div class="test-step">
            <strong>الخطوة 3:</strong> املأ البيانات:
            <ul>
                <li>اسم التبويب: "اختبار الميزات"</li>
                <li>الأيقونة: "fas fa-test-tube"</li>
                <li>أضف أعمدة: الاسم، العمر، المدينة، التاريخ</li>
            </ul>
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> تبويب جديد يظهر مع الأعمدة المحددة
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">➕ الاختبار 2: إضافة صف وعمود جديد</div>
        <div class="test-step">
            <strong>الخطوة 1:</strong> اضغط على زر "إضافة صف" في شريط الأدوات
        </div>
        <div class="test-step">
            <strong>الخطوة 2:</strong> اضغط على زر "إضافة عمود" واكتب "الوظيفة"
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> صف فارغ جديد + عمود "الوظيفة" يظهران
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">✏️ الاختبار 3: التحرير المباشر</div>
        <div class="test-step">
            <strong>الخطوة 1:</strong> انقر نقرة مزدوجة على أي خلية فارغة
        </div>
        <div class="test-step">
            <strong>الخطوة 2:</strong> اكتب بيانات تجريبية (مثل: أحمد، 25، الرياض)
        </div>
        <div class="test-step">
            <strong>الخطوة 3:</strong> اضغط Enter أو انقر خارج الخلية
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> البيانات تحفظ وتظهر في الخلية
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">🗑️ الاختبار 4: تحديد وحذف الصفوف</div>
        <div class="test-step">
            <strong>الخطوة 1:</strong> أضف عدة صفوف بالبيانات
        </div>
        <div class="test-step">
            <strong>الخطوة 2:</strong> حدد checkbox لبعض الصفوف
        </div>
        <div class="test-step">
            <strong>الخطوة 3:</strong> اضغط زر "حذف المحدد"
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> رسالة تأكيد ثم حذف الصفوف المحددة
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">🔄 الاختبار 5: الفرز التفاعلي</div>
        <div class="test-step">
            <strong>الخطوة 1:</strong> أضف بيانات متنوعة في عمود "العمر"
        </div>
        <div class="test-step">
            <strong>الخطوة 2:</strong> انقر على عنوان عمود "العمر"
        </div>
        <div class="test-step">
            <strong>الخطوة 3:</strong> انقر مرة أخرى لعكس الترتيب
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> البيانات ترتب تصاعدياً ثم تنازلياً مع ظهور أسهم
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">🔍 الاختبار 6: البحث المتقدم</div>
        <div class="test-step">
            <strong>الخطوة 1:</strong> اكتب في مربع البحث "أحمد"
        </div>
        <div class="test-step">
            <strong>الخطوة 2:</strong> لاحظ تحديث الإحصائيات
        </div>
        <div class="test-step">
            <strong>الخطوة 3:</strong> امسح البحث وتأكد من عودة جميع البيانات
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> فلترة فورية مع تحديث عداد "نتائج البحث"
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">📊 الاختبار 7: الإحصائيات المباشرة</div>
        <div class="test-step">
            <strong>تحقق من ظهور:</strong>
            <ul>
                <li>إجمالي الصفوف</li>
                <li>عدد الصفوف المحددة (عند التحديد)</li>
                <li>نتائج البحث (عند البحث)</li>
                <li>وقت آخر تحديث</li>
            </ul>
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> جميع الإحصائيات تتحدث فورياً
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">📁 الاختبار 8: استيراد البيانات</div>
        <div class="test-step">
            <strong>الخطوة 1:</strong> اضغط "استيراد Excel"
        </div>
        <div class="test-step">
            <strong>الخطوة 2:</strong> ارفع ملف test-data.csv
        </div>
        <div class="test-step">
            <strong>الخطوة 3:</strong> تأكد من معاينة البيانات
        </div>
        <div class="test-step">
            <strong>الخطوة 4:</strong> اضغط "استيراد البيانات"
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> البيانات تستورد مع عناوين المجموعات
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">🎨 الاختبار 9: إعدادات عناوين المجموعات</div>
        <div class="test-step">
            <strong>الخطوة 1:</strong> اضغط زر "المجموعات" في شريط الأدوات
        </div>
        <div class="test-step">
            <strong>الخطوة 2:</strong> غير النص المعرف إلى "سجل حركة"
        </div>
        <div class="test-step">
            <strong>الخطوة 3:</strong> غير الألوان والخطوط
        </div>
        <div class="test-step">
            <strong>الخطوة 4:</strong> احفظ الإعدادات
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> عناوين المجموعات تظهر بالتنسيق الجديد
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">💾 الاختبار 10: التصدير</div>
        <div class="test-step">
            <strong>الخطوة 1:</strong> اضغط "تصدير" في شريط الأدوات
        </div>
        <div class="test-step">
            <strong>الخطوة 2:</strong> جرب تصدير CSV
        </div>
        <div class="test-step">
            <strong>الخطوة 3:</strong> جرب تصدير HTML
        </div>
        <div class="test-step success">
            <strong>النتيجة المتوقعة:</strong> ملفات تحمل بالتنسيق الصحيح
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">📈 نتائج الاختبار</div>
        <div class="test-step success">
            <strong>✅ نجح:</strong> قم بوضع علامة على كل اختبار ينجح
        </div>
        <div class="test-step warning">
            <strong>⚠️ مشاكل طفيفة:</strong> سجل أي مشاكل بسيطة
        </div>
        <div class="test-step error">
            <strong>❌ فشل:</strong> سجل أي اختبارات تفشل
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0; padding: 20px; background: #dbeafe; border-radius: 10px;">
        <h2 style="color: #1e40af;">🎯 هدف الاختبار</h2>
        <p>التأكد من أن جميع الميزات الجديدة تعمل بشكل صحيح ومتكامل</p>
        <p><strong>الوقت المتوقع للاختبار:</strong> 15-20 دقيقة</p>
    </div>
</body>
</html>
