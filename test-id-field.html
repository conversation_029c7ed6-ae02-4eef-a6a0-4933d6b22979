<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>اختبار حقل رقم الهوية</title>
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      direction: rtl;
      text-align: right;
      margin: 20px;
      background: #f8f9fa;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
      background: white;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .title {
      color: #3b82f6;
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 20px;
      text-align: center;
    }

    .field-group {
      margin-bottom: 20px;
    }

    .field-label {
      display: block;
      font-weight: bold;
      color: #333;
      margin-bottom: 8px;
    }

    .field-input {
      width: 100%;
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      font-family: 'Cairo', sans-serif;
      transition: border-color 0.3s ease;
    }

    .field-input:focus {
      outline: none;
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .field-input.valid {
      border-color: #10b981;
      background-color: #f0fdf4;
    }

    .field-input.invalid {
      border-color: #ef4444;
      background-color: #fef2f2;
    }

    .field-message {
      margin-top: 5px;
      font-size: 14px;
      padding: 5px 10px;
      border-radius: 4px;
    }

    .field-message.success {
      color: #10b981;
      background-color: #f0fdf4;
      border: 1px solid #10b981;
    }

    .field-message.error {
      color: #ef4444;
      background-color: #fef2f2;
      border: 1px solid #ef4444;
    }

    .test-cases {
      margin-top: 30px;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .test-case {
      margin-bottom: 10px;
      padding: 10px;
      background: white;
      border-radius: 6px;
      border: 1px solid #e5e7eb;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .test-case:hover {
      background-color: #f3f4f6;
    }

    .test-case-label {
      font-weight: bold;
      color: #374151;
    }

    .test-case-value {
      color: #6b7280;
      font-family: monospace;
      margin-top: 5px;
    }

    .info-box {
      background: #e0f2fe;
      border: 1px solid #0284c7;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .info-title {
      font-weight: bold;
      color: #0284c7;
      margin-bottom: 5px;
    }

    .info-text {
      color: #0369a1;
      line-height: 1.5;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">اختبار حقل رقم الهوية</h1>
    
    <div class="info-box">
      <div class="info-title">📋 الهدف من الاختبار:</div>
      <div class="info-text">
        التأكد من أن حقل "رقم الهوية" لا يحتوي على قيود تحقق صارمة، 
        ويقبل أي نص أو رقم يدخله المستخدم دون إظهار رسائل خطأ.
      </div>
    </div>

    <div class="field-group">
      <label class="field-label">رقم الهوية:</label>
      <input 
        type="text" 
        id="idField" 
        class="field-input" 
        placeholder="أدخل رقم الهوية..."
        oninput="validateField()"
      >
      <div id="fieldMessage" class="field-message" style="display: none;"></div>
    </div>

    <div class="test-cases">
      <h3 style="margin-top: 0; color: #374151;">حالات اختبار مختلفة:</h3>
      
      <div class="test-case" onclick="testValue('123456789')">
        <div class="test-case-label">رقم قصير (9 أرقام)</div>
        <div class="test-case-value">123456789</div>
      </div>

      <div class="test-case" onclick="testValue('1234567890')">
        <div class="test-case-label">رقم عادي (10 أرقام)</div>
        <div class="test-case-value">1234567890</div>
      </div>

      <div class="test-case" onclick="testValue('12345678901')">
        <div class="test-case-label">رقم طويل (11 رقم)</div>
        <div class="test-case-value">12345678901</div>
      </div>

      <div class="test-case" onclick="testValue('ABC123DEF')">
        <div class="test-case-label">رقم مختلط (أحرف وأرقام)</div>
        <div class="test-case-value">ABC123DEF</div>
      </div>

      <div class="test-case" onclick="testValue('ID-2024-001')">
        <div class="test-case-label">رقم مع رموز</div>
        <div class="test-case-value">ID-2024-001</div>
      </div>

      <div class="test-case" onclick="testValue('رقم عربي 123')">
        <div class="test-case-label">نص عربي مع أرقام</div>
        <div class="test-case-value">رقم عربي 123</div>
      </div>

      <div class="test-case" onclick="testValue('')">
        <div class="test-case-label">حقل فارغ</div>
        <div class="test-case-value">(فارغ)</div>
      </div>
    </div>
  </div>

  <script>
    function validateField() {
      const field = document.getElementById('idField');
      const message = document.getElementById('fieldMessage');
      const value = field.value.trim();

      // محاكاة النظام الجديد - بدون قيود
      if (value === '') {
        field.className = 'field-input';
        message.style.display = 'none';
      } else {
        field.className = 'field-input valid';
        message.className = 'field-message success';
        message.textContent = '✅ رقم الهوية مقبول - لا توجد قيود';
        message.style.display = 'block';
      }
    }

    function testValue(value) {
      const field = document.getElementById('idField');
      field.value = value;
      validateField();
      
      // تمييز الحقل لإظهار التغيير
      field.style.background = '#fff3cd';
      setTimeout(() => {
        field.style.background = '';
      }, 500);
    }

    // محاكاة النظام القديم للمقارنة
    function validateOldSystem(value) {
      const pattern = /^[0-9]{10}$/;
      return pattern.test(value);
    }

    // عرض مقارنة
    function showComparison() {
      const testValues = [
        '123456789',
        '1234567890', 
        '12345678901',
        'ABC123DEF',
        'ID-2024-001'
      ];

      console.log('مقارنة بين النظام القديم والجديد:');
      testValues.forEach(value => {
        const oldResult = validateOldSystem(value);
        const newResult = true; // النظام الجديد يقبل كل شيء
        console.log(`القيمة: "${value}"`);
        console.log(`  النظام القديم: ${oldResult ? '✅ مقبول' : '❌ مرفوض'}`);
        console.log(`  النظام الجديد: ${newResult ? '✅ مقبول' : '❌ مرفوض'}`);
        console.log('---');
      });
    }

    // تشغيل المقارنة عند تحميل الصفحة
    showComparison();
  </script>
</body>
</html>
