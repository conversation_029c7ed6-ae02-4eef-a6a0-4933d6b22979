const { notarize } = require('@electron/notarize')

module.exports = {
  appId: 'com.suspects.data.management',
  productName: 'برنامج بيانات المتهمين',
  copyright: 'Copyright © 2024 م- محرم اليفرسي',
  
  // Directories
  directories: {
    output: 'dist-electron',
    buildResources: 'build'
  },
  
  // Files to include
  files: [
    'dist/**/*',
    'electron/**/*',
    'node_modules/**/*',
    '!node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}',
    '!node_modules/*/{test,__tests__,tests,powered-test,example,examples}',
    '!node_modules/*.d.ts',
    '!node_modules/.bin',
    '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}',
    '!.editorconfig',
    '!**/._*',
    '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}',
    '!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}',
    '!**/{appveyor.yml,.travis.yml,circle.yml}',
    '!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}'
  ],
  
  // Extra resources
  extraResources: [
    {
      from: 'public/icons',
      to: 'icons',
      filter: ['**/*']
    }
  ],
  
  // Windows configuration
  win: {
    target: [
      {
        target: 'nsis',
        arch: ['x64', 'ia32']
      },
      {
        target: 'portable',
        arch: ['x64']
      }
    ],
    icon: 'public/icons/icon.ico',
    
    // Code signing for Windows
    certificateFile: process.env.WINDOWS_CERTIFICATE_FILE,
    certificatePassword: process.env.WINDOWS_CERTIFICATE_PASSWORD,
    
    // Security settings
    requestedExecutionLevel: 'asInvoker',
    signAndEditExecutable: true,
    signDlls: true,
    
    // Publisher information
    publisherName: 'محرم اليفرسي',
    verifyUpdateCodeSignature: true,
    
    // File associations
    fileAssociations: [
      {
        ext: 'sdb',
        name: 'Suspects Database',
        description: 'ملف قاعدة بيانات المتهمين',
        icon: 'public/icons/database.ico'
      }
    ]
  },
  
  // NSIS installer configuration
  nsis: {
    oneClick: false,
    allowElevation: true,
    allowToChangeInstallationDirectory: true,
    installerIcon: 'public/icons/installer.ico',
    uninstallerIcon: 'public/icons/uninstaller.ico',
    installerHeaderIcon: 'public/icons/header.ico',
    createDesktopShortcut: true,
    createStartMenuShortcut: true,
    shortcutName: 'برنامج بيانات المتهمين',
    
    // Multi-language support
    language: '1025', // Arabic
    
    // Custom installer script
    include: 'build/installer.nsh',
    
    // License
    license: 'LICENSE.txt',
    
    // Installer/Uninstaller messages in Arabic
    installerLanguages: ['ar', 'en'],
    
    // Security
    runAfterFinish: false,
    menuCategory: 'أدوات الأمان',
    
    // Registry entries for security
    perMachine: true,
    
    // Custom NSIS defines
    warningsAsErrors: false
  },
  
  // Portable configuration
  portable: {
    artifactName: '${productName}-${version}-portable.${ext}'
  },
  
  // macOS configuration (for future support)
  mac: {
    target: [
      {
        target: 'dmg',
        arch: ['x64', 'arm64']
      }
    ],
    icon: 'public/icons/icon.icns',
    category: 'public.app-category.productivity',
    
    // Code signing for macOS
    identity: process.env.APPLE_IDENTITY,
    
    // Hardened runtime
    hardenedRuntime: true,
    gatekeeperAssess: false,
    
    // Entitlements
    entitlements: 'build/entitlements.mac.plist',
    entitlementsInherit: 'build/entitlements.mac.plist',
    
    // Notarization
    notarize: process.env.APPLE_ID && process.env.APPLE_ID_PASSWORD ? {
      teamId: process.env.APPLE_TEAM_ID
    } : false
  },
  
  // DMG configuration
  dmg: {
    contents: [
      {
        x: 130,
        y: 220
      },
      {
        x: 410,
        y: 220,
        type: 'link',
        path: '/Applications'
      }
    ],
    artifactName: '${productName}-${version}.${ext}'
  },
  
  // Linux configuration
  linux: {
    target: [
      {
        target: 'AppImage',
        arch: ['x64']
      },
      {
        target: 'deb',
        arch: ['x64']
      },
      {
        target: 'rpm',
        arch: ['x64']
      }
    ],
    icon: 'public/icons/icon.png',
    category: 'Office',
    desktop: {
      Name: 'برنامج بيانات المتهمين',
      Comment: 'نظام متكامل لإدارة بيانات المتهمين',
      Keywords: 'suspects;database;management;security;',
      StartupWMClass: 'suspects-data-management'
    }
  },
  
  // Auto-updater configuration
  publish: [
    {
      provider: 'github',
      owner: process.env.GITHUB_OWNER || 'your-github-username',
      repo: process.env.GITHUB_REPO || 'suspects-data-management',
      private: true,
      token: process.env.GITHUB_TOKEN
    }
  ],
  
  // Compression
  compression: 'maximum',
  
  // Build metadata
  buildVersion: process.env.BUILD_NUMBER || '1',
  
  // Electron version
  electronVersion: '28.0.0',
  
  // Node.js integration
  nodeGypRebuild: false,
  buildDependenciesFromSource: false,
  
  // Security
  afterSign: async (context) => {
    const { electronPlatformName, appOutDir } = context
    
    if (electronPlatformName === 'darwin') {
      // Notarize on macOS
      if (process.env.APPLE_ID && process.env.APPLE_ID_PASSWORD) {
        console.log('🔐 Starting notarization process...')
        
        await notarize({
          appBundleId: 'com.suspects.data.management',
          appPath: `${appOutDir}/${context.packager.appInfo.productFilename}.app`,
          appleId: process.env.APPLE_ID,
          appleIdPassword: process.env.APPLE_ID_PASSWORD,
          teamId: process.env.APPLE_TEAM_ID
        })
        
        console.log('✅ Notarization completed')
      }
    }
    
    if (electronPlatformName === 'win32') {
      // Additional Windows security measures
      console.log('🔐 Applying Windows security measures...')
      
      // Here you can add additional security steps
      // like virus scanning, additional signing, etc.
      
      console.log('✅ Windows security measures applied')
    }
  },
  
  // Build hooks
  beforeBuild: async (context) => {
    console.log('🔧 Running pre-build security checks...')
    
    // Verify environment variables
    const requiredEnvVars = []
    
    if (process.platform === 'win32') {
      requiredEnvVars.push('WINDOWS_CERTIFICATE_FILE', 'WINDOWS_CERTIFICATE_PASSWORD')
    }
    
    if (process.platform === 'darwin') {
      requiredEnvVars.push('APPLE_IDENTITY')
    }
    
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
    
    if (missingVars.length > 0) {
      console.warn(`⚠️  Missing environment variables: ${missingVars.join(', ')}`)
      console.warn('Code signing may not work properly')
    }
    
    console.log('✅ Pre-build checks completed')
  },
  
  afterPack: async (context) => {
    console.log('📦 Post-pack processing...')
    
    // Additional security measures after packing
    // You can add file integrity checks, additional signing, etc.
    
    console.log('✅ Post-pack processing completed')
  }
}

// Helper function for conditional configuration
function getSigningConfig() {
  if (process.platform === 'win32') {
    return {
      certificateFile: process.env.WINDOWS_CERTIFICATE_FILE,
      certificatePassword: process.env.WINDOWS_CERTIFICATE_PASSWORD
    }
  }
  
  if (process.platform === 'darwin') {
    return {
      identity: process.env.APPLE_IDENTITY
    }
  }
  
  return {}
}
