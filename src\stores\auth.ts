import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface User {
  id: string
  username: string
  fullName: string
  role: 'admin' | 'user' | 'viewer'
  email?: string
  avatar?: string
  lastLogin?: Date
  isActive: boolean
  permissions: string[]
}

export const useAuthStore = defineStore('auth', () => {
  // الحالة
  const currentUser = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const isLoading = ref(false)
  const loginError = ref<string | null>(null)

  // المستخدمون الافتراضيون
  const defaultUsers: User[] = [
    {
      id: '1',
      username: 'admin',
      fullName: 'المدير العام',
      role: 'admin',
      email: '<EMAIL>',
      isActive: true,
      permissions: ['*'] // جميع الصلاحيات
    },
    {
      id: '2',
      username: 'investigator',
      fullName: 'محقق رئيسي',
      role: 'user',
      email: '<EMAIL>',
      isActive: true,
      permissions: ['suspects.read', 'suspects.write', 'database.read', 'database.write', 'reports.read']
    },
    {
      id: '3',
      username: 'viewer',
      fullName: 'مراقب',
      role: 'viewer',
      email: '<EMAIL>',
      isActive: true,
      permissions: ['suspects.read', 'database.read', 'reports.read']
    }
  ]

  // كلمات المرور الافتراضية (في التطبيق الحقيقي يجب تشفيرها)
  const defaultPasswords: Record<string, string> = {
    'admin': 'admin123',
    'investigator': 'inv123',
    'viewer': 'view123'
  }

  // الحسابات المحسوبة
  const userRole = computed(() => currentUser.value?.role || null)
  const userPermissions = computed(() => currentUser.value?.permissions || [])
  const isAdmin = computed(() => userRole.value === 'admin')
  const canManageUsers = computed(() => isAdmin.value || hasPermission('users.manage'))

  // الدوال
  function hasPermission(permission: string): boolean {
    if (!currentUser.value) return false
    if (currentUser.value.permissions.includes('*')) return true
    return currentUser.value.permissions.includes(permission)
  }

  async function login(username: string, password: string): Promise<boolean> {
    isLoading.value = true
    loginError.value = null

    try {
      // استيراد UserService ديناميكياً لتجنب المشاكل الدائرية
      const { userService } = await import('@/services/UserService')

      // محاولة تسجيل الدخول باستخدام UserService
      const loginResult = await userService.login(username, password)

      if (loginResult.success && loginResult.user) {
        // تحويل بيانات المستخدم للتنسيق المطلوب
        currentUser.value = {
          id: loginResult.user.id,
          username: loginResult.user.username,
          fullName: loginResult.user.name,
          role: loginResult.user.role.name as 'admin' | 'user' | 'viewer',
          email: loginResult.user.email,
          lastLogin: new Date(),
          isActive: loginResult.user.isActive,
          permissions: loginResult.user.permissions?.permissions || []
        }
        isAuthenticated.value = true

        // حفظ حالة تسجيل الدخول في localStorage
        localStorage.setItem('auth_user', JSON.stringify(currentUser.value))
        localStorage.setItem('auth_token', 'mock_token_' + Date.now())

        console.log('✅ تم تسجيل الدخول بنجاح:', currentUser.value.fullName)
        return true
      } else {
        loginError.value = loginResult.message || 'فشل في تسجيل الدخول'
        return false
      }

    } catch (error) {
      console.error('❌ خطأ في تسجيل الدخول:', error)
      loginError.value = 'حدث خطأ أثناء تسجيل الدخول'
      return false
    } finally {
      isLoading.value = false
    }
  }

  function logout() {
    currentUser.value = null
    isAuthenticated.value = false
    loginError.value = null

    // إزالة بيانات تسجيل الدخول من localStorage
    localStorage.removeItem('auth_user')
    localStorage.removeItem('auth_token')

    console.log('✅ تم تسجيل الخروج بنجاح')
  }

  function checkAuthStatus() {
    try {
      const savedUser = localStorage.getItem('auth_user')
      const savedToken = localStorage.getItem('auth_token')

      if (savedUser && savedToken) {
        currentUser.value = JSON.parse(savedUser)
        isAuthenticated.value = true
        console.log('✅ تم استعادة حالة تسجيل الدخول:', currentUser.value?.fullName)
      }
    } catch (error) {
      console.error('❌ خطأ في استعادة حالة تسجيل الدخول:', error)
      logout()
    }
  }

  function clearError() {
    loginError.value = null
  }

  // استعادة حالة تسجيل الدخول عند تحميل التطبيق
  checkAuthStatus()

  return {
    // الحالة
    currentUser,
    isAuthenticated,
    isLoading,
    loginError,
    defaultUsers,

    // الحسابات المحسوبة
    userRole,
    userPermissions,
    isAdmin,
    canManageUsers,

    // الدوال
    hasPermission,
    login,
    logout,
    checkAuthStatus,
    clearError
  }
})
