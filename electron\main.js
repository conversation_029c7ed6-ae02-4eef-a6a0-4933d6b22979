const { app, BrowserWindow, <PERSON>u, ipc<PERSON>ain, dialog } = require('electron')
const path = require('path')
const fs = require('fs').promises
const os = require('os')
const SQLiteManager = require('./database/sqlite-manager')
const SearchService = require('./services/search-service')
const ImportService = require('./services/import-service')
const UpdaterService = require('./services/updater-service')
const MigrationsManager = require('./database/migrations-manager')
const isDev = process.env.NODE_ENV === 'development'

// Keep a global reference of the window object
let mainWindow

// Database and services
let sqliteManager
let searchService
let importService
let updaterService
let migrationsManager

// Activation system configuration
const ACTIVATION_FILE = path.join(os.homedir(), '.suspects-app', 'activation.json')
const APP_DATA_DIR = path.join(os.homedir(), '.suspects-app')

function createWindow() {
  // Create the browser window with enhanced security
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),

      // Enhanced security settings
      sandbox: false, // We need access to Node.js APIs
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false,

      // Content Security Policy
      additionalArguments: [
        '--disable-web-security=false',
        '--disable-features=VizDisplayCompositor'
      ]
    },
    icon: path.join(__dirname, '../public/icons/icon.png'),
    show: false,
    titleBarStyle: 'default',

    // Window security
    autoHideMenuBar: false,
    fullscreenable: true,
    maximizable: true,
    minimizable: true,
    resizable: true,

    // Additional security
    acceptFirstMouse: false,
    disableAutoHideCursor: false
  })

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:5175')
    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // Setup Content Security Policy
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; " +
          "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
          "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
          "font-src 'self' https://fonts.gstatic.com data:; " +
          "img-src 'self' data: blob: https:; " +
          "connect-src 'self' https: wss:; " +
          "media-src 'self' data: blob:; " +
          "object-src 'none'; " +
          "base-uri 'self'; " +
          "form-action 'self';"
        ]
      }
    })
  })

  // Security event handlers
  mainWindow.webContents.on('new-window', (event, url) => {
    event.preventDefault()
    console.log('🔒 Blocked attempt to open new window:', url)
  })

  mainWindow.webContents.on('will-navigate', (event, url) => {
    if (url !== mainWindow.webContents.getURL()) {
      event.preventDefault()
      console.log('🔒 Blocked navigation attempt:', url)
    }
  })

  mainWindow.webContents.on('will-redirect', (event, url) => {
    event.preventDefault()
    console.log('🔒 Blocked redirect attempt:', url)
  })

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    // Setup updater service with main window
    if (updaterService) {
      updaterService.setMainWindow(mainWindow)
      updaterService.scheduleUpdateChecks()
    }

    // Additional security checks after window is shown
    if (isDev) {
      console.log('🔧 Development mode - some security features may be relaxed')
    } else {
      console.log('🔒 Production mode - full security enabled')
    }
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Set up menu
  createMenu()
}

function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Handle new file
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // Handle open file
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // Handle save
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'فرض إعادة التحميل', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
        { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول البرنامج',
          click: () => {
            // Show about dialog
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// App event handlers (removed - handled in activation system section)

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Close database connection before quitting
    if (sqliteManager) {
      sqliteManager.close()
    }
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Enhanced Security Measures
app.on('web-contents-created', (event, contents) => {
  // Prevent new window creation
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
    console.log('🔒 Blocked new window creation:', navigationUrl)
  })

  // Prevent navigation to external URLs
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)

    if (parsedUrl.origin !== 'http://localhost:5175' && parsedUrl.origin !== 'file://') {
      event.preventDefault()
      console.log('🔒 Blocked navigation to external URL:', navigationUrl)
    }
  })

  // Prevent opening external links
  contents.setWindowOpenHandler(({ url }) => {
    console.log('🔒 Blocked window open attempt:', url)
    return { action: 'deny' }
  })

  // Block dangerous protocols
  contents.on('will-redirect', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    const dangerousProtocols = ['file:', 'chrome:', 'chrome-extension:', 'moz-extension:']

    if (dangerousProtocols.includes(parsedUrl.protocol)) {
      event.preventDefault()
      console.log('🔒 Blocked dangerous protocol:', parsedUrl.protocol)
    }
  })
})

// Prevent certificate errors in development
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (isDev && url.startsWith('https://localhost')) {
    // In development, ignore certificate errors for localhost
    event.preventDefault()
    callback(true)
  } else {
    // In production, use default behavior
    callback(false)
  }
})

// Security: Disable node integration in all renderers
app.on('web-contents-created', (event, contents) => {
  contents.on('dom-ready', () => {
    // Remove node globals from renderer if they somehow got there
    contents.executeJavaScript(`
      delete window.require;
      delete window.exports;
      delete window.module;
      delete window.process;
      delete window.global;
      delete window.Buffer;
      delete window.setImmediate;
      delete window.clearImmediate;
    `).catch(err => {
      console.log('Could not remove node globals:', err)
    })
  })
})

// Prevent eval and related functions
app.on('web-contents-created', (event, contents) => {
  contents.on('dom-ready', () => {
    if (!isDev) {
      contents.executeJavaScript(`
        // Disable eval in production
        window.eval = function() {
          throw new Error('eval() is disabled for security reasons');
        };

        // Disable Function constructor
        window.Function = function() {
          throw new Error('Function constructor is disabled for security reasons');
        };
      `).catch(err => {
        console.log('Could not disable eval:', err)
      })
    }
  })
})

// Activation System Functions
async function ensureAppDataDir() {
  try {
    await fs.mkdir(APP_DATA_DIR, { recursive: true })
  } catch (error) {
    console.error('Failed to create app data directory:', error)
  }
}

async function getActivationStatus() {
  try {
    await ensureAppDataDir()
    const data = await fs.readFile(ACTIVATION_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    // File doesn't exist or is corrupted
    return null
  }
}

async function saveActivationStatus(status) {
  try {
    await ensureAppDataDir()
    await fs.writeFile(ACTIVATION_FILE, JSON.stringify(status, null, 2))
    console.log('✅ Activation status saved to:', ACTIVATION_FILE)
  } catch (error) {
    console.error('Failed to save activation status:', error)
    throw error
  }
}

async function clearActivationStatus() {
  try {
    await fs.unlink(ACTIVATION_FILE)
    console.log('✅ Activation status cleared')
  } catch (error) {
    // File might not exist, which is fine
    console.log('Activation file already cleared or doesn\'t exist')
  }
}

// IPC Handlers for Activation
ipcMain.handle('activation:getStatus', async () => {
  return await getActivationStatus()
})

ipcMain.handle('activation:saveStatus', async (event, status) => {
  return await saveActivationStatus(status)
})

ipcMain.handle('activation:clearStatus', async () => {
  return await clearActivationStatus()
})

// Database IPC Handlers
function setupDatabaseHandlers() {
  // Search operations
  ipcMain.handle('db:search', async (event, searchParams) => {
    try {
      return await searchService.advancedSearch(searchParams)
    } catch (error) {
      console.error('Database search error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:quickSearch', async (event, query, limit) => {
    try {
      return await searchService.quickSearch(query, limit)
    } catch (error) {
      console.error('Quick search error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:searchWithHighlight', async (event, query, options) => {
    try {
      return await searchService.searchWithHighlight(query, options)
    } catch (error) {
      console.error('Search with highlight error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:fuzzySearch', async (event, query, threshold) => {
    try {
      return await searchService.fuzzySearch(query, threshold)
    } catch (error) {
      console.error('Fuzzy search error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:getStats', async () => {
    try {
      return await searchService.getSearchStats()
    } catch (error) {
      console.error('Get stats error:', error)
      return { success: false, error: error.message }
    }
  })

  // CRUD operations
  ipcMain.handle('db:getSuspects', async (event, options = {}) => {
    try {
      const { limit = 50, offset = 0, sortBy = 'created_at', sortOrder = 'DESC' } = options

      const sql = `
        SELECT s.*,
               u1.name as created_by_name,
               u2.name as updated_by_name
        FROM suspects s
        LEFT JOIN users u1 ON s.created_by = u1.id
        LEFT JOIN users u2 ON s.updated_by = u2.id
        ORDER BY s.${sortBy} ${sortOrder}
        LIMIT ? OFFSET ?
      `

      const stmt = sqliteManager.db.prepare(sql)
      const results = stmt.all(limit, offset)

      const countStmt = sqliteManager.db.prepare('SELECT COUNT(*) as total FROM suspects')
      const { total } = countStmt.get()

      return {
        success: true,
        data: results,
        total,
        limit,
        offset
      }
    } catch (error) {
      console.error('Get suspects error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:getSuspect', async (event, id) => {
    try {
      const sql = `
        SELECT s.*,
               u1.name as created_by_name,
               u2.name as updated_by_name
        FROM suspects s
        LEFT JOIN users u1 ON s.created_by = u1.id
        LEFT JOIN users u2 ON s.updated_by = u2.id
        WHERE s.id = ?
      `

      const stmt = sqliteManager.db.prepare(sql)
      const result = stmt.get(id)

      if (!result) {
        return { success: false, error: 'Suspect not found' }
      }

      // Get attachments
      const attachmentsStmt = sqliteManager.db.prepare('SELECT * FROM attachments WHERE suspect_id = ?')
      const attachments = attachmentsStmt.all(id)

      return {
        success: true,
        data: { ...result, attachments }
      }
    } catch (error) {
      console.error('Get suspect error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:createSuspect', async (event, suspectData) => {
    try {
      const {
        file_number, full_name, id_number, address, phone, age,
        nationality, profession, marital_status, seizures, arrest_date,
        notes, created_by
      } = suspectData

      const sql = `
        INSERT INTO suspects (
          file_number, full_name, id_number, address, phone, age,
          nationality, profession, marital_status, seizures, arrest_date,
          notes, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `

      const stmt = sqliteManager.db.prepare(sql)
      const result = stmt.run(
        file_number, full_name, id_number, address, phone, age,
        nationality, profession, marital_status, seizures, arrest_date,
        notes, created_by, created_by
      )

      return {
        success: true,
        data: { id: result.lastInsertRowid, ...suspectData }
      }
    } catch (error) {
      console.error('Create suspect error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:updateSuspect', async (event, id, suspectData) => {
    try {
      const {
        file_number, full_name, id_number, address, phone, age,
        nationality, profession, marital_status, seizures, arrest_date,
        notes, is_released, release_date, is_transferred, transfer_date,
        updated_by
      } = suspectData

      const sql = `
        UPDATE suspects SET
          file_number = ?, full_name = ?, id_number = ?, address = ?, phone = ?, age = ?,
          nationality = ?, profession = ?, marital_status = ?, seizures = ?, arrest_date = ?,
          notes = ?, is_released = ?, release_date = ?, is_transferred = ?, transfer_date = ?,
          updated_by = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `

      const stmt = sqliteManager.db.prepare(sql)
      const result = stmt.run(
        file_number, full_name, id_number, address, phone, age,
        nationality, profession, marital_status, seizures, arrest_date,
        notes, is_released, release_date, is_transferred, transfer_date,
        updated_by, id
      )

      if (result.changes === 0) {
        return { success: false, error: 'Suspect not found' }
      }

      return { success: true, data: { id, ...suspectData } }
    } catch (error) {
      console.error('Update suspect error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:deleteSuspect', async (event, id) => {
    try {
      const stmt = sqliteManager.db.prepare('DELETE FROM suspects WHERE id = ?')
      const result = stmt.run(id)

      if (result.changes === 0) {
        return { success: false, error: 'Suspect not found' }
      }

      return { success: true }
    } catch (error) {
      console.error('Delete suspect error:', error)
      return { success: false, error: error.message }
    }
  })

  // Database info
  ipcMain.handle('db:getInfo', async () => {
    try {
      return {
        success: true,
        data: sqliteManager.getDatabaseInfo()
      }
    } catch (error) {
      console.error('Get database info error:', error)
      return { success: false, error: error.message }
    }
  })

  // Import operations
  ipcMain.handle('import:largeDataset', async (event, filePath, options) => {
    try {
      return await importService.importLargeDataset(filePath, {
        ...options,
        progressCallback: (progress) => {
          // Send progress updates to renderer
          event.sender.send('import:progress', progress)
        }
      })
    } catch (error) {
      console.error('Import error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('import:getStatus', async () => {
    try {
      return {
        success: true,
        data: importService.getImportStatus()
      }
    } catch (error) {
      console.error('Get import status error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('import:cancel', async () => {
    try {
      const cancelled = importService.cancelImport()
      return {
        success: true,
        data: { cancelled }
      }
    } catch (error) {
      console.error('Cancel import error:', error)
      return { success: false, error: error.message }
    }
  })

  // File dialog operations
  ipcMain.handle('dialog:openFile', async (event, options = {}) => {
    try {
      const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openFile'],
        filters: [
          { name: 'CSV Files', extensions: ['csv'] },
          { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        ...options
      })

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('Open file dialog error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('dialog:saveFile', async (event, options = {}) => {
    try {
      const result = await dialog.showSaveDialog(mainWindow, {
        filters: [
          { name: 'CSV Files', extensions: ['csv'] },
          { name: 'Excel Files', extensions: ['xlsx'] },
          { name: 'PDF Files', extensions: ['pdf'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        ...options
      })

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('Save file dialog error:', error)
      return { success: false, error: error.message }
    }
  })

  // Updater operations
  ipcMain.handle('updater:checkForUpdates', async () => {
    try {
      return await updaterService.checkForUpdates()
    } catch (error) {
      console.error('Check for updates error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('updater:downloadUpdate', async () => {
    try {
      return await updaterService.downloadUpdate()
    } catch (error) {
      console.error('Download update error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('updater:installUpdate', async () => {
    try {
      return await updaterService.installUpdate()
    } catch (error) {
      console.error('Install update error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('updater:getStatus', async () => {
    try {
      return {
        success: true,
        data: updaterService.getUpdateStatus()
      }
    } catch (error) {
      console.error('Get updater status error:', error)
      return { success: false, error: error.message }
    }
  })

  // Migrations operations
  ipcMain.handle('migrations:getStatus', async () => {
    try {
      return {
        success: true,
        data: await migrationsManager.getMigrationStatus()
      }
    } catch (error) {
      console.error('Get migrations status error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('migrations:run', async () => {
    try {
      return await migrationsManager.runMigrations()
    } catch (error) {
      console.error('Run migrations error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('migrations:rollback', async (event, migrationName) => {
    try {
      return await migrationsManager.rollbackMigration(migrationName)
    } catch (error) {
      console.error('Rollback migration error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('migrations:validate', async () => {
    try {
      return {
        success: true,
        data: await migrationsManager.validateMigrations()
      }
    } catch (error) {
      console.error('Validate migrations error:', error)
      return { success: false, error: error.message }
    }
  })

  console.log('✅ Database IPC handlers setup complete')
}

// Initialize application
async function initializeApp() {
  try {
    await ensureAppDataDir()

    // Initialize SQLite database
    sqliteManager = new SQLiteManager()
    await sqliteManager.initialize()

    // Initialize migrations manager
    migrationsManager = new MigrationsManager(sqliteManager)
    await migrationsManager.initialize()

    // Run database migrations
    await migrationsManager.runMigrations()

    // Initialize search service
    searchService = new SearchService(sqliteManager)

    // Initialize import service
    importService = new ImportService(sqliteManager)

    // Initialize updater service
    updaterService = new UpdaterService()

    // Setup database IPC handlers
    setupDatabaseHandlers()

    console.log('✅ Application initialized successfully')

    createWindow()
  } catch (error) {
    console.error('❌ Failed to initialize application:', error)
    app.quit()
  }
}

app.whenReady().then(initializeApp)
