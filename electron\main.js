const { app, BrowserWindow, <PERSON>u, ipc<PERSON>ain, dialog } = require('electron')
const path = require('path')
const fs = require('fs').promises
const os = require('os')
const SQLiteManager = require('./database/sqlite-manager')
const SearchService = require('./services/search-service')
const ImportService = require('./services/import-service')
const isDev = process.env.NODE_ENV === 'development'

// Keep a global reference of the window object
let mainWindow

// Database and services
let sqliteManager
let searchService
let importService

// Activation system configuration
const ACTIVATION_FILE = path.join(os.homedir(), '.suspects-app', 'activation.json')
const APP_DATA_DIR = path.join(os.homedir(), '.suspects-app')

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../public/icon.png'),
    show: false,
    titleBarStyle: 'default'
  })

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:5175')
    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Set up menu
  createMenu()
}

function createMenu() {
  const template = [
    {
      label: 'ملف',
      submenu: [
        {
          label: 'جديد',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            // Handle new file
          }
        },
        {
          label: 'فتح',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            // Handle open file
          }
        },
        {
          label: 'حفظ',
          accelerator: 'CmdOrCtrl+S',
          click: () => {
            // Handle save
          }
        },
        { type: 'separator' },
        {
          label: 'خروج',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'تحرير',
      submenu: [
        { label: 'تراجع', accelerator: 'CmdOrCtrl+Z', role: 'undo' },
        { label: 'إعادة', accelerator: 'Shift+CmdOrCtrl+Z', role: 'redo' },
        { type: 'separator' },
        { label: 'قص', accelerator: 'CmdOrCtrl+X', role: 'cut' },
        { label: 'نسخ', accelerator: 'CmdOrCtrl+C', role: 'copy' },
        { label: 'لصق', accelerator: 'CmdOrCtrl+V', role: 'paste' }
      ]
    },
    {
      label: 'عرض',
      submenu: [
        { label: 'إعادة تحميل', accelerator: 'CmdOrCtrl+R', role: 'reload' },
        { label: 'فرض إعادة التحميل', accelerator: 'CmdOrCtrl+Shift+R', role: 'forceReload' },
        { label: 'أدوات المطور', accelerator: 'F12', role: 'toggleDevTools' },
        { type: 'separator' },
        { label: 'تكبير', accelerator: 'CmdOrCtrl+Plus', role: 'zoomin' },
        { label: 'تصغير', accelerator: 'CmdOrCtrl+-', role: 'zoomout' },
        { label: 'حجم طبيعي', accelerator: 'CmdOrCtrl+0', role: 'resetzoom' },
        { type: 'separator' },
        { label: 'ملء الشاشة', accelerator: 'F11', role: 'togglefullscreen' }
      ]
    },
    {
      label: 'نافذة',
      submenu: [
        { label: 'تصغير', accelerator: 'CmdOrCtrl+M', role: 'minimize' },
        { label: 'إغلاق', accelerator: 'CmdOrCtrl+W', role: 'close' }
      ]
    },
    {
      label: 'مساعدة',
      submenu: [
        {
          label: 'حول البرنامج',
          click: () => {
            // Show about dialog
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)
}

// App event handlers (removed - handled in activation system section)

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    // Close database connection before quitting
    if (sqliteManager) {
      sqliteManager.close()
    }
    app.quit()
  }
})

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
  })
})

// Activation System Functions
async function ensureAppDataDir() {
  try {
    await fs.mkdir(APP_DATA_DIR, { recursive: true })
  } catch (error) {
    console.error('Failed to create app data directory:', error)
  }
}

async function getActivationStatus() {
  try {
    await ensureAppDataDir()
    const data = await fs.readFile(ACTIVATION_FILE, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    // File doesn't exist or is corrupted
    return null
  }
}

async function saveActivationStatus(status) {
  try {
    await ensureAppDataDir()
    await fs.writeFile(ACTIVATION_FILE, JSON.stringify(status, null, 2))
    console.log('✅ Activation status saved to:', ACTIVATION_FILE)
  } catch (error) {
    console.error('Failed to save activation status:', error)
    throw error
  }
}

async function clearActivationStatus() {
  try {
    await fs.unlink(ACTIVATION_FILE)
    console.log('✅ Activation status cleared')
  } catch (error) {
    // File might not exist, which is fine
    console.log('Activation file already cleared or doesn\'t exist')
  }
}

// IPC Handlers for Activation
ipcMain.handle('activation:getStatus', async () => {
  return await getActivationStatus()
})

ipcMain.handle('activation:saveStatus', async (event, status) => {
  return await saveActivationStatus(status)
})

ipcMain.handle('activation:clearStatus', async () => {
  return await clearActivationStatus()
})

// Database IPC Handlers
function setupDatabaseHandlers() {
  // Search operations
  ipcMain.handle('db:search', async (event, searchParams) => {
    try {
      return await searchService.advancedSearch(searchParams)
    } catch (error) {
      console.error('Database search error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:quickSearch', async (event, query, limit) => {
    try {
      return await searchService.quickSearch(query, limit)
    } catch (error) {
      console.error('Quick search error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:searchWithHighlight', async (event, query, options) => {
    try {
      return await searchService.searchWithHighlight(query, options)
    } catch (error) {
      console.error('Search with highlight error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:fuzzySearch', async (event, query, threshold) => {
    try {
      return await searchService.fuzzySearch(query, threshold)
    } catch (error) {
      console.error('Fuzzy search error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:getStats', async () => {
    try {
      return await searchService.getSearchStats()
    } catch (error) {
      console.error('Get stats error:', error)
      return { success: false, error: error.message }
    }
  })

  // CRUD operations
  ipcMain.handle('db:getSuspects', async (event, options = {}) => {
    try {
      const { limit = 50, offset = 0, sortBy = 'created_at', sortOrder = 'DESC' } = options

      const sql = `
        SELECT s.*,
               u1.name as created_by_name,
               u2.name as updated_by_name
        FROM suspects s
        LEFT JOIN users u1 ON s.created_by = u1.id
        LEFT JOIN users u2 ON s.updated_by = u2.id
        ORDER BY s.${sortBy} ${sortOrder}
        LIMIT ? OFFSET ?
      `

      const stmt = sqliteManager.db.prepare(sql)
      const results = stmt.all(limit, offset)

      const countStmt = sqliteManager.db.prepare('SELECT COUNT(*) as total FROM suspects')
      const { total } = countStmt.get()

      return {
        success: true,
        data: results,
        total,
        limit,
        offset
      }
    } catch (error) {
      console.error('Get suspects error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:getSuspect', async (event, id) => {
    try {
      const sql = `
        SELECT s.*,
               u1.name as created_by_name,
               u2.name as updated_by_name
        FROM suspects s
        LEFT JOIN users u1 ON s.created_by = u1.id
        LEFT JOIN users u2 ON s.updated_by = u2.id
        WHERE s.id = ?
      `

      const stmt = sqliteManager.db.prepare(sql)
      const result = stmt.get(id)

      if (!result) {
        return { success: false, error: 'Suspect not found' }
      }

      // Get attachments
      const attachmentsStmt = sqliteManager.db.prepare('SELECT * FROM attachments WHERE suspect_id = ?')
      const attachments = attachmentsStmt.all(id)

      return {
        success: true,
        data: { ...result, attachments }
      }
    } catch (error) {
      console.error('Get suspect error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:createSuspect', async (event, suspectData) => {
    try {
      const {
        file_number, full_name, id_number, address, phone, age,
        nationality, profession, marital_status, seizures, arrest_date,
        notes, created_by
      } = suspectData

      const sql = `
        INSERT INTO suspects (
          file_number, full_name, id_number, address, phone, age,
          nationality, profession, marital_status, seizures, arrest_date,
          notes, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `

      const stmt = sqliteManager.db.prepare(sql)
      const result = stmt.run(
        file_number, full_name, id_number, address, phone, age,
        nationality, profession, marital_status, seizures, arrest_date,
        notes, created_by, created_by
      )

      return {
        success: true,
        data: { id: result.lastInsertRowid, ...suspectData }
      }
    } catch (error) {
      console.error('Create suspect error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:updateSuspect', async (event, id, suspectData) => {
    try {
      const {
        file_number, full_name, id_number, address, phone, age,
        nationality, profession, marital_status, seizures, arrest_date,
        notes, is_released, release_date, is_transferred, transfer_date,
        updated_by
      } = suspectData

      const sql = `
        UPDATE suspects SET
          file_number = ?, full_name = ?, id_number = ?, address = ?, phone = ?, age = ?,
          nationality = ?, profession = ?, marital_status = ?, seizures = ?, arrest_date = ?,
          notes = ?, is_released = ?, release_date = ?, is_transferred = ?, transfer_date = ?,
          updated_by = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `

      const stmt = sqliteManager.db.prepare(sql)
      const result = stmt.run(
        file_number, full_name, id_number, address, phone, age,
        nationality, profession, marital_status, seizures, arrest_date,
        notes, is_released, release_date, is_transferred, transfer_date,
        updated_by, id
      )

      if (result.changes === 0) {
        return { success: false, error: 'Suspect not found' }
      }

      return { success: true, data: { id, ...suspectData } }
    } catch (error) {
      console.error('Update suspect error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('db:deleteSuspect', async (event, id) => {
    try {
      const stmt = sqliteManager.db.prepare('DELETE FROM suspects WHERE id = ?')
      const result = stmt.run(id)

      if (result.changes === 0) {
        return { success: false, error: 'Suspect not found' }
      }

      return { success: true }
    } catch (error) {
      console.error('Delete suspect error:', error)
      return { success: false, error: error.message }
    }
  })

  // Database info
  ipcMain.handle('db:getInfo', async () => {
    try {
      return {
        success: true,
        data: sqliteManager.getDatabaseInfo()
      }
    } catch (error) {
      console.error('Get database info error:', error)
      return { success: false, error: error.message }
    }
  })

  // Import operations
  ipcMain.handle('import:largeDataset', async (event, filePath, options) => {
    try {
      return await importService.importLargeDataset(filePath, {
        ...options,
        progressCallback: (progress) => {
          // Send progress updates to renderer
          event.sender.send('import:progress', progress)
        }
      })
    } catch (error) {
      console.error('Import error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('import:getStatus', async () => {
    try {
      return {
        success: true,
        data: importService.getImportStatus()
      }
    } catch (error) {
      console.error('Get import status error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('import:cancel', async () => {
    try {
      const cancelled = importService.cancelImport()
      return {
        success: true,
        data: { cancelled }
      }
    } catch (error) {
      console.error('Cancel import error:', error)
      return { success: false, error: error.message }
    }
  })

  // File dialog operations
  ipcMain.handle('dialog:openFile', async (event, options = {}) => {
    try {
      const result = await dialog.showOpenDialog(mainWindow, {
        properties: ['openFile'],
        filters: [
          { name: 'CSV Files', extensions: ['csv'] },
          { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        ...options
      })

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('Open file dialog error:', error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle('dialog:saveFile', async (event, options = {}) => {
    try {
      const result = await dialog.showSaveDialog(mainWindow, {
        filters: [
          { name: 'CSV Files', extensions: ['csv'] },
          { name: 'Excel Files', extensions: ['xlsx'] },
          { name: 'PDF Files', extensions: ['pdf'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        ...options
      })

      return {
        success: true,
        data: result
      }
    } catch (error) {
      console.error('Save file dialog error:', error)
      return { success: false, error: error.message }
    }
  })

  console.log('✅ Database IPC handlers setup complete')
}

// Initialize application
async function initializeApp() {
  try {
    await ensureAppDataDir()

    // Initialize SQLite database
    sqliteManager = new SQLiteManager()
    await sqliteManager.initialize()

    // Initialize search service
    searchService = new SearchService(sqliteManager)

    // Initialize import service
    importService = new ImportService(sqliteManager)

    // Setup database IPC handlers
    setupDatabaseHandlers()

    console.log('✅ Application initialized successfully')

    createWindow()
  } catch (error) {
    console.error('❌ Failed to initialize application:', error)
    app.quit()
  }
}

app.whenReady().then(initializeApp)
