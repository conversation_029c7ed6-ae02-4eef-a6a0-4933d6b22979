<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6 pb-4 border-b border-secondary-200">
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
            <i class="fas fa-search-plus text-purple-600 text-xl"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-secondary-800">إعداد مذكرة بحث ومطابقة</h3>
            <p class="text-sm text-secondary-600">البحث عن ارتباطات المشتبه به في قاعدة البيانات</p>
          </div>
        </div>
        <button @click="$emit('close')" class="text-secondary-400 hover:text-secondary-600 text-xl">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Search Form -->
      <div class="space-y-6">
        <!-- Phone Number Input -->
        <div class="neumorphic-card bg-secondary-50 p-4">
          <label class="block text-sm font-medium text-secondary-700 mb-3">
            <i class="fas fa-phone ml-2 text-purple-600"></i>
            رقم هاتف المشتبه به المراد البحث عن ارتباطاته
          </label>
          <input
            v-model="formData.phoneNumber"
            type="text"
            placeholder="أدخل رقم الهاتف المراد البحث عنه"
            class="neumorphic-input w-full text-lg"
            dir="ltr"
          />
        </div>

        <!-- Name Input -->
        <div class="neumorphic-card bg-secondary-50 p-4">
          <label class="block text-sm font-medium text-secondary-700 mb-3">
            <i class="fas fa-id-card ml-2 text-purple-600"></i>
            اسم المشتبه به
          </label>
          <input
            v-model="formData.suspectName"
            type="text"
            placeholder="الاسم الكامل للمشتبه به"
            class="neumorphic-input w-full text-lg"
          />
        </div>

        <!-- Control Buttons -->
        <div class="neumorphic-card bg-gradient-to-r from-purple-50 to-blue-50 p-6">
          <h4 class="text-lg font-semibold text-secondary-800 mb-4 flex items-center gap-2">
            <i class="fas fa-cogs text-purple-600"></i>
            أوامر البحث والمطابقة
          </h4>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Search Button -->
            <button
              @click="handleStartSearch"
              :disabled="!canStartSearch"
              class="neumorphic-button bg-white hover:bg-blue-50 p-4 rounded-lg transition-all duration-300 flex flex-col items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed"
              :class="canStartSearch ? 'hover:shadow-lg' : ''"
            >
              <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <i class="fas fa-search text-blue-600 text-xl"></i>
              </div>
              <div class="text-center">
                <div class="font-medium text-secondary-800">البدء في عملية البحث والمطابقة</div>
                <div class="text-xs text-secondary-600 mt-1">البحث في جميع التبويبات</div>
              </div>
            </button>

            <!-- Generate Memo Button -->
            <button
              @click="handleGenerateMemo"
              :disabled="!canGenerateMemo"
              class="neumorphic-button bg-white hover:bg-green-50 p-4 rounded-lg transition-all duration-300 flex flex-col items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed"
              :class="canGenerateMemo ? 'hover:shadow-lg' : ''"
            >
              <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <i class="fas fa-file-alt text-green-600 text-xl"></i>
              </div>
              <div class="text-center">
                <div class="font-medium text-secondary-800">إنشاء مذكرة ملخص البحث والمطابقة</div>
                <div class="text-xs text-secondary-600 mt-1">تلخيص النتائج</div>
              </div>
            </button>

            <!-- Export Results Button -->
            <button
              @click="handleExportResults"
              :disabled="!canExportResults"
              class="neumorphic-button bg-white hover:bg-amber-50 p-4 rounded-lg transition-all duration-300 flex flex-col items-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed"
              :class="canExportResults ? 'hover:shadow-lg' : ''"
            >
              <div class="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center">
                <i class="fas fa-download text-amber-600 text-xl"></i>
              </div>
              <div class="text-center">
                <div class="font-medium text-secondary-800">تصدير نتيجة المطابقة</div>
                <div class="text-xs text-secondary-600 mt-1">حفظ النتائج</div>
              </div>
            </button>
          </div>
        </div>

        <!-- Search Status -->
        <div v-if="searchStatus" class="neumorphic-card p-4" :class="getStatusClass()">
          <div class="flex items-center gap-3">
            <i :class="getStatusIcon()"></i>
            <div class="flex-1">
              <div class="font-medium">{{ searchStatus.title }}</div>
              <div class="text-sm mt-1">{{ searchStatus.message }}</div>

              <!-- Progress Bar -->
              <div v-if="searchStatus.type === 'loading' && searchProgress > 0" class="mt-3">
                <div class="flex items-center justify-between text-xs text-secondary-600 mb-1">
                  <span>تقدم البحث</span>
                  <span>{{ searchProgress }}%</span>
                </div>
                <div class="w-full bg-secondary-200 rounded-full h-2">
                  <div
                    class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    :style="{ width: searchProgress + '%' }"
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Results Summary -->
        <div v-if="searchResults" class="neumorphic-card bg-blue-50 p-4">
          <h4 class="font-semibold text-secondary-800 mb-3 flex items-center gap-2">
            <i class="fas fa-chart-bar text-blue-600"></i>
            ملخص نتائج البحث
          </h4>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div class="bg-white rounded-lg p-3">
              <div class="text-2xl font-bold text-blue-600">{{ searchResults.smsCount }}</div>
              <div class="text-xs text-secondary-600">رسائل نصية</div>
            </div>
            <div class="bg-white rounded-lg p-3">
              <div class="text-2xl font-bold text-green-600">{{ searchResults.callsCount }}</div>
              <div class="text-xs text-secondary-600">مكالمات</div>
            </div>
            <div class="bg-white rounded-lg p-3">
              <div class="text-2xl font-bold text-purple-600">{{ searchResults.contactsCount }}</div>
              <div class="text-xs text-secondary-600">جهات اتصال</div>
            </div>
            <div class="bg-white rounded-lg p-3">
              <div class="text-2xl font-bold text-amber-600">{{ searchResults.totalMatches }}</div>
              <div class="text-xs text-secondary-600">إجمالي المطابقات</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Actions -->
      <div class="flex items-center justify-end gap-3 mt-8 pt-4 border-t border-secondary-200">
        <button
          @click="$emit('close')"
          class="neumorphic-button bg-secondary-100 text-secondary-700 px-6 py-2 rounded-lg hover:bg-secondary-200 transition-colors"
        >
          إغلاق
        </button>
        <button
          @click="handleReset"
          class="neumorphic-button bg-warning-100 text-warning-700 px-6 py-2 rounded-lg hover:bg-warning-200 transition-colors"
        >
          إعادة تعيين
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'

// Types
interface SearchResults {
  smsCount: number
  callsCount: number
  contactsCount: number
  totalMatches: number
  details: any[]
}

interface SearchStatus {
  type: 'loading' | 'success' | 'error' | 'info'
  title: string
  message: string
}

// Emits
const emit = defineEmits<{
  close: []
  startSearch: [data: { phoneNumber: string, suspectName: string }]
  generateMemo: [data: { phoneNumber: string, suspectName: string, results: SearchResults }]
  exportResults: [data: { phoneNumber: string, suspectName: string, results: SearchResults }]
  reset: []
}>()

// Reactive data
const formData = reactive({
  phoneNumber: '',
  suspectName: ''
})

const searchStatus = ref<SearchStatus | null>(null)
const searchResults = ref<SearchResults | null>(null)
const searchProgress = ref(0)

// Props للتحكم في حالة النافذة من الخارج
const props = defineProps<{
  initialData?: {
    phoneNumber: string
    suspectName: string
    hasResults: boolean
  }
}>()

// Computed
const isFormValid = computed(() => {
  return formData.phoneNumber.trim() !== '' &&
         formData.suspectName.trim() !== ''
})

const hasSearchResults = computed(() => {
  return searchResults.value !== null && searchResults.value.totalMatches > 0
})

// حالة الأيقونات حسب وجود التبويبات المؤقتة
const canStartSearch = computed(() => {
  return isFormValid.value && !props.initialData?.hasResults
})

const canGenerateMemo = computed(() => {
  return isFormValid.value && (hasSearchResults.value || props.initialData?.hasResults)
})

const canExportResults = computed(() => {
  return isFormValid.value && (hasSearchResults.value || props.initialData?.hasResults)
})

// Methods
function handleStartSearch() {
  // التحقق من صحة الإدخال
  if (!formData.phoneNumber.trim()) {
    searchStatus.value = {
      type: 'error',
      title: 'حقل رقم الهاتف فارغ!',
      message: 'يرجى إدخال رقم هاتف المشتبه به قبل البدء بعملية البحث والمطابقة.'
    }
    return
  }

  if (!formData.suspectName.trim()) {
    searchStatus.value = {
      type: 'error',
      title: 'حقل اسم المشتبه به فارغ!',
      message: 'يرجى إدخال اسم المشتبه به قبل البدء بعملية البحث والمطابقة.'
    }
    return
  }

  // بدء عملية البحث
  searchStatus.value = {
    type: 'loading',
    title: 'جاري البحث...',
    message: 'يتم البحث في جميع التبويبات عن الرقم المحدد'
  }

  emit('startSearch', {
    phoneNumber: formData.phoneNumber.trim(),
    suspectName: formData.suspectName.trim()
  })
}

function handleGenerateMemo() {
  if (!canGenerateMemo.value) return

  // إنشاء نتائج وهمية فقط إذا لم تكن هناك نتائج حقيقية
  const results = searchResults.value || {
    smsCount: 0,
    callsCount: 0,
    contactsCount: 0,
    totalMatches: 1, // قيمة وهمية للإشارة إلى وجود تبويبات مؤقتة
    details: []
  }

  emit('generateMemo', {
    phoneNumber: formData.phoneNumber.trim(),
    suspectName: formData.suspectName.trim(),
    results: results
  })
}

function handleExportResults() {
  if (!canExportResults.value) return

  // إنشاء نتائج وهمية فقط إذا لم تكن هناك نتائج حقيقية
  const results = searchResults.value || {
    smsCount: 0,
    callsCount: 0,
    contactsCount: 0,
    totalMatches: 1, // قيمة وهمية للإشارة إلى وجود تبويبات مؤقتة
    details: []
  }

  emit('exportResults', {
    phoneNumber: formData.phoneNumber.trim(),
    suspectName: formData.suspectName.trim(),
    results: results
  })
}

function handleReset() {
  formData.phoneNumber = ''
  formData.suspectName = ''
  searchStatus.value = null
  searchResults.value = null
  searchProgress.value = 0

  // إشعار المكون الأب بإعادة التعيين
  emit('reset')
}

// تهيئة البيانات عند فتح النافذة
function initializeData() {
  if (props.initialData) {
    formData.phoneNumber = props.initialData.phoneNumber
    formData.suspectName = props.initialData.suspectName

    if (props.initialData.hasResults) {
      // لا ننشئ نتائج وهمية هنا، فقط نُظهر الرسالة
      searchStatus.value = {
        type: 'info',
        title: 'توجد نتائج بحث سابقة',
        message: 'يمكنك إنشاء المذكرة أو تصدير النتائج مباشرة'
      }
    }
  }
}

// تهيئة البيانات عند تحميل المكون
onMounted(() => {
  initializeData()
})

// مراقبة تغيير البيانات الأولية
watch(() => props.initialData, () => {
  initializeData()
}, { deep: true })

function getStatusClass() {
  if (!searchStatus.value) return ''
  
  switch (searchStatus.value.type) {
    case 'loading': return 'bg-blue-50 border border-blue-200'
    case 'success': return 'bg-success-50 border border-success-200'
    case 'error': return 'bg-danger-50 border border-danger-200'
    case 'info': return 'bg-info-50 border border-info-200'
    default: return 'bg-secondary-50 border border-secondary-200'
  }
}

function getStatusIcon() {
  if (!searchStatus.value) return ''
  
  switch (searchStatus.value.type) {
    case 'loading': return 'fas fa-spinner fa-spin text-blue-600'
    case 'success': return 'fas fa-check-circle text-success-600'
    case 'error': return 'fas fa-exclamation-triangle text-danger-600'
    case 'info': return 'fas fa-info-circle text-info-600'
    default: return 'fas fa-info-circle text-secondary-600'
  }
}

// Expose methods for parent component
defineExpose({
  setSearchResults: (results: SearchResults) => {
    searchResults.value = results
    searchProgress.value = 100
    searchStatus.value = {
      type: 'success',
      title: 'تم البحث بنجاح',
      message: `تم العثور على ${results.totalMatches} مطابقة في قاعدة البيانات`
    }
  },
  setSearchError: (error: string) => {
    searchProgress.value = 0
    searchStatus.value = {
      type: 'error',
      title: 'خطأ في البحث',
      message: error
    }
  },
  setSearchLoading: (message: string) => {
    searchStatus.value = {
      type: 'loading',
      title: 'جاري البحث...',
      message
    }
  },
  setSearchProgress: (progress: number) => {
    searchProgress.value = progress
  },
  updateSearchMessage: (message: string) => {
    if (searchStatus.value) {
      searchStatus.value.message = message
    }
  }
})
</script>

<style scoped>
.neumorphic-card {
  box-shadow: 
    8px 8px 16px rgba(0, 0, 0, 0.1),
    -8px -8px 16px rgba(255, 255, 255, 0.8);
}

.neumorphic-button {
  box-shadow: 
    4px 4px 8px rgba(0, 0, 0, 0.1),
    -4px -4px 8px rgba(255, 255, 255, 0.8);
}

.neumorphic-button:hover:not(:disabled) {
  box-shadow: 
    2px 2px 4px rgba(0, 0, 0, 0.1),
    -2px -2px 4px rgba(255, 255, 255, 0.8);
}

.neumorphic-input {
  box-shadow: 
    inset 4px 4px 8px rgba(0, 0, 0, 0.1),
    inset -4px -4px 8px rgba(255, 255, 255, 0.8);
  border: none;
  outline: none;
  padding: 12px 16px;
  border-radius: 8px;
  background: #f8fafc;
}

.neumorphic-input:focus {
  box-shadow: 
    inset 6px 6px 12px rgba(0, 0, 0, 0.15),
    inset -6px -6px 12px rgba(255, 255, 255, 0.9);
}
</style>
