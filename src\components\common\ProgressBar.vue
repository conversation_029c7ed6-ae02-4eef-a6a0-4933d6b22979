<template>
  <div class="progress-container">
    <!-- Progress Header -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center gap-3">
        <div class="relative">
          <div v-if="status === 'loading'" class="w-6 h-6 border-2 border-primary-200 border-t-primary-600 rounded-full animate-spin"></div>
          <div v-else-if="status === 'success'" class="w-6 h-6 bg-success-100 rounded-full flex items-center justify-center">
            <i class="fas fa-check text-success-600 text-sm"></i>
          </div>
          <div v-else-if="status === 'error'" class="w-6 h-6 bg-danger-100 rounded-full flex items-center justify-center">
            <i class="fas fa-times text-danger-600 text-sm"></i>
          </div>
          <div v-else class="w-6 h-6 bg-secondary-100 rounded-full flex items-center justify-center">
            <i class="fas fa-clock text-secondary-600 text-sm"></i>
          </div>
        </div>
        
        <div>
          <h4 class="font-medium text-secondary-800">{{ title }}</h4>
          <p class="text-sm text-secondary-600">{{ currentMessage }}</p>
        </div>
      </div>
      
      <div class="flex items-center gap-3">
        <!-- Progress Percentage -->
        <div class="text-right">
          <div class="text-lg font-bold text-primary-600">{{ Math.round(progress) }}%</div>
          <div v-if="showDetails" class="text-xs text-secondary-500">
            {{ processedItems }}/{{ totalItems }}
          </div>
        </div>

        <!-- Close Button (only show when success or error) -->
        <button
          v-if="status === 'success' || status === 'error'"
          @click="$emit('close')"
          class="w-8 h-8 rounded-full bg-secondary-100 hover:bg-secondary-200 flex items-center justify-center transition-colors"
          title="إغلاق"
        >
          <i class="fas fa-times text-secondary-600 text-sm"></i>
        </button>
      </div>
    </div>

    <!-- Progress Bar -->
    <div class="relative">
      <!-- Background -->
      <div class="w-full bg-secondary-200 rounded-full h-3 overflow-hidden">
        <!-- Progress Fill -->
        <div
          class="h-full transition-all duration-300 ease-out rounded-full"
          :class="getProgressBarClass"
          :style="{ width: `${Math.min(progress, 100)}%` }"
        >
          <!-- Animated Stripes for Loading -->
          <div 
            v-if="status === 'loading' && showStripes"
            class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-20 animate-pulse"
          ></div>
        </div>
      </div>
      
      <!-- Progress Steps (if provided) -->
      <div v-if="steps && steps.length > 0" class="flex justify-between mt-2">
        <div 
          v-for="(step, index) in steps" 
          :key="index"
          class="flex flex-col items-center"
          :style="{ width: `${100 / steps.length}%` }"
        >
          <div 
            class="w-3 h-3 rounded-full border-2 transition-all duration-300"
            :class="getStepClass(index)"
          ></div>
          <span class="text-xs text-secondary-600 mt-1 text-center">{{ step.name }}</span>
        </div>
      </div>
    </div>

    <!-- Detailed Information -->
    <div v-if="showDetails && details" class="mt-4 p-3 bg-secondary-50 rounded-lg">
      <div class="grid grid-cols-2 gap-4 text-sm">
        <div v-if="details.fileSize">
          <span class="text-secondary-600">حجم الملف:</span>
          <span class="font-medium text-secondary-800 mr-2">{{ formatFileSize(details.fileSize) }}</span>
        </div>
        <div v-if="details.processingSpeed">
          <span class="text-secondary-600">سرعة المعالجة:</span>
          <span class="font-medium text-secondary-800 mr-2">{{ details.processingSpeed }}</span>
        </div>
        <div v-if="details.estimatedTime">
          <span class="text-secondary-600">الوقت المتبقي:</span>
          <span class="font-medium text-secondary-800 mr-2">{{ details.estimatedTime }}</span>
        </div>
        <div v-if="details.currentOperation">
          <span class="text-secondary-600">العملية الحالية:</span>
          <span class="font-medium text-secondary-800 mr-2">{{ details.currentOperation }}</span>
        </div>
      </div>
    </div>

    <!-- Error Details -->
    <div v-if="status === 'error' && errorMessage" class="mt-3 p-3 bg-danger-50 border border-danger-200 rounded-lg">
      <div class="flex items-start gap-2">
        <i class="fas fa-exclamation-triangle text-danger-600 mt-0.5"></i>
        <div>
          <p class="text-danger-800 font-medium">حدث خطأ أثناء المعالجة</p>
          <p class="text-danger-700 text-sm mt-1">{{ errorMessage }}</p>
        </div>
      </div>
    </div>

    <!-- Success Details -->
    <div v-if="status === 'success' && successMessage" class="mt-3 p-3 bg-success-50 border border-success-200 rounded-lg">
      <div class="flex items-start gap-2">
        <i class="fas fa-check-circle text-success-600 mt-0.5"></i>
        <div>
          <p class="text-success-800 font-medium">تمت العملية بنجاح</p>
          <p class="text-success-700 text-sm mt-1">{{ successMessage }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// Types
interface ProgressStep {
  name: string
  completed: boolean
}

interface ProgressDetails {
  fileSize?: number
  processingSpeed?: string
  estimatedTime?: string
  currentOperation?: string
}

// Props
interface Props {
  progress: number // 0-100
  status: 'idle' | 'loading' | 'success' | 'error'
  title: string
  currentMessage: string
  totalItems?: number
  processedItems?: number
  steps?: ProgressStep[]
  showDetails?: boolean
  showStripes?: boolean
  details?: ProgressDetails
  errorMessage?: string
  successMessage?: string
}

const props = withDefaults(defineProps<Props>(), {
  progress: 0,
  status: 'idle',
  title: 'جاري المعالجة...',
  currentMessage: 'يرجى الانتظار...',
  totalItems: 0,
  processedItems: 0,
  showDetails: false,
  showStripes: true
})

// Emits
const emit = defineEmits<{
  close: []
}>()

// Computed
const getProgressBarClass = computed(() => {
  switch (props.status) {
    case 'loading':
      return 'bg-gradient-to-r from-primary-500 to-primary-600'
    case 'success':
      return 'bg-gradient-to-r from-success-500 to-success-600'
    case 'error':
      return 'bg-gradient-to-r from-danger-500 to-danger-600'
    default:
      return 'bg-gradient-to-r from-secondary-400 to-secondary-500'
  }
})

// Methods
function getStepClass(index: number): string {
  const step = props.steps?.[index]
  if (!step) return 'border-secondary-300 bg-white'

  if (step.completed) {
    return 'border-success-500 bg-success-500'
  } else {
    const currentStepIndex = props.steps?.findIndex(s => !s.completed) ?? 0
    if (index === currentStepIndex) {
      return 'border-primary-500 bg-primary-100'
    } else {
      return 'border-secondary-300 bg-white'
    }
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}


</script>

<style scoped>
.progress-container {
  @apply w-full;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}
</style>
