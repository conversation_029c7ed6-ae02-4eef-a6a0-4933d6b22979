const { app, Browser<PERSON>indow, Menu, ipc<PERSON><PERSON>, dialog } = require('electron')
const path = require('path')
const fs = require('fs').promises
const os = require('os')
const isDev = process.env.NODE_ENV === 'development'

// Keep a global reference of the window object
let mainWindow

// Database fallback - use file system for simple operations
const dbPath = path.join(os.homedir(), '.suspects-app')
const dataFile = path.join(dbPath, 'suspects-data.json')
const usersFile = path.join(dbPath, 'users-data.json')

// Ensure data directory exists
async function ensureDataDir() {
  try {
    await fs.mkdir(dbPath, { recursive: true })
  } catch (error) {
    console.error('Error creating data directory:', error)
  }
}

// Simple file-based database operations
async function readJsonFile(filePath, defaultData = []) {
  try {
    const data = await fs.readFile(filePath, 'utf8')
    return JSON.parse(data)
  } catch (error) {
    return defaultData
  }
}

async function writeJsonFile(filePath, data) {
  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8')
    return { success: true }
  } catch (error) {
    console.error('Error writing file:', error)
    return { success: false, error: error.message }
  }
}

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    icon: path.join(__dirname, '../public/icon.png'),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload-hybrid.js'),
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false
    },
    show: false,
    titleBarStyle: 'default',
    frame: true,
    backgroundColor: '#f8fafc'
  })

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:5175')
    // Open DevTools in development
    mainWindow.webContents.openDevTools()
  } else {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()
    
    if (isDev) {
      mainWindow.webContents.openDevTools()
    }
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    require('electron').shell.openExternal(url)
    return { action: 'deny' }
  })

  // Security: Prevent new window creation
  mainWindow.webContents.on('new-window', (event, url) => {
    event.preventDefault()
    require('electron').shell.openExternal(url)
  })
}

// App event handlers
app.whenReady().then(async () => {
  await ensureDataDir()
  createWindow()

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Security: Prevent navigation to external websites
app.on('web-contents-created', (event, contents) => {
  contents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl)
    
    if (parsedUrl.origin !== 'http://localhost:5175' && parsedUrl.origin !== 'http://localhost:5176') {
      event.preventDefault()
    }
  })
})

// IPC Handlers

// App info
ipcMain.handle('app:getVersion', () => {
  return app.getVersion()
})

// File operations
ipcMain.handle('dialog:openFile', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options)
  return result
})

ipcMain.handle('dialog:saveFile', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options)
  return result
})

// Activation system
let isActivated = false
const activationFile = path.join(dbPath, 'activation.json')

ipcMain.handle('activation:check', async () => {
  try {
    const data = await readJsonFile(activationFile, { activated: false })
    isActivated = data.activated
    return isActivated
  } catch (error) {
    return false
  }
})

ipcMain.handle('activation:activate', async (event, code) => {
  const validCodes = ['773$729#886', '777$3236#888', '777$167#794']
  if (validCodes.includes(code)) {
    isActivated = true
    await writeJsonFile(activationFile, { activated: true, date: new Date().toISOString() })
    return { success: true }
  }
  return { success: false, error: 'كود التفعيل غير صحيح' }
})

// Database operations using file system
ipcMain.handle('db:query', async (event, query, params) => {
  try {
    // Simple query handling for basic operations
    if (query.includes('SELECT') && query.includes('suspects')) {
      const data = await readJsonFile(dataFile, [])
      return { success: true, data, message: 'تم تنفيذ الاستعلام بنجاح' }
    }
    
    if (query.includes('INSERT') && query.includes('suspects')) {
      const data = await readJsonFile(dataFile, [])
      const newRecord = params || {}
      newRecord.id = Date.now()
      data.push(newRecord)
      await writeJsonFile(dataFile, data)
      return { success: true, data: [newRecord], message: 'تم إضافة السجل بنجاح' }
    }
    
    return { success: true, data: [], message: 'تم تنفيذ الاستعلام' }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

ipcMain.handle('db:search', async (event, searchParams) => {
  try {
    const data = await readJsonFile(dataFile, [])
    let results = data
    
    if (searchParams.query) {
      const query = searchParams.query.toLowerCase()
      results = data.filter(record => 
        Object.values(record).some(value => 
          String(value).toLowerCase().includes(query)
        )
      )
    }
    
    return {
      success: true,
      data: results,
      total: results.length,
      message: 'تم البحث بنجاح'
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

// Import operations
ipcMain.handle('import:largeDataset', async (event, filePath, options) => {
  try {
    // Simple CSV import simulation
    const csvData = await fs.readFile(filePath, 'utf8')
    const lines = csvData.split('\n').filter(line => line.trim())
    const headers = lines[0].split(',')
    
    const records = []
    for (let i = 1; i < lines.length; i++) {
      const values = lines[i].split(',')
      const record = {}
      headers.forEach((header, index) => {
        record[header.trim()] = values[index]?.trim() || ''
      })
      record.id = Date.now() + i
      records.push(record)
    }
    
    const existingData = await readJsonFile(dataFile, [])
    const allData = [...existingData, ...records]
    await writeJsonFile(dataFile, allData)
    
    return {
      success: true,
      imported: records.length,
      total: allData.length,
      message: `تم استيراد ${records.length} سجل بنجاح`
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

// User management
ipcMain.handle('users:getAll', async () => {
  try {
    const users = await readJsonFile(usersFile, [
      {
        id: 1,
        username: 'admin',
        password: 'admin123', // In real app, this should be hashed
        role: 'admin',
        name: 'المدير العام',
        email: '<EMAIL>',
        active: true,
        createdAt: new Date().toISOString()
      }
    ])
    return { success: true, data: users }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

ipcMain.handle('users:authenticate', async (event, username, password) => {
  try {
    const users = await readJsonFile(usersFile, [])
    const user = users.find(u => u.username === username && u.password === password && u.active)
    
    if (user) {
      return {
        success: true,
        user: {
          id: user.id,
          username: user.username,
          role: user.role,
          name: user.name,
          email: user.email
        }
      }
    }
    
    return { success: false, error: 'اسم المستخدم أو كلمة المرور غير صحيحة' }
  } catch (error) {
    return { success: false, error: error.message }
  }
})

console.log('Electron app started successfully with hybrid database!')
console.log('Data directory:', dbPath)
