const Database = require('better-sqlite3')
const path = require('path')
const fs = require('fs')
const os = require('os')

class SQLiteManager {
  constructor() {
    this.db = null
    this.dbPath = path.join(os.homedir(), '.suspects-app', 'suspects.db')
    this.isInitialized = false
  }

  async initialize() {
    try {
      // Ensure directory exists
      const dbDir = path.dirname(this.dbPath)
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true })
      }

      // Open database connection
      this.db = new Database(this.dbPath)
      
      // Enable WAL mode for better performance
      this.db.pragma('journal_mode = WAL')
      this.db.pragma('synchronous = NORMAL')
      this.db.pragma('cache_size = 10000')
      this.db.pragma('temp_store = MEMORY')
      
      // Create tables if they don't exist
      await this.createTables()
      
      // Create indexes for performance
      await this.createIndexes()
      
      this.isInitialized = true
      console.log('✅ SQLite database initialized successfully')
      console.log('📍 Database location:', this.dbPath)
      
    } catch (error) {
      console.error('❌ Failed to initialize SQLite database:', error)
      throw error
    }
  }

  async createTables() {
    const tables = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        role TEXT NOT NULL,
        permissions TEXT, -- JSON string
        is_active BOOLEAN DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
      )`,

      // Suspects table - optimized for large datasets
      `CREATE TABLE IF NOT EXISTS suspects (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_number TEXT UNIQUE NOT NULL,
        full_name TEXT NOT NULL,
        id_number TEXT,
        address TEXT,
        phone TEXT,
        age INTEGER,
        nationality TEXT,
        profession TEXT,
        marital_status TEXT,
        seizures TEXT,
        arrest_date DATE,
        notes TEXT,
        is_released BOOLEAN DEFAULT 0,
        release_date DATE,
        is_transferred BOOLEAN DEFAULT 0,
        transfer_date DATE,
        created_by INTEGER,
        updated_by INTEGER,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (created_by) REFERENCES users(id),
        FOREIGN KEY (updated_by) REFERENCES users(id)
      )`,

      // Attachments table
      `CREATE TABLE IF NOT EXISTS attachments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        suspect_id INTEGER NOT NULL,
        field_name TEXT NOT NULL,
        file_name TEXT NOT NULL,
        original_name TEXT NOT NULL,
        file_type TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        file_path TEXT NOT NULL,
        uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (suspect_id) REFERENCES suspects(id) ON DELETE CASCADE
      )`,

      // Fields configuration table
      `CREATE TABLE IF NOT EXISTS fields (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        label TEXT NOT NULL,
        icon TEXT,
        input_type TEXT NOT NULL,
        is_required BOOLEAN DEFAULT 0,
        is_visible BOOLEAN DEFAULT 1,
        field_order INTEGER DEFAULT 0,
        options TEXT, -- JSON string for select options
        validation TEXT, -- JSON string for validation rules
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL, -- JSON string
        category TEXT DEFAULT 'general',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Audit log table
      `CREATE TABLE IF NOT EXISTS audit_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER,
        action TEXT NOT NULL,
        resource TEXT NOT NULL,
        resource_id INTEGER,
        details TEXT, -- JSON string
        ip_address TEXT,
        user_agent TEXT,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`,

      // Backup records table
      `CREATE TABLE IF NOT EXISTS backup_records (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_path TEXT,
        backup_type TEXT DEFAULT 'manual', -- 'auto' or 'manual'
        file_size INTEGER,
        checksum TEXT,
        status TEXT DEFAULT 'completed', -- 'completed', 'failed', 'in_progress'
        encrypted BOOLEAN DEFAULT 0,
        compressed BOOLEAN DEFAULT 0,
        description TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Full-text search table for suspects
      `CREATE VIRTUAL TABLE IF NOT EXISTS suspects_fts USING fts5(
        file_number,
        full_name,
        id_number,
        address,
        phone,
        nationality,
        profession,
        notes,
        content='suspects',
        content_rowid='id'
      )`
    ]

    for (const sql of tables) {
      this.db.exec(sql)
    }

    console.log('✅ Database tables created successfully')
  }

  async createIndexes() {
    const indexes = [
      // Users indexes
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
      'CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)',

      // Suspects indexes - critical for performance with large datasets
      'CREATE INDEX IF NOT EXISTS idx_suspects_file_number ON suspects(file_number)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_full_name ON suspects(full_name)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_id_number ON suspects(id_number)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_nationality ON suspects(nationality)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_arrest_date ON suspects(arrest_date)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_is_released ON suspects(is_released)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_is_transferred ON suspects(is_transferred)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_created_at ON suspects(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_updated_at ON suspects(updated_at)',
      
      // Composite indexes for common queries
      'CREATE INDEX IF NOT EXISTS idx_suspects_status ON suspects(is_released, is_transferred)',
      'CREATE INDEX IF NOT EXISTS idx_suspects_name_file ON suspects(full_name, file_number)',

      // Attachments indexes
      'CREATE INDEX IF NOT EXISTS idx_attachments_suspect_id ON attachments(suspect_id)',
      'CREATE INDEX IF NOT EXISTS idx_attachments_field_name ON attachments(field_name)',
      'CREATE INDEX IF NOT EXISTS idx_attachments_file_type ON attachments(file_type)',

      // Audit log indexes
      'CREATE INDEX IF NOT EXISTS idx_audit_user_id ON audit_log(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_audit_action ON audit_log(action)',
      'CREATE INDEX IF NOT EXISTS idx_audit_resource ON audit_log(resource)',
      'CREATE INDEX IF NOT EXISTS idx_audit_timestamp ON audit_log(timestamp)',

      // Backup records indexes
      'CREATE INDEX IF NOT EXISTS idx_backup_type ON backup_records(backup_type)',
      'CREATE INDEX IF NOT EXISTS idx_backup_status ON backup_records(status)',
      'CREATE INDEX IF NOT EXISTS idx_backup_created_at ON backup_records(created_at)'
    ]

    for (const sql of indexes) {
      this.db.exec(sql)
    }

    console.log('✅ Database indexes created successfully')
  }

  // FTS triggers for automatic indexing
  setupFTSTriggers() {
    const triggers = [
      // Insert trigger
      `CREATE TRIGGER IF NOT EXISTS suspects_fts_insert AFTER INSERT ON suspects BEGIN
        INSERT INTO suspects_fts(rowid, file_number, full_name, id_number, address, phone, nationality, profession, notes)
        VALUES (new.id, new.file_number, new.full_name, new.id_number, new.address, new.phone, new.nationality, new.profession, new.notes);
      END`,

      // Update trigger
      `CREATE TRIGGER IF NOT EXISTS suspects_fts_update AFTER UPDATE ON suspects BEGIN
        UPDATE suspects_fts SET 
          file_number = new.file_number,
          full_name = new.full_name,
          id_number = new.id_number,
          address = new.address,
          phone = new.phone,
          nationality = new.nationality,
          profession = new.profession,
          notes = new.notes
        WHERE rowid = new.id;
      END`,

      // Delete trigger
      `CREATE TRIGGER IF NOT EXISTS suspects_fts_delete AFTER DELETE ON suspects BEGIN
        DELETE FROM suspects_fts WHERE rowid = old.id;
      END`
    ]

    for (const sql of triggers) {
      this.db.exec(sql)
    }

    console.log('✅ FTS triggers created successfully')
  }

  // High-performance search methods
  searchSuspects(query, options = {}) {
    const {
      limit = 100,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      filters = {}
    } = options

    let sql = `
      SELECT s.*, 
             u1.name as created_by_name,
             u2.name as updated_by_name
      FROM suspects s
      LEFT JOIN users u1 ON s.created_by = u1.id
      LEFT JOIN users u2 ON s.updated_by = u2.id
    `

    const params = []
    const conditions = []

    // Full-text search
    if (query && query.trim()) {
      sql = `
        SELECT s.*, 
               u1.name as created_by_name,
               u2.name as updated_by_name,
               fts.rank
        FROM suspects_fts fts
        JOIN suspects s ON s.id = fts.rowid
        LEFT JOIN users u1 ON s.created_by = u1.id
        LEFT JOIN users u2 ON s.updated_by = u2.id
        WHERE suspects_fts MATCH ?
      `
      params.push(query.trim())
    }

    // Apply filters
    if (filters.nationality) {
      conditions.push('s.nationality = ?')
      params.push(filters.nationality)
    }

    if (filters.isReleased !== undefined) {
      conditions.push('s.is_released = ?')
      params.push(filters.isReleased ? 1 : 0)
    }

    if (filters.isTransferred !== undefined) {
      conditions.push('s.is_transferred = ?')
      params.push(filters.isTransferred ? 1 : 0)
    }

    if (filters.dateFrom) {
      conditions.push('s.arrest_date >= ?')
      params.push(filters.dateFrom)
    }

    if (filters.dateTo) {
      conditions.push('s.arrest_date <= ?')
      params.push(filters.dateTo)
    }

    // Add conditions to SQL
    if (conditions.length > 0) {
      const whereClause = query && query.trim() ? 'AND' : 'WHERE'
      sql += ` ${whereClause} ${conditions.join(' AND ')}`
    }

    // Add sorting
    const validSortColumns = ['file_number', 'full_name', 'arrest_date', 'created_at', 'updated_at']
    const sortColumn = validSortColumns.includes(sortBy) ? sortBy : 'created_at'
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC'
    
    if (query && query.trim()) {
      sql += ` ORDER BY fts.rank, s.${sortColumn} ${order}`
    } else {
      sql += ` ORDER BY s.${sortColumn} ${order}`
    }

    // Add pagination
    sql += ` LIMIT ? OFFSET ?`
    params.push(limit, offset)

    try {
      const stmt = this.db.prepare(sql)
      const results = stmt.all(...params)
      
      // Get total count for pagination
      let countSql = `SELECT COUNT(*) as total FROM suspects s`
      let countParams = []
      
      if (query && query.trim()) {
        countSql = `SELECT COUNT(*) as total FROM suspects_fts WHERE suspects_fts MATCH ?`
        countParams.push(query.trim())
      }
      
      if (conditions.length > 0) {
        const whereClause = query && query.trim() ? 'AND EXISTS (SELECT 1 FROM suspects WHERE id = suspects_fts.rowid AND' : 'WHERE'
        countSql += ` ${whereClause} ${conditions.join(' AND ')}`
        if (query && query.trim()) {
          countSql += ')'
        }
        countParams.push(...params.slice(query ? 1 : 0, -2)) // Exclude limit and offset
      }
      
      const countStmt = this.db.prepare(countSql)
      const { total } = countStmt.get(...countParams)

      return {
        results,
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    } catch (error) {
      console.error('Search error:', error)
      throw error
    }
  }

  close() {
    if (this.db) {
      this.db.close()
      this.db = null
      this.isInitialized = false
      console.log('✅ Database connection closed')
    }
  }

  // Utility method to get database info
  getDatabaseInfo() {
    if (!this.isInitialized) return null

    const info = this.db.prepare(`
      SELECT 
        (SELECT COUNT(*) FROM users) as users_count,
        (SELECT COUNT(*) FROM suspects) as suspects_count,
        (SELECT COUNT(*) FROM attachments) as attachments_count,
        (SELECT COUNT(*) FROM audit_log) as audit_log_count
    `).get()

    return {
      ...info,
      dbPath: this.dbPath,
      dbSize: fs.existsSync(this.dbPath) ? fs.statSync(this.dbPath).size : 0
    }
  }
}

module.exports = SQLiteManager
