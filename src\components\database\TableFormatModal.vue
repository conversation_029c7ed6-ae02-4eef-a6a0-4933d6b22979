<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-secondary-800">
          <i class="fas fa-palette ml-2"></i>
          تنسيق الجدول الكامل
        </h3>
        <button @click="$emit('close')" class="text-secondary-400 hover:text-secondary-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- القسم الأول: تنسيق صف عناوين الأعمدة والإطار الخارجي -->
        <div class="neumorphic-card bg-secondary-50">
          <h4 class="text-lg font-semibold text-secondary-800 mb-4 flex items-center">
            <i class="fas fa-table ml-2"></i>
            تنسيق صف العناوين والإطار الخارجي
          </h4>

          <!-- أ) تنسيق صف العناوين -->
          <div class="space-y-4 mb-6">
            <h5 class="text-md font-medium text-secondary-700 border-b border-secondary-200 pb-2">
              أ) تنسيق صف العناوين
            </h5>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- لون نصوص العناوين -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون نصوص العناوين
                </label>
                <input
                  v-model="formData.headerTextColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- حجم الخط -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  حجم الخط (px)
                </label>
                <select v-model="formData.headerFontSize" class="neumorphic-select w-full">
                  <option v-for="size in fontSizes" :key="size" :value="size">{{ size }}px</option>
                </select>
              </div>

              <!-- سمك الخط -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  سمك الخط
                </label>
                <select v-model="formData.headerFontWeight" class="neumorphic-select w-full">
                  <option value="normal">عادي</option>
                  <option value="bold">عريض</option>
                </select>
              </div>

              <!-- لون الخلفية -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون الخلفية
                </label>
                <input
                  v-model="formData.headerBackgroundColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- لون الحدود الداخلية -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون الحدود الداخلية
                </label>
                <input
                  v-model="formData.headerBorderColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- سمك الحدود -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  سمك الحدود (px)
                </label>
                <select v-model="formData.headerBorderWidth" class="neumorphic-select w-full">
                  <option v-for="width in borderWidths" :key="width" :value="width">{{ width }}px</option>
                </select>
              </div>

              <!-- الحدود السفلية -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون الحدود السفلية
                </label>
                <input
                  v-model="formData.headerBottomBorderColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- سمك الحدود السفلية -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  سمك الحدود السفلية (px)
                </label>
                <select v-model="formData.headerBottomBorderWidth" class="neumorphic-select w-full">
                  <option v-for="width in borderWidths" :key="width" :value="width">{{ width }}px</option>
                </select>
              </div>
            </div>
          </div>

          <!-- ب) تنسيق الإطار الخارجي -->
          <div class="space-y-4">
            <h5 class="text-md font-medium text-secondary-700 border-b border-secondary-200 pb-2">
              ب) تنسيق الإطار الخارجي للجدول
            </h5>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- لون الإطار الخارجي -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون الإطار الخارجي
                </label>
                <input
                  v-model="formData.tableBorderColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- سمك الإطار الخارجي -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  سمك الإطار الخارجي (px)
                </label>
                <select v-model="formData.tableBorderWidth" class="neumorphic-select w-full">
                  <option v-for="width in borderWidths" :key="width" :value="width">{{ width }}px</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- القسم الثاني: تنسيق صفوف البيانات وعناوين المجموعات -->
        <div class="neumorphic-card bg-secondary-50">
          <h4 class="text-lg font-semibold text-secondary-800 mb-4 flex items-center">
            <i class="fas fa-list ml-2"></i>
            تنسيق صفوف البيانات وعناوين المجموعات
          </h4>

          <!-- أ) تنسيق صفوف البيانات -->
          <div class="space-y-4 mb-6">
            <h5 class="text-md font-medium text-secondary-700 border-b border-secondary-200 pb-2">
              أ) تنسيق صفوف البيانات
            </h5>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- لون خط نصوص البيانات -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون خط نصوص البيانات
                </label>
                <input
                  v-model="formData.cellTextColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- حجم الخط -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  حجم الخط (px)
                </label>
                <select v-model="formData.cellFontSize" class="neumorphic-select w-full">
                  <option v-for="size in fontSizes" :key="size" :value="size">{{ size }}px</option>
                </select>
              </div>

              <!-- سمك الخط -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  سمك الخط
                </label>
                <select v-model="formData.cellFontWeight" class="neumorphic-select w-full">
                  <option value="normal">عادي</option>
                  <option value="bold">عريض</option>
                </select>
              </div>

              <!-- لون الحدود الداخلية -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون الحدود الداخلية
                </label>
                <input
                  v-model="formData.cellBorderColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- سمك الحدود -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  سمك الحدود (px)
                </label>
                <select v-model="formData.cellBorderWidth" class="neumorphic-select w-full">
                  <option v-for="width in borderWidths" :key="width" :value="width">{{ width }}px</option>
                </select>
              </div>

              <!-- المحاذاة -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  المحاذاة
                </label>
                <select v-model="formData.cellTextAlign" class="neumorphic-select w-full">
                  <option value="right">يمين</option>
                  <option value="center">وسط</option>
                  <option value="left">يسار</option>
                </select>
              </div>

              <!-- لون خلفية الخلايا -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون خلفية الخلايا
                </label>
                <input
                  v-model="formData.cellBackgroundColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>
            </div>
          </div>

          <!-- ب) تنسيق صف عناوين المجموعات -->
          <div class="space-y-4">
            <h5 class="text-md font-medium text-secondary-700 border-b border-secondary-200 pb-2">
              ب) تنسيق صف عناوين المجموعات
            </h5>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- لون خط عناوين المجموعات -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون خط عناوين المجموعات
                </label>
                <input
                  v-model="formData.groupHeaderTextColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- حجم الخط -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  حجم الخط (px)
                </label>
                <select v-model="formData.groupHeaderFontSize" class="neumorphic-select w-full">
                  <option v-for="size in fontSizes" :key="size" :value="size">{{ size }}px</option>
                </select>
              </div>

              <!-- سمك الخط -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  سمك الخط
                </label>
                <select v-model="formData.groupHeaderFontWeight" class="neumorphic-select w-full">
                  <option value="normal">عادي</option>
                  <option value="bold">عريض</option>
                </select>
              </div>

              <!-- لون الخلفية -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون الخلفية
                </label>
                <input
                  v-model="formData.groupHeaderBackgroundColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- المحاذاة -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  المحاذاة
                </label>
                <select v-model="formData.groupHeaderTextAlign" class="neumorphic-select w-full">
                  <option value="right">يمين</option>
                  <option value="center">وسط</option>
                  <option value="left">يسار</option>
                </select>
              </div>

              <!-- لون الحد السفلي -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون الحد السفلي
                </label>
                <input
                  v-model="formData.groupHeaderBottomBorderColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- سمك الحد السفلي -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  سمك الحد السفلي (px)
                </label>
                <select v-model="formData.groupHeaderBottomBorderWidth" class="neumorphic-select w-full">
                  <option v-for="width in borderWidths" :key="width" :value="width">{{ width }}px</option>
                </select>
              </div>

              <!-- لون الحد العلوي -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  لون الحد العلوي
                </label>
                <input
                  v-model="formData.groupHeaderTopBorderColor"
                  type="color"
                  class="w-full h-10 rounded-neumorphic border-0 cursor-pointer"
                />
              </div>

              <!-- سمك الحد العلوي -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">
                  سمك الحد العلوي (px)
                </label>
                <select v-model="formData.groupHeaderTopBorderWidth" class="neumorphic-select w-full">
                  <option v-for="width in borderWidths" :key="width" :value="width">{{ width }}px</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end gap-3 pt-6 mt-6 border-t border-secondary-200">
        <button
          @click="resetToDefaults"
          class="neumorphic-button text-warning-600 hover:text-warning-700"
        >
          <i class="fas fa-undo ml-2"></i>
          إعادة تعيين
        </button>
        <button
          @click="$emit('close')"
          class="neumorphic-button text-secondary-600 hover:text-secondary-700"
        >
          إلغاء
        </button>
        <button
          @click="saveSettings"
          class="neumorphic-button text-success-600 hover:text-success-700"
        >
          <i class="fas fa-save ml-2"></i>
          حفظ الإعدادات
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted } from 'vue'
import { useDatabaseStore } from '@/stores/database'

// Props
interface Props {
  tabId: string
}

const props = defineProps<Props>()

// Store
const databaseStore = useDatabaseStore()

// Emits
const emit = defineEmits<{
  close: []
  save: [settings: TableFormatSettings]
}>()

// Interface for table format settings
interface TableFormatSettings {
  // Header formatting
  headerTextColor: string
  headerFontSize: number
  headerFontWeight: 'normal' | 'bold'
  headerBackgroundColor: string
  headerBorderColor: string
  headerBorderWidth: number
  headerBottomBorderColor: string
  headerBottomBorderWidth: number
  
  // Table border
  tableBorderColor: string
  tableBorderWidth: number
  
  // Cell formatting
  cellTextColor: string
  cellFontSize: number
  cellFontWeight: 'normal' | 'bold'
  cellBorderColor: string
  cellBorderWidth: number
  cellTextAlign: 'left' | 'center' | 'right'
  cellBackgroundColor: string
  
  // Group header formatting
  groupHeaderTextColor: string
  groupHeaderFontSize: number
  groupHeaderFontWeight: 'normal' | 'bold'
  groupHeaderBackgroundColor: string
  groupHeaderTextAlign: 'left' | 'center' | 'right'
  groupHeaderBottomBorderColor: string
  groupHeaderBottomBorderWidth: number
  groupHeaderTopBorderColor: string
  groupHeaderTopBorderWidth: number
}

// Form data
const formData = reactive<TableFormatSettings>({
  // Header formatting - default values
  headerTextColor: '#334155',
  headerFontSize: 14,
  headerFontWeight: 'bold',
  headerBackgroundColor: '#f1f5f9',
  headerBorderColor: '#e2e8f0',
  headerBorderWidth: 1,
  headerBottomBorderColor: '#cbd5e1',
  headerBottomBorderWidth: 2,
  
  // Table border
  tableBorderColor: '#94a3b8',
  tableBorderWidth: 2,
  
  // Cell formatting
  cellTextColor: '#475569',
  cellFontSize: 14,
  cellFontWeight: 'normal',
  cellBorderColor: '#e2e8f0',
  cellBorderWidth: 1,
  cellTextAlign: 'right',
  cellBackgroundColor: '#ffffff',
  
  // Group header formatting
  groupHeaderTextColor: '#1e40af',
  groupHeaderFontSize: 16,
  groupHeaderFontWeight: 'bold',
  groupHeaderBackgroundColor: '#dbeafe',
  groupHeaderTextAlign: 'right',
  groupHeaderBottomBorderColor: '#3b82f6',
  groupHeaderBottomBorderWidth: 2,
  groupHeaderTopBorderColor: '#3b82f6',
  groupHeaderTopBorderWidth: 1
})

// Options
const fontSizes = [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 22, 24, 26, 28, 30]
const borderWidths = [0, 1, 2, 3, 4, 5]

// Methods
function resetToDefaults() {
  Object.assign(formData, {
    // Header formatting - default values
    headerTextColor: '#334155',
    headerFontSize: 14,
    headerFontWeight: 'bold',
    headerBackgroundColor: '#f1f5f9',
    headerBorderColor: '#e2e8f0',
    headerBorderWidth: 1,
    headerBottomBorderColor: '#cbd5e1',
    headerBottomBorderWidth: 2,
    
    // Table border
    tableBorderColor: '#94a3b8',
    tableBorderWidth: 2,
    
    // Cell formatting
    cellTextColor: '#475569',
    cellFontSize: 14,
    cellFontWeight: 'normal',
    cellBorderColor: '#e2e8f0',
    cellBorderWidth: 1,
    cellTextAlign: 'right',
    cellBackgroundColor: '#ffffff',
    
    // Group header formatting
    groupHeaderTextColor: '#1e40af',
    groupHeaderFontSize: 16,
    groupHeaderFontWeight: 'bold',
    groupHeaderBackgroundColor: '#dbeafe',
    groupHeaderTextAlign: 'right',
    groupHeaderBottomBorderColor: '#3b82f6',
    groupHeaderBottomBorderWidth: 2,
    groupHeaderTopBorderColor: '#3b82f6',
    groupHeaderTopBorderWidth: 1
  })
}

function saveSettings() {
  emit('save', { ...formData })
}

function loadSavedSettings() {
  const tab = databaseStore.getTabById(props.tabId)
  if (tab && tab.settings?.tableFormatting) {
    const saved = tab.settings.tableFormatting

    // Load saved values into form
    if (saved.headerTextColor) formData.headerTextColor = saved.headerTextColor
    if (saved.headerFontSize) formData.headerFontSize = saved.headerFontSize
    if (saved.headerFontWeight) formData.headerFontWeight = saved.headerFontWeight
    if (saved.headerBackgroundColor) formData.headerBackgroundColor = saved.headerBackgroundColor
    if (saved.headerBorderColor) formData.headerBorderColor = saved.headerBorderColor
    if (saved.headerBorderWidth) formData.headerBorderWidth = saved.headerBorderWidth
    if (saved.headerBottomBorderColor) formData.headerBottomBorderColor = saved.headerBottomBorderColor
    if (saved.headerBottomBorderWidth) formData.headerBottomBorderWidth = saved.headerBottomBorderWidth

    if (saved.tableBorderColor) formData.tableBorderColor = saved.tableBorderColor
    if (saved.tableBorderWidth) formData.tableBorderWidth = saved.tableBorderWidth

    if (saved.cellTextColor) formData.cellTextColor = saved.cellTextColor
    if (saved.cellFontSize) formData.cellFontSize = saved.cellFontSize
    if (saved.cellFontWeight) formData.cellFontWeight = saved.cellFontWeight
    if (saved.cellBorderColor) formData.cellBorderColor = saved.cellBorderColor
    if (saved.cellBorderWidth) formData.cellBorderWidth = saved.cellBorderWidth
    if (saved.cellTextAlign) formData.cellTextAlign = saved.cellTextAlign
    if (saved.cellBackgroundColor) formData.cellBackgroundColor = saved.cellBackgroundColor

    if (saved.groupHeaderTextColor) formData.groupHeaderTextColor = saved.groupHeaderTextColor
    if (saved.groupHeaderFontSize) formData.groupHeaderFontSize = saved.groupHeaderFontSize
    if (saved.groupHeaderFontWeight) formData.groupHeaderFontWeight = saved.groupHeaderFontWeight
    if (saved.groupHeaderBackgroundColor) formData.groupHeaderBackgroundColor = saved.groupHeaderBackgroundColor
    if (saved.groupHeaderTextAlign) formData.groupHeaderTextAlign = saved.groupHeaderTextAlign
    if (saved.groupHeaderBottomBorderColor) formData.groupHeaderBottomBorderColor = saved.groupHeaderBottomBorderColor
    if (saved.groupHeaderBottomBorderWidth) formData.groupHeaderBottomBorderWidth = saved.groupHeaderBottomBorderWidth
    if (saved.groupHeaderTopBorderColor) formData.groupHeaderTopBorderColor = saved.groupHeaderTopBorderColor
    if (saved.groupHeaderTopBorderWidth) formData.groupHeaderTopBorderWidth = saved.groupHeaderTopBorderWidth
  }
}

// Load saved settings when component mounts
onMounted(() => {
  loadSavedSettings()
})
</script>
