<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🔄 إعادة تعيين قاعدة البيانات</title>
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      direction: rtl;
      text-align: center;
      margin: 50px;
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
      min-height: 100vh;
      color: white;
    }

    .container {
      max-width: 600px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.1);
      padding: 40px;
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .title {
      font-size: 36px;
      font-weight: bold;
      margin-bottom: 20px;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .warning {
      background: rgba(255, 255, 255, 0.2);
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 15px;
      padding: 25px;
      margin: 25px 0;
      font-size: 18px;
      line-height: 1.6;
    }

    .reset-button {
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      color: white;
      padding: 20px 40px;
      border: none;
      border-radius: 25px;
      font-weight: bold;
      font-size: 18px;
      cursor: pointer;
      box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
      transition: all 0.3s;
      margin: 20px;
    }

    .reset-button:hover {
      transform: scale(1.1);
      box-shadow: 0 12px 24px rgba(0, 0, 0, 0.4);
    }

    .success {
      background: linear-gradient(135deg, #10b981, #059669);
      border-radius: 15px;
      padding: 25px;
      margin: 25px 0;
      font-size: 18px;
      display: none;
    }

    .loading {
      display: none;
      margin: 20px 0;
    }

    .spinner {
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top: 4px solid white;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 0 auto;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">🔄 إعادة تعيين قاعدة البيانات</h1>
    
    <div class="warning">
      <h3>⚠️ تحذير مهم!</h3>
      <p>هذا الإجراء سيحذف قاعدة البيانات الحالية بالكامل وينشئ قاعدة بيانات جديدة بالفهارس الصحيحة.</p>
      <p><strong>سيتم فقدان جميع البيانات الموجودة!</strong></p>
      <p>هذا ضروري لإصلاح مشكلة فهرسة username في قاعدة البيانات.</p>
    </div>

    <button class="reset-button" onclick="resetDatabase()">
      🗑️ حذف قاعدة البيانات وإعادة إنشائها
    </button>

    <div class="loading" id="loading">
      <div class="spinner"></div>
      <p>جاري إعادة تعيين قاعدة البيانات...</p>
    </div>

    <div class="success" id="success">
      <h3>✅ تم بنجاح!</h3>
      <p>تم حذف قاعدة البيانات القديمة وإنشاء قاعدة بيانات جديدة بالفهارس الصحيحة.</p>
      <p>يمكنك الآن العودة للتطبيق وتسجيل الدخول.</p>
      <a href="http://localhost:5175" style="color: white; text-decoration: underline;">العودة للتطبيق</a>
    </div>
  </div>

  <script>
    async function resetDatabase() {
      const confirmed = confirm('هل أنت متأكد من حذف قاعدة البيانات؟ سيتم فقدان جميع البيانات!');
      if (!confirmed) return;

      document.getElementById('loading').style.display = 'block';
      document.querySelector('.reset-button').style.display = 'none';

      try {
        // حذف قاعدة البيانات
        const deleteRequest = indexedDB.deleteDatabase('SuspectsDatabase');
        
        deleteRequest.onsuccess = function() {
          console.log('✅ تم حذف قاعدة البيانات بنجاح');
          
          // إظهار رسالة النجاح
          document.getElementById('loading').style.display = 'none';
          document.getElementById('success').style.display = 'block';
          
          // إعادة تحميل الصفحة بعد 3 ثوان
          setTimeout(() => {
            window.location.href = 'http://localhost:5175';
          }, 3000);
        };
        
        deleteRequest.onerror = function() {
          console.error('❌ فشل في حذف قاعدة البيانات');
          alert('فشل في حذف قاعدة البيانات');
          document.getElementById('loading').style.display = 'none';
          document.querySelector('.reset-button').style.display = 'block';
        };
        
        deleteRequest.onblocked = function() {
          console.warn('⚠️ حذف قاعدة البيانات محجوب - أغلق جميع علامات التبويب الأخرى');
          alert('يرجى إغلاق جميع علامات التبويب الأخرى للتطبيق ثم المحاولة مرة أخرى');
          document.getElementById('loading').style.display = 'none';
          document.querySelector('.reset-button').style.display = 'block';
        };

      } catch (error) {
        console.error('Error resetting database:', error);
        alert('حدث خطأ أثناء إعادة تعيين قاعدة البيانات');
        document.getElementById('loading').style.display = 'none';
        document.querySelector('.reset-button').style.display = 'block';
      }
    }

    console.log('🔄 صفحة إعادة تعيين قاعدة البيانات جاهزة');
    console.log('⚠️ تحذير: هذا الإجراء سيحذف جميع البيانات!');
  </script>
</body>
</html>
