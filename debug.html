<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأيقونات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            direction: rtl;
        }
        .test-icon {
            font-size: 24px;
            margin: 10px;
            color: #333;
        }
        .test-button {
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>اختبار الأيقونات والعناصر</h1>
    
    <h2>اختبار Font Awesome:</h2>
    <i class="fas fa-plus test-icon"></i> إضافة
    <i class="fas fa-database test-icon"></i> قاعدة بيانات
    <i class="fas fa-file-excel test-icon"></i> Excel
    <i class="fas fa-cog test-icon"></i> إعدادات
    
    <h2>اختبار الأزرار:</h2>
    <button class="test-button">
        <i class="fas fa-plus"></i> إضافة تبويب
    </button>
    <button class="test-button">
        <i class="fas fa-file-excel"></i> استيراد Excel
    </button>
    
    <h2>اختبار JavaScript:</h2>
    <button onclick="testJS()">اختبار JavaScript</button>
    <div id="result"></div>
    
    <script>
        function testJS() {
            document.getElementById('result').innerHTML = 'JavaScript يعمل بشكل صحيح!';
        }
        
        // Test if Font Awesome is loaded
        window.addEventListener('load', function() {
            const icon = document.querySelector('.fas');
            const computed = window.getComputedStyle(icon, ':before');
            const content = computed.getPropertyValue('content');
            
            if (content && content !== 'none' && content !== '""') {
                console.log('✅ Font Awesome محمل بشكل صحيح');
            } else {
                console.log('❌ Font Awesome غير محمل');
            }
        });
    </script>
</body>
</html>
