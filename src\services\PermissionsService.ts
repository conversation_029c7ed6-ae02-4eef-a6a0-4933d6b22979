import type { 
  User, 
  UserPermissions, 
  AppSection, 
  AppTab, 
  AppAction,
  APP_SECTIONS,
  DEFAULT_ROLES
} from '@/types'

export class PermissionsService {
  private static instance: PermissionsService
  private appSections: AppSection[] = []

  constructor() {
    // تحميل هيكل التطبيق
    this.loadAppStructure()
  }

  static getInstance(): PermissionsService {
    if (!PermissionsService.instance) {
      PermissionsService.instance = new PermissionsService()
    }
    return PermissionsService.instance
  }

  private loadAppStructure() {
    // تحميل هيكل التطبيق من types
    this.appSections = [
      {
        id: 'suspects',
        name: 'suspects',
        displayName: 'بيانات المتهمين',
        icon: 'fas fa-users',
        tabs: [
          {
            id: 'add-suspect',
            name: 'add-suspect',
            displayName: 'إضافة بيانات متهم',
            icon: 'fas fa-user-plus',
            actions: [
              { id: 'add-suspect-btn', name: 'add-suspect', displayName: 'إضافة متهم', icon: 'fas fa-plus', type: 'button' },
              { id: 'save-suspect-btn', name: 'save-suspect', displayName: 'حفظ البيانات', icon: 'fas fa-save', type: 'button' },
              { id: 'clear-form-btn', name: 'clear-form', displayName: 'مسح النموذج', icon: 'fas fa-eraser', type: 'button' }
            ]
          },
          {
            id: 'review-status',
            name: 'review-status',
            displayName: 'مراجعة الحالة الراهنة',
            icon: 'fas fa-clipboard-check',
            actions: [
              { id: 'edit-suspect-btn', name: 'edit-suspect', displayName: 'تعديل البيانات', icon: 'fas fa-edit', type: 'button' },
              { id: 'delete-suspect-btn', name: 'delete-suspect', displayName: 'حذف المتهم', icon: 'fas fa-trash', type: 'button' },
              { id: 'export-suspect-btn', name: 'export-suspect', displayName: 'تصدير البيانات', icon: 'fas fa-download', type: 'button' }
            ]
          },
          {
            id: 'summary-charts',
            name: 'summary-charts',
            displayName: 'الرسوم البيانية التلخيصية',
            icon: 'fas fa-chart-bar',
            actions: [
              { id: 'view-charts', name: 'view-charts', displayName: 'عرض الرسوم البيانية', icon: 'fas fa-eye', type: 'feature' },
              { id: 'export-charts', name: 'export-charts', displayName: 'تصدير الرسوم البيانية', icon: 'fas fa-download', type: 'button' }
            ]
          },
          {
            id: 'card-view',
            name: 'card-view',
            displayName: 'عرض البطاقات',
            icon: 'fas fa-id-card',
            actions: [
              { id: 'view-cards', name: 'view-cards', displayName: 'عرض البطاقات', icon: 'fas fa-eye', type: 'feature' },
              { id: 'export-cards', name: 'export-cards', displayName: 'تصدير البطاقات', icon: 'fas fa-download', type: 'button' }
            ]
          }
        ]
      },
      {
        id: 'database',
        name: 'database',
        displayName: 'قاعدة البيانات',
        icon: 'fas fa-database',
        tabs: [
          {
            id: 'database-tabs',
            name: 'database-tabs',
            displayName: 'تبويبات قاعدة البيانات',
            icon: 'fas fa-table',
            actions: [
              { id: 'import-data', name: 'import-data', displayName: 'استيراد البيانات', icon: 'fas fa-upload', type: 'button' },
              { id: 'export-data', name: 'export-data', displayName: 'تصدير البيانات', icon: 'fas fa-download', type: 'button' },
              { id: 'edit-cells', name: 'edit-cells', displayName: 'تعديل محتوى الخلايا', icon: 'fas fa-edit', type: 'feature' },
              { id: 'delete-tabs', name: 'delete-tabs', displayName: 'حذف التبويبات', icon: 'fas fa-times', type: 'button' },
              { id: 'search-matching', name: 'search-matching', displayName: 'البحث والمطابقة', icon: 'fas fa-search', type: 'button' },
              { id: 'format-table', name: 'format-table', displayName: 'تنسيق الجداول', icon: 'fas fa-palette', type: 'button' },
              { id: 'highlight-content', name: 'highlight-content', displayName: 'تمييز المحتوى المهم', icon: 'fas fa-highlighter', type: 'button' }
            ]
          }
        ]
      },
      {
        id: 'settings',
        name: 'settings',
        displayName: 'الإعدادات',
        icon: 'fas fa-cog',
        tabs: [
          {
            id: 'theme-customization',
            name: 'theme-customization',
            displayName: 'تخصيص المظهر',
            icon: 'fas fa-palette',
            actions: [
              { id: 'change-theme', name: 'change-theme', displayName: 'تغيير المظهر', icon: 'fas fa-paint-brush', type: 'feature' }
            ]
          },
          {
            id: 'fields-configuration',
            name: 'fields-configuration',
            displayName: 'إعدادات الحقول',
            icon: 'fas fa-list',
            actions: [
              { id: 'add-field', name: 'add-field', displayName: 'إضافة حقل', icon: 'fas fa-plus', type: 'button' },
              { id: 'edit-field', name: 'edit-field', displayName: 'تعديل حقل', icon: 'fas fa-edit', type: 'button' },
              { id: 'delete-field', name: 'delete-field', displayName: 'حذف حقل', icon: 'fas fa-trash', type: 'button' }
            ]
          },
          {
            id: 'backup-restore',
            name: 'backup-restore',
            displayName: 'النسخ الاحتياطي والاستعادة',
            icon: 'fas fa-shield-alt',
            actions: [
              { id: 'create-backup', name: 'create-backup', displayName: 'إنشاء نسخة احتياطية', icon: 'fas fa-save', type: 'button' },
              { id: 'restore-backup', name: 'restore-backup', displayName: 'استعادة نسخة احتياطية', icon: 'fas fa-upload', type: 'button' },
              { id: 'auto-backup-settings', name: 'auto-backup-settings', displayName: 'إعدادات النسخ التلقائي', icon: 'fas fa-cog', type: 'feature' }
            ]
          },
          {
            id: 'user-management',
            name: 'user-management',
            displayName: 'إدارة المستخدمين',
            icon: 'fas fa-users-cog',
            actions: [
              { id: 'add-user', name: 'add-user', displayName: 'إضافة مستخدم', icon: 'fas fa-user-plus', type: 'button' },
              { id: 'edit-user', name: 'edit-user', displayName: 'تعديل مستخدم', icon: 'fas fa-user-edit', type: 'button' },
              { id: 'toggle-user-status', name: 'toggle-user-status', displayName: 'تفعيل/إلغاء تفعيل المستخدم', icon: 'fas fa-user-slash', type: 'button' },
              { id: 'reset-password', name: 'reset-password', displayName: 'إعادة تعيين كلمة المرور', icon: 'fas fa-key', type: 'button' },
              { id: 'manage-permissions', name: 'manage-permissions', displayName: 'إدارة الصلاحيات', icon: 'fas fa-shield-alt', type: 'feature' }
            ]
          },
          {
            id: 'developer-settings',
            name: 'developer-settings',
            displayName: 'إعدادات المطور',
            icon: 'fas fa-code',
            actions: [
              { id: 'edit-developer-info', name: 'edit-developer-info', displayName: 'تعديل معلومات المطور', icon: 'fas fa-edit', type: 'button' }
            ]
          }
        ]
      }
    ]
  }

  /**
   * الحصول على هيكل التطبيق
   */
  getAppStructure(): AppSection[] {
    return this.appSections
  }

  /**
   * الحصول على قسم معين
   */
  getSection(sectionId: string): AppSection | undefined {
    return this.appSections.find(section => section.id === sectionId)
  }

  /**
   * الحصول على تبويب معين
   */
  getTab(sectionId: string, tabId: string): AppTab | undefined {
    const section = this.getSection(sectionId)
    return section?.tabs.find(tab => tab.id === tabId)
  }

  /**
   * الحصول على إجراء معين
   */
  getAction(sectionId: string, tabId: string, actionId: string): AppAction | undefined {
    const tab = this.getTab(sectionId, tabId)
    return tab?.actions.find(action => action.id === actionId)
  }

  /**
   * إنشاء صلاحيات افتراضية للمستخدم
   */
  createDefaultPermissions(userId: string, roleId: string): UserPermissions {
    const permissions: UserPermissions = {
      userId,
      sections: {}
    }

    // تحديد الصلاحيات حسب الدور
    this.appSections.forEach(section => {
      permissions.sections[section.id] = {
        allowed: this.isSectionAllowedForRole(section.id, roleId),
        tabs: {}
      }

      section.tabs.forEach(tab => {
        permissions.sections[section.id].tabs[tab.id] = {
          allowed: this.isTabAllowedForRole(section.id, tab.id, roleId),
          actions: {}
        }

        tab.actions.forEach(action => {
          permissions.sections[section.id].tabs[tab.id].actions[action.id] = 
            this.isActionAllowedForRole(section.id, tab.id, action.id, roleId)
        })
      })
    })

    return permissions
  }

  /**
   * التحقق من صلاحية القسم للدور
   */
  private isSectionAllowedForRole(sectionId: string, roleId: string): boolean {
    if (roleId === 'admin') return true
    
    switch (sectionId) {
      case 'suspects':
        return ['supervisor', 'officer', 'investigator', 'viewer'].includes(roleId)
      case 'database':
        return ['supervisor', 'officer', 'investigator', 'viewer'].includes(roleId)
      case 'settings':
        return ['admin', 'supervisor'].includes(roleId)
      default:
        return false
    }
  }

  /**
   * التحقق من صلاحية التبويب للدور
   */
  private isTabAllowedForRole(sectionId: string, tabId: string, roleId: string): boolean {
    if (roleId === 'admin') return true
    
    // إذا لم يكن لديه صلاحية للقسم، فلا يمكنه الوصول للتبويب
    if (!this.isSectionAllowedForRole(sectionId, roleId)) return false
    
    if (sectionId === 'settings') {
      if (tabId === 'user-management') {
        return roleId === 'admin'
      }
      return ['admin', 'supervisor'].includes(roleId)
    }
    
    return true
  }

  /**
   * التحقق من صلاحية الإجراء للدور
   */
  private isActionAllowedForRole(sectionId: string, tabId: string, actionId: string, roleId: string): boolean {
    if (roleId === 'admin') return true
    
    // إذا لم يكن لديه صلاحية للتبويب، فلا يمكنه تنفيذ الإجراء
    if (!this.isTabAllowedForRole(sectionId, tabId, roleId)) return false
    
    // صلاحيات خاصة بالأدوار
    if (roleId === 'viewer') {
      // المراقب يمكنه فقط العرض
      const viewOnlyActions = ['view-charts', 'view-cards']
      return viewOnlyActions.includes(actionId) || actionId.includes('view')
    }
    
    if (roleId === 'investigator') {
      // المحقق يمكنه العرض والتعديل المحدود
      const investigatorActions = ['edit-suspect-btn', 'view-charts', 'view-cards']
      return investigatorActions.includes(actionId) || actionId.includes('view')
    }
    
    if (roleId === 'officer') {
      // الضابط يمكنه معظم العمليات عدا الحذف
      const restrictedActions = ['delete-suspect-btn', 'delete-tabs', 'delete-field']
      return !restrictedActions.includes(actionId)
    }
    
    return true
  }

  /**
   * التحقق من صلاحية المستخدم لقسم معين
   */
  canAccessSection(userPermissions: UserPermissions, sectionId: string): boolean {
    return userPermissions.sections[sectionId]?.allowed || false
  }

  /**
   * التحقق من صلاحية المستخدم لتبويب معين
   */
  canAccessTab(userPermissions: UserPermissions, sectionId: string, tabId: string): boolean {
    return userPermissions.sections[sectionId]?.tabs[tabId]?.allowed || false
  }

  /**
   * التحقق من صلاحية المستخدم لإجراء معين
   */
  canPerformAction(userPermissions: UserPermissions, sectionId: string, tabId: string, actionId: string): boolean {
    return userPermissions.sections[sectionId]?.tabs[tabId]?.actions[actionId] || false
  }
}

// تصدير instance واحد
export const permissionsService = PermissionsService.getInstance()
