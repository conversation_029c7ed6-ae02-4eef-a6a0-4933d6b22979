<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-md w-full">
      <!-- Header -->
      <div class="flex items-center gap-3 mb-4">
        <div class="neumorphic-icon text-warning-600">
          <i class="fas fa-exclamation-triangle"></i>
        </div>
        <h2 class="text-lg font-bold text-secondary-800">{{ title }}</h2>
      </div>

      <!-- Message -->
      <p class="text-secondary-600 mb-6">{{ message }}</p>

      <!-- Actions -->
      <div class="flex items-center justify-end gap-3">
        <button
          @click="$emit('cancel')"
          class="neumorphic-button text-secondary-600 hover:text-secondary-700"
        >
          {{ cancelText }}
        </button>
        <button
          @click="$emit('confirm')"
          :class="[
            'neumorphic-button hover:shadow-neumorphic-hover',
            confirmClass || 'text-primary-600 hover:text-primary-700'
          ]"
        >
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  confirmClass?: string
}

withDefaults(defineProps<Props>(), {
  confirmText: 'تأكيد',
  cancelText: 'إلغاء',
  confirmClass: ''
})

// Emits
defineEmits<{
  confirm: []
  cancel: []
}>()
</script>
