# دليل المساهمة في برنامج إدارة بيانات المتهمين

نرحب بمساهماتكم في تطوير وتحسين برنامج إدارة بيانات المتهمين! هذا الدليل سيساعدكم على فهم كيفية المساهمة بفعالية.

## 📋 جدول المحتويات

- [قواعد السلوك](#قواعد-السلوك)
- [كيفية المساهمة](#كيفية-المساهمة)
- [الإبلاغ عن الأخطاء](#الإبلاغ-عن-الأخطاء)
- [اقتراح ميزات جديدة](#اقتراح-ميزات-جديدة)
- [إرشادات التطوير](#إرشادات-التطوير)
- [معايير الكود](#معايير-الكود)
- [عملية المراجعة](#عملية-المراجعة)

## 🤝 قواعد السلوك

### التزاماتنا

نحن ملتزمون بتوفير بيئة ترحيبية وشاملة للجميع، بغض النظر عن:
- العمر أو الجنس أو الهوية الجنسية
- العرق أو الجنسية أو الدين
- مستوى الخبرة أو التعليم
- الوضع الاجتماعي أو الاقتصادي

### السلوك المتوقع

- استخدام لغة ترحيبية وشاملة
- احترام وجهات النظر والخبرات المختلفة
- قبول النقد البناء بأدب
- التركيز على ما هو أفضل للمجتمع
- إظهار التعاطف مع أعضاء المجتمع الآخرين

### السلوك غير المقبول

- استخدام لغة أو صور جنسية
- التنمر أو التعليقات المهينة
- المضايقة العامة أو الخاصة
- نشر معلومات خاصة للآخرين دون إذن
- أي سلوك آخر يُعتبر غير مناسب مهنياً

## 🚀 كيفية المساهمة

### 1. إعداد بيئة التطوير

```bash
# استنساخ المشروع
git clone https://github.com/your-username/suspects-data-management.git
cd suspects-data-management

# تثبيت التبعيات
npm install

# إنشاء فرع جديد للميزة
git checkout -b feature/your-feature-name
```

### 2. أنواع المساهمات المرحب بها

- **إصلاح الأخطاء**: تحديد وإصلاح المشاكل الموجودة
- **ميزات جديدة**: إضافة وظائف جديدة مفيدة
- **تحسين الأداء**: تحسين سرعة وكفاءة التطبيق
- **تحسين الواجهة**: تحسين تجربة المستخدم والتصميم
- **الوثائق**: تحسين أو إضافة وثائق
- **الاختبارات**: إضافة أو تحسين الاختبارات
- **الترجمة**: إضافة دعم لغات جديدة

### 3. سير العمل

1. **Fork** المشروع إلى حسابك
2. **Clone** النسخة المنسوخة محلياً
3. إنشاء **فرع جديد** للميزة أو الإصلاح
4. إجراء **التغييرات** المطلوبة
5. **اختبار** التغييرات بدقة
6. **Commit** التغييرات مع رسائل واضحة
7. **Push** الفرع إلى GitHub
8. إنشاء **Pull Request**

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ

- تأكد من أن المشكلة لم يتم الإبلاغ عنها مسبقاً
- تحقق من أنك تستخدم أحدث إصدار
- جرب إعادة إنتاج المشكلة في بيئة نظيفة

### معلومات مطلوبة

عند الإبلاغ عن خطأ، يرجى تضمين:

```markdown
**وصف المشكلة**
وصف واضح ومختصر للمشكلة.

**خطوات إعادة الإنتاج**
1. اذهب إلى '...'
2. اضغط على '...'
3. مرر إلى '...'
4. شاهد الخطأ

**السلوك المتوقع**
وصف واضح لما كنت تتوقع حدوثه.

**لقطات الشاشة**
إذا كان ذلك مناسباً، أضف لقطات شاشة لتوضيح المشكلة.

**معلومات البيئة**
- نظام التشغيل: [مثل Windows 10, macOS 12.0]
- المتصفح: [مثل Chrome 95, Firefox 94]
- إصدار التطبيق: [مثل 1.0.0]

**معلومات إضافية**
أي معلومات أخرى حول المشكلة.
```

## 💡 اقتراح ميزات جديدة

### قبل الاقتراح

- تحقق من أن الميزة لم يتم اقتراحها مسبقاً
- تأكد من أن الميزة تتماشى مع أهداف المشروع
- فكر في التأثير على الأداء والأمان

### قالب اقتراح الميزة

```markdown
**هل اقتراحك مرتبط بمشكلة؟**
وصف واضح ومختصر للمشكلة. مثال: أشعر بالإحباط عندما [...]

**وصف الحل المقترح**
وصف واضح ومختصر لما تريد حدوثه.

**وصف البدائل المدروسة**
وصف واضح ومختصر لأي حلول أو ميزات بديلة فكرت فيها.

**معلومات إضافية**
أضف أي معلومات أخرى أو لقطات شاشة حول طلب الميزة هنا.
```

## 🛠️ إرشادات التطوير

### هيكل المشروع

```
src/
├── components/          # مكونات Vue قابلة لإعادة الاستخدام
│   ├── common/         # مكونات مشتركة
│   ├── forms/          # مكونات النماذج
│   └── ui/             # مكونات واجهة المستخدم
├── views/              # صفحات التطبيق الرئيسية
├── stores/             # إدارة الحالة (Pinia)
├── composables/        # Composition API functions
├── utils/              # أدوات مساعدة
├── types/              # تعريفات TypeScript
├── assets/             # الموارد الثابتة
└── router/             # إعدادات التوجيه

electron/
├── main.js             # العملية الرئيسية
├── preload.js          # سكريبت التحميل المسبق
├── database/           # إدارة قاعدة البيانات
├── services/           # خدمات النظام
└── tests/              # اختبارات الأداء
```

### متطلبات التطوير

- **Node.js**: 18.0.0 أو أحدث
- **npm**: 8.0.0 أو أحدث
- **Git**: للتحكم في الإصدارات
- **محرر نصوص**: VS Code مُوصى به

### إعداد بيئة التطوير

```bash
# تثبيت التبعيات
npm install

# تشغيل وضع التطوير
npm run dev

# تشغيل Electron في وضع التطوير
npm run electron:dev

# تشغيل الاختبارات
npm test

# فحص جودة الكود
npm run lint

# تنسيق الكود
npm run format
```

## 📏 معايير الكود

### JavaScript/TypeScript

- استخدم **TypeScript** للملفات الجديدة
- اتبع **ESLint** و **Prettier** configurations
- استخدم **Composition API** في Vue 3
- اكتب **JSDoc** للدوال المعقدة

```typescript
/**
 * يبحث في بيانات المتهمين باستخدام معايير متعددة
 * @param query - نص البحث
 * @param filters - فلاتر البحث
 * @returns نتائج البحث مع معلومات الصفحات
 */
async function searchSuspects(
  query: string,
  filters: SearchFilters
): Promise<SearchResult> {
  // تنفيذ البحث
}
```

### Vue Components

- استخدم **Composition API**
- اكتب **TypeScript** مع `<script setup lang="ts">`
- استخدم **Tailwind CSS** للتصميم
- اتبع **naming conventions** العربية للمكونات

```vue
<template>
  <div class="neumorphic-card">
    <!-- محتوى المكون -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// Props
interface Props {
  title: string
  data: SuspectData[]
}

const props = defineProps<Props>()

// State
const isLoading = ref(false)

// Computed
const filteredData = computed(() => {
  // منطق الفلترة
})

// Methods
const handleSubmit = async () => {
  // منطق الإرسال
}
</script>
```

### CSS/Styling

- استخدم **Tailwind CSS** classes
- اتبع **Neumorphic design** patterns
- ادعم **RTL** للنصوص العربية
- استخدم **CSS variables** للألوان

```css
.neumorphic-card {
  @apply bg-white rounded-2xl p-6;
  box-shadow: 
    8px 8px 16px #d1d9e6,
    -8px -8px 16px #ffffff;
}

.neumorphic-button {
  @apply px-4 py-2 rounded-xl transition-all duration-200;
  box-shadow: 
    4px 4px 8px #d1d9e6,
    -4px -4px 8px #ffffff;
}

.neumorphic-button:hover {
  transform: translateY(-2px);
  box-shadow: 
    6px 6px 12px #d1d9e6,
    -6px -6px 12px #ffffff;
}
```

### Git Commit Messages

استخدم الصيغة التالية للرسائل:

```
نوع: وصف مختصر

وصف مفصل إذا لزم الأمر.

- تغيير 1
- تغيير 2
- تغيير 3
```

**أنواع الـ commits:**
- `feat`: ميزة جديدة
- `fix`: إصلاح خطأ
- `docs`: تحديث الوثائق
- `style`: تغييرات التنسيق
- `refactor`: إعادة هيكلة الكود
- `test`: إضافة أو تحديث اختبارات
- `chore`: مهام صيانة

**أمثلة:**
```
feat: إضافة ميزة البحث المتقدم في المتهمين

fix: إصلاح مشكلة تحميل البيانات في صفحة التقارير

docs: تحديث دليل التثبيت في README

style: تحسين تصميم نموذج إضافة متهم جديد
```

## 🔍 عملية المراجعة

### معايير القبول

- **الوظيفة**: التغيير يعمل كما هو متوقع
- **الأداء**: لا يؤثر سلباً على أداء التطبيق
- **الأمان**: لا يقدم ثغرات أمنية
- **التوافق**: يعمل على جميع المنصات المدعومة
- **الاختبارات**: يتضمن اختبارات مناسبة
- **الوثائق**: يتضمن وثائق محدثة

### عملية المراجعة

1. **فحص تلقائي**: CI/CD checks
2. **مراجعة الكود**: من قبل maintainer
3. **اختبار يدوي**: للميزات الجديدة
4. **مراجعة الأمان**: للتغييرات الحساسة
5. **الموافقة النهائية**: من قبل project owner

### نصائح للحصول على مراجعة سريعة

- اكتب **وصف واضح** للـ Pull Request
- أضف **لقطات شاشة** للتغييرات البصرية
- اربط **Issues** ذات الصلة
- اجعل التغييرات **صغيرة ومركزة**
- اكتب **اختبارات شاملة**
- تأكد من **نجاح جميع الفحوصات**

## 📞 التواصل

### قنوات التواصل

- **GitHub Issues**: للأخطاء والميزات
- **GitHub Discussions**: للنقاشات العامة
- **Email**: للمسائل الحساسة أو الخاصة

### أوقات الاستجابة المتوقعة

- **الأخطاء الحرجة**: 24 ساعة
- **الأخطاء العادية**: 3-5 أيام
- **طلبات الميزات**: 1-2 أسبوع
- **Pull Requests**: 3-7 أيام

## 🙏 شكر وتقدير

نشكر جميع المساهمين الذين يساعدون في تطوير وتحسين هذا المشروع. مساهماتكم تجعل البرنامج أفضل للجميع!

### المساهمون الرئيسيون

- **محرم اليفرسي** - المطور الأساسي والمؤسس

---

**ملاحظة**: هذا دليل حي يتم تحديثه باستمرار. إذا كان لديك اقتراحات لتحسينه، فلا تتردد في فتح issue أو pull request!
