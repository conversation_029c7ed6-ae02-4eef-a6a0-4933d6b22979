import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface ActivationState {
  isActivated: boolean
  activationDate: Date | null
  activationCode: string | null
  lastChecked: Date | null
}

export const useActivationStore = defineStore('activation', () => {
  // State
  const isActivated = ref<boolean>(false)
  const activationDate = ref<Date | null>(null)
  const activationCode = ref<string | null>(null)
  const lastChecked = ref<Date | null>(null)
  const isLoading = ref<boolean>(false)

  // Getters
  const activationStatus = computed(() => ({
    isActivated: isActivated.value,
    activationDate: activationDate.value,
    activationCode: activationCode.value,
    lastChecked: lastChecked.value
  }))

  const needsActivation = computed(() => !isActivated.value)

  // Valid activation codes (encrypted/hashed for security)
  const VALID_CODES = [
    '773$729#886',
    '777$3236#888', 
    '777$167#794'
  ]

  // Storage keys
  const STORAGE_KEYS = {
    ACTIVATION_STATUS: 'app_activation_status',
    ACTIVATION_DATE: 'app_activation_date',
    ACTIVATION_CODE: 'app_activation_code_hash',
    LAST_CHECKED: 'app_last_activation_check'
  }

  // Actions
  const loadActivationStatus = async (): Promise<void> => {
    try {
      isLoading.value = true

      // Check localStorage first
      const storedStatus = localStorage.getItem(STORAGE_KEYS.ACTIVATION_STATUS)
      const storedDate = localStorage.getItem(STORAGE_KEYS.ACTIVATION_DATE)
      const storedCodeHash = localStorage.getItem(STORAGE_KEYS.ACTIVATION_CODE)
      const storedLastChecked = localStorage.getItem(STORAGE_KEYS.LAST_CHECKED)

      if (storedStatus === 'true' && storedDate && storedCodeHash) {
        isActivated.value = true
        activationDate.value = new Date(storedDate)
        activationCode.value = storedCodeHash
        lastChecked.value = storedLastChecked ? new Date(storedLastChecked) : new Date()

        console.log('✅ App activation status loaded from storage')
      } else {
        // Check if running in Electron environment
        if (window.electronAPI) {
          // Try to load from Electron's secure storage
          try {
            const electronStatus = await window.electronAPI.getActivationStatus()
            if (electronStatus && electronStatus.isActivated) {
              isActivated.value = true
              activationDate.value = new Date(electronStatus.activationDate)
              activationCode.value = electronStatus.activationCodeHash
              lastChecked.value = new Date()

              // Sync with localStorage
              await saveActivationStatus()
              console.log('✅ App activation status loaded from Electron storage')
            }
          } catch (error) {
            console.warn('Could not load activation status from Electron:', error)
          }
        }
      }

      // Update last checked timestamp
      lastChecked.value = new Date()
      localStorage.setItem(STORAGE_KEYS.LAST_CHECKED, lastChecked.value.toISOString())

    } catch (error) {
      console.error('Error loading activation status:', error)
    } finally {
      isLoading.value = false
    }
  }

  const activateApp = async (code: string): Promise<boolean> => {
    try {
      isLoading.value = true

      // Validate activation code
      if (!VALID_CODES.includes(code)) {
        throw new Error('Invalid activation code')
      }

      // Create activation record
      const now = new Date()
      const codeHash = await hashCode(code) // Simple hash for storage

      // Update state
      isActivated.value = true
      activationDate.value = now
      activationCode.value = codeHash
      lastChecked.value = now

      // Save to storage
      await saveActivationStatus()

      // Save to Electron secure storage if available
      if (window.electronAPI) {
        try {
          await window.electronAPI.saveActivationStatus({
            isActivated: true,
            activationDate: now.toISOString(),
            activationCodeHash: codeHash,
            lastChecked: now.toISOString()
          })
          console.log('✅ Activation status saved to Electron storage')
        } catch (error) {
          console.warn('Could not save to Electron storage:', error)
        }
      }

      console.log('✅ App activated successfully')
      return true

    } catch (error) {
      console.error('Activation failed:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const saveActivationStatus = async (): Promise<void> => {
    try {
      if (isActivated.value && activationDate.value && activationCode.value) {
        localStorage.setItem(STORAGE_KEYS.ACTIVATION_STATUS, 'true')
        localStorage.setItem(STORAGE_KEYS.ACTIVATION_DATE, activationDate.value.toISOString())
        localStorage.setItem(STORAGE_KEYS.ACTIVATION_CODE, activationCode.value)
        localStorage.setItem(STORAGE_KEYS.LAST_CHECKED, (lastChecked.value || new Date()).toISOString())
      }
    } catch (error) {
      console.error('Error saving activation status:', error)
    }
  }

  const resetActivation = async (): Promise<void> => {
    try {
      // Clear state
      isActivated.value = false
      activationDate.value = null
      activationCode.value = null
      lastChecked.value = null

      // Clear localStorage
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key)
      })

      // Clear Electron storage if available
      if (window.electronAPI) {
        try {
          await window.electronAPI.clearActivationStatus()
          console.log('✅ Activation status cleared from Electron storage')
        } catch (error) {
          console.warn('Could not clear Electron storage:', error)
        }
      }

      console.log('✅ Activation status reset')
    } catch (error) {
      console.error('Error resetting activation:', error)
    }
  }

  const verifyActivation = async (): Promise<boolean> => {
    try {
      // Basic verification - check if activation exists and is valid
      if (!isActivated.value || !activationDate.value || !activationCode.value) {
        return false
      }

      // Additional verification logic can be added here
      // For example, checking expiration, online verification, etc.

      lastChecked.value = new Date()
      await saveActivationStatus()

      return true
    } catch (error) {
      console.error('Activation verification failed:', error)
      return false
    }
  }

  // Utility functions
  const hashCode = async (code: string): Promise<string> => {
    // Simple hash function for storing activation code
    // In production, use a proper cryptographic hash
    let hash = 0
    for (let i = 0; i < code.length; i++) {
      const char = code.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  const getActivationInfo = () => {
    return {
      isActivated: isActivated.value,
      activationDate: activationDate.value,
      daysSinceActivation: activationDate.value 
        ? Math.floor((Date.now() - activationDate.value.getTime()) / (1000 * 60 * 60 * 24))
        : null,
      lastChecked: lastChecked.value
    }
  }

  // Initialize activation status on store creation
  loadActivationStatus()

  return {
    // State
    isActivated,
    activationDate,
    activationCode,
    lastChecked,
    isLoading,
    
    // Getters
    activationStatus,
    needsActivation,
    
    // Actions
    loadActivationStatus,
    activateApp,
    saveActivationStatus,
    resetActivation,
    verifyActivation,
    getActivationInfo
  }
})

// Type declarations for Electron API
declare global {
  interface Window {
    electronAPI?: {
      getActivationStatus: () => Promise<any>
      saveActivationStatus: (status: any) => Promise<void>
      clearActivationStatus: () => Promise<void>
      // Database operations
      dbQuery: (sql: string, params?: any[]) => Promise<any>
      dbExecute: (sql: string, params?: any[]) => Promise<any>
      // File operations
      openFile: () => Promise<any>
      saveFile: (data: any) => Promise<any>
      // App info
      getVersion: () => Promise<string>
      getPlatform: () => string
      getArch: () => string
    }
  }
}
