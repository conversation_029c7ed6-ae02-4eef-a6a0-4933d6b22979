const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File operations
  openFile: (options) => ipcRenderer.invoke('dialog:openFile', options),
  saveFile: (options) => ipcRenderer.invoke('dialog:saveFile', options),

  // App info
  getVersion: () => ipcRenderer.invoke('app:getVersion'),

  // Activation system
  checkActivation: () => ipcRenderer.invoke('activation:check'),
  activateApp: (code) => ipcRenderer.invoke('activation:activate', code),

  // Basic database operations (mock)
  dbQuery: (query, params) => ipcRenderer.invoke('db:query', query, params),
  dbSearch: (searchParams) => ipcRenderer.invoke('db:search', searchParams),

  // Platform info
  platform: process.platform,
  
  // Basic notifications
  showNotification: (title, body) => {
    new Notification(title, { body })
  }
})

console.log('Preload script loaded successfully!')
