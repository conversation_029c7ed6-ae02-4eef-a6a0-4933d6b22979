const { autoUpdater } = require('electron-updater')
const { app, dialog, BrowserWindow } = require('electron')
const path = require('path')
const fs = require('fs').promises

class UpdaterService {
  constructor() {
    this.isUpdateAvailable = false
    this.isUpdateDownloaded = false
    this.updateInfo = null
    this.downloadProgress = null
    this.mainWindow = null
    
    this.setupAutoUpdater()
  }

  setupAutoUpdater() {
    // Configure auto-updater
    autoUpdater.autoDownload = false
    autoUpdater.autoInstallOnAppQuit = true
    
    // Set update server (if using custom server)
    if (process.env.UPDATE_SERVER_URL) {
      autoUpdater.setFeedURL({
        provider: 'generic',
        url: process.env.UPDATE_SERVER_URL
      })
    }

    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      console.log('🔍 Checking for updates...')
      this.sendToRenderer('updater:checking')
    })

    autoUpdater.on('update-available', (info) => {
      console.log('📦 Update available:', info.version)
      this.isUpdateAvailable = true
      this.updateInfo = info
      this.sendToRenderer('updater:available', info)
      
      // Show update notification
      this.showUpdateNotification(info)
    })

    autoUpdater.on('update-not-available', (info) => {
      console.log('✅ No updates available')
      this.sendToRenderer('updater:not-available', info)
    })

    autoUpdater.on('error', (error) => {
      console.error('❌ Update error:', error)
      this.sendToRenderer('updater:error', {
        message: error.message,
        stack: error.stack
      })
    })

    autoUpdater.on('download-progress', (progress) => {
      this.downloadProgress = progress
      console.log(`📥 Download progress: ${Math.round(progress.percent)}%`)
      this.sendToRenderer('updater:download-progress', progress)
    })

    autoUpdater.on('update-downloaded', (info) => {
      console.log('✅ Update downloaded:', info.version)
      this.isUpdateDownloaded = true
      this.sendToRenderer('updater:downloaded', info)
      
      // Show install notification
      this.showInstallNotification(info)
    })
  }

  setMainWindow(window) {
    this.mainWindow = window
  }

  async checkForUpdates() {
    try {
      console.log('🔍 Manually checking for updates...')
      const result = await autoUpdater.checkForUpdates()
      return {
        success: true,
        updateInfo: result?.updateInfo || null
      }
    } catch (error) {
      console.error('Update check failed:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  async downloadUpdate() {
    if (!this.isUpdateAvailable) {
      throw new Error('No update available to download')
    }

    try {
      console.log('📥 Starting update download...')
      await autoUpdater.downloadUpdate()
      return { success: true }
    } catch (error) {
      console.error('Update download failed:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  async installUpdate() {
    if (!this.isUpdateDownloaded) {
      throw new Error('No update downloaded to install')
    }

    try {
      console.log('🔄 Installing update and restarting...')
      
      // Perform pre-update tasks
      await this.performPreUpdateTasks()
      
      // Install and restart
      autoUpdater.quitAndInstall(false, true)
      
      return { success: true }
    } catch (error) {
      console.error('Update installation failed:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  async performPreUpdateTasks() {
    try {
      console.log('🔧 Performing pre-update tasks...')
      
      // Create backup before update
      await this.createPreUpdateBackup()
      
      // Save current state
      await this.saveApplicationState()
      
      // Close database connections
      await this.closeDatabaseConnections()
      
      console.log('✅ Pre-update tasks completed')
    } catch (error) {
      console.error('Pre-update tasks failed:', error)
      throw error
    }
  }

  async createPreUpdateBackup() {
    try {
      const backupDir = path.join(app.getPath('userData'), 'backups', 'pre-update')
      await fs.mkdir(backupDir, { recursive: true })
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const backupFile = path.join(backupDir, `backup-${timestamp}.json`)
      
      // Create backup data
      const backupData = {
        version: app.getVersion(),
        timestamp: new Date().toISOString(),
        userData: app.getPath('userData'),
        settings: await this.exportSettings(),
        database: await this.exportDatabaseSchema()
      }
      
      await fs.writeFile(backupFile, JSON.stringify(backupData, null, 2))
      console.log('💾 Pre-update backup created:', backupFile)
    } catch (error) {
      console.error('Failed to create pre-update backup:', error)
    }
  }

  async saveApplicationState() {
    try {
      const stateFile = path.join(app.getPath('userData'), 'app-state.json')
      const state = {
        windowBounds: this.mainWindow?.getBounds(),
        isMaximized: this.mainWindow?.isMaximized(),
        lastUpdateCheck: new Date().toISOString(),
        version: app.getVersion()
      }
      
      await fs.writeFile(stateFile, JSON.stringify(state, null, 2))
      console.log('💾 Application state saved')
    } catch (error) {
      console.error('Failed to save application state:', error)
    }
  }

  async closeDatabaseConnections() {
    try {
      // Signal to close database connections
      this.sendToRenderer('updater:prepare-shutdown')
      
      // Wait a bit for cleanup
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      console.log('🔌 Database connections closed')
    } catch (error) {
      console.error('Failed to close database connections:', error)
    }
  }

  async exportSettings() {
    try {
      const settingsFile = path.join(app.getPath('userData'), 'settings.json')
      const settings = await fs.readFile(settingsFile, 'utf8')
      return JSON.parse(settings)
    } catch (error) {
      return {}
    }
  }

  async exportDatabaseSchema() {
    try {
      // This would export the database schema for migration purposes
      return {
        version: '1.0.0',
        tables: [],
        indexes: []
      }
    } catch (error) {
      return {}
    }
  }

  showUpdateNotification(info) {
    if (!this.mainWindow) return

    const options = {
      type: 'info',
      title: 'تحديث متاح',
      message: `إصدار جديد متاح: ${info.version}`,
      detail: info.releaseNotes || 'يتضمن هذا التحديث تحسينات وإصلاحات مهمة.',
      buttons: ['تحميل الآن', 'تذكيرني لاحقاً', 'تخطي هذا الإصدار'],
      defaultId: 0,
      cancelId: 1
    }

    dialog.showMessageBox(this.mainWindow, options).then((result) => {
      if (result.response === 0) {
        // Download now
        this.downloadUpdate()
      } else if (result.response === 2) {
        // Skip this version
        this.skipVersion(info.version)
      }
    })
  }

  showInstallNotification(info) {
    if (!this.mainWindow) return

    const options = {
      type: 'info',
      title: 'التحديث جاهز للتثبيت',
      message: `تم تحميل الإصدار ${info.version} بنجاح`,
      detail: 'هل تريد إعادة تشغيل التطبيق الآن لتثبيت التحديث؟',
      buttons: ['إعادة التشغيل الآن', 'إعادة التشغيل لاحقاً'],
      defaultId: 0,
      cancelId: 1
    }

    dialog.showMessageBox(this.mainWindow, options).then((result) => {
      if (result.response === 0) {
        // Install now
        this.installUpdate()
      }
    })
  }

  async skipVersion(version) {
    try {
      const skipFile = path.join(app.getPath('userData'), 'skipped-versions.json')
      let skippedVersions = []
      
      try {
        const data = await fs.readFile(skipFile, 'utf8')
        skippedVersions = JSON.parse(data)
      } catch (error) {
        // File doesn't exist, start with empty array
      }
      
      if (!skippedVersions.includes(version)) {
        skippedVersions.push(version)
        await fs.writeFile(skipFile, JSON.stringify(skippedVersions, null, 2))
        console.log('⏭️ Version skipped:', version)
      }
    } catch (error) {
      console.error('Failed to skip version:', error)
    }
  }

  async isVersionSkipped(version) {
    try {
      const skipFile = path.join(app.getPath('userData'), 'skipped-versions.json')
      const data = await fs.readFile(skipFile, 'utf8')
      const skippedVersions = JSON.parse(data)
      return skippedVersions.includes(version)
    } catch (error) {
      return false
    }
  }

  sendToRenderer(channel, data = null) {
    if (this.mainWindow && this.mainWindow.webContents) {
      this.mainWindow.webContents.send(channel, data)
    }
  }

  getUpdateStatus() {
    return {
      isUpdateAvailable: this.isUpdateAvailable,
      isUpdateDownloaded: this.isUpdateDownloaded,
      updateInfo: this.updateInfo,
      downloadProgress: this.downloadProgress,
      currentVersion: app.getVersion()
    }
  }

  // Schedule automatic update checks
  scheduleUpdateChecks() {
    const checkInterval = parseInt(process.env.UPDATE_CHECK_INTERVAL) || 24 * 60 * 60 * 1000 // 24 hours

    setInterval(() => {
      if (!app.isPackaged) return // Don't check for updates in development
      
      console.log('🕐 Scheduled update check...')
      this.checkForUpdates()
    }, checkInterval)

    // Initial check after 30 seconds
    setTimeout(() => {
      if (app.isPackaged) {
        this.checkForUpdates()
      }
    }, 30000)
  }
}

module.exports = UpdaterService
