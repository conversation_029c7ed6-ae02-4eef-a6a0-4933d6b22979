<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6 pb-4 border-b border-secondary-200">
        <div class="flex items-center gap-3">
          <div class="neumorphic-icon">
            <i class="fas fa-plus text-primary-600"></i>
          </div>
          <h2 class="text-xl font-bold text-secondary-800">
            {{ isEditing ? 'تعديل الحقل' : 'إضافة حقل جديد' }}
          </h2>
        </div>
        <button @click="$emit('cancel')" class="text-secondary-400 hover:text-secondary-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <!-- Form -->
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Field Label -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-tag ml-1"></i>
            تسمية الحقل *
          </label>
          <input
            v-model="formData.label"
            type="text"
            class="neumorphic-input w-full"
            placeholder="مثال: الاسم الرباعي"
            required
          />
          <p class="text-xs text-secondary-500 mt-1">النص الذي سيظهر كعنوان للحقل</p>
        </div>

        <!-- Field Icon -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-icons ml-1"></i>
            أيقونة الحقل
          </label>
          <div class="grid grid-cols-8 gap-2 p-4 neumorphic-input">
            <button
              v-for="icon in availableIcons"
              :key="icon"
              type="button"
              @click="formData.icon = icon"
              :class="[
                'p-3 rounded-neumorphic-sm transition-all duration-300',
                formData.icon === icon 
                  ? 'bg-primary-100 text-primary-600 shadow-neumorphic-inset' 
                  : 'hover:bg-secondary-100'
              ]"
            >
              <i :class="icon"></i>
            </button>
          </div>
          <p class="text-xs text-secondary-500 mt-1">اختر أيقونة تمثل نوع البيانات</p>
        </div>

        <!-- Input Type -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-keyboard ml-1"></i>
            نوع الإدخال *
          </label>
          <div class="grid grid-cols-2 gap-3">
            <label
              v-for="type in inputTypes"
              :key="type.value"
              :class="[
                'neumorphic-button cursor-pointer text-center transition-all duration-300',
                formData.inputType === type.value 
                  ? 'bg-primary-100 text-primary-700 shadow-neumorphic-inset' 
                  : ''
              ]"
            >
              <input
                v-model="formData.inputType"
                :value="type.value"
                type="radio"
                class="hidden"
              />
              <div class="flex items-center justify-center gap-2">
                <i :class="type.icon"></i>
                <span>{{ type.label }}</span>
              </div>
            </label>
          </div>
        </div>

        <!-- Select Options (only for select type) -->
        <div v-if="formData.inputType === 'select'">
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-list ml-1"></i>
            خيارات القائمة
          </label>
          <div class="space-y-2">
            <div
              v-for="(option, index) in formData.options"
              :key="index"
              class="flex items-center gap-2"
            >
              <input
                v-model="formData.options[index]"
                type="text"
                class="neumorphic-input flex-1"
                :placeholder="`الخيار ${index + 1}`"
              />
              <button
                type="button"
                @click="removeOption(index)"
                class="neumorphic-button p-2 text-danger-600"
              >
                <i class="fas fa-trash"></i>
              </button>
            </div>
            <button
              type="button"
              @click="addOption"
              class="neumorphic-button text-primary-600 w-full"
            >
              <i class="fas fa-plus ml-2"></i>
              إضافة خيار
            </button>
          </div>
        </div>

        <!-- Validation Rules -->
        <div v-if="showValidation">
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-shield-alt ml-1"></i>
            قواعد التحقق
          </label>
          <div class="neumorphic-input p-4 space-y-4">
            <!-- Min/Max Length for text -->
            <div v-if="formData.inputType === 'text' || formData.inputType === 'textarea'" class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-xs text-secondary-600 mb-1">الحد الأدنى للأحرف</label>
                <input
                  v-model.number="formData.validation.minLength"
                  type="number"
                  min="0"
                  class="neumorphic-input w-full text-sm"
                />
              </div>
              <div>
                <label class="block text-xs text-secondary-600 mb-1">الحد الأقصى للأحرف</label>
                <input
                  v-model.number="formData.validation.maxLength"
                  type="number"
                  min="1"
                  class="neumorphic-input w-full text-sm"
                />
              </div>
            </div>

            <!-- Pattern for text -->
            <div v-if="formData.inputType === 'text'">
              <label class="block text-xs text-secondary-600 mb-1">نمط التحقق (Regex)</label>
              <input
                v-model="formData.validation.pattern"
                type="text"
                class="neumorphic-input w-full text-sm"
                placeholder="مثال: ^[0-9]{10}$ للأرقام فقط"
              />
            </div>

            <!-- File validation -->
            <div v-if="formData.inputType === 'image' || formData.inputType === 'file'">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-xs text-secondary-600 mb-1">أنواع الملفات المسموحة</label>
                  <input
                    v-model="fileTypesString"
                    type="text"
                    class="neumorphic-input w-full text-sm"
                    :placeholder="formData.inputType === 'image' ? 'jpg,png,gif' : 'pdf,doc,docx'"
                  />
                </div>
                <div>
                  <label class="block text-xs text-secondary-600 mb-1">الحد الأقصى للحجم (MB)</label>
                  <input
                    v-model.number="maxFileSizeMB"
                    type="number"
                    min="1"
                    max="100"
                    class="neumorphic-input w-full text-sm"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Field Settings -->
        <div class="grid grid-cols-2 gap-4">
          <label class="flex items-center gap-3 neumorphic-button cursor-pointer">
            <input
              v-model="formData.isRequired"
              type="checkbox"
              class="hidden"
            />
            <div :class="[
              'w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-300',
              formData.isRequired 
                ? 'bg-primary-500 border-primary-500 text-white' 
                : 'border-secondary-300'
            ]">
              <i v-if="formData.isRequired" class="fas fa-check text-xs"></i>
            </div>
            <span>حقل مطلوب</span>
          </label>

          <label class="flex items-center gap-3 neumorphic-button cursor-pointer">
            <input
              v-model="formData.isVisible"
              type="checkbox"
              class="hidden"
            />
            <div :class="[
              'w-5 h-5 rounded border-2 flex items-center justify-center transition-all duration-300',
              formData.isVisible 
                ? 'bg-success-500 border-success-500 text-white' 
                : 'border-secondary-300'
            ]">
              <i v-if="formData.isVisible" class="fas fa-check text-xs"></i>
            </div>
            <span>مرئي في النموذج</span>
          </label>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-end gap-3 pt-4 border-t border-secondary-200">
          <button
            type="button"
            @click="$emit('cancel')"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700"
          >
            إلغاء
          </button>
          <button
            type="submit"
            class="neumorphic-button text-primary-600 hover:text-primary-700"
            :disabled="!isFormValid"
          >
            <i class="fas fa-save ml-2"></i>
            {{ isEditing ? 'تحديث' : 'إضافة' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { SuspectField, FieldValidation } from '@/types'

// Props
interface Props {
  field?: SuspectField | null
  isEditing: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  save: [field: Omit<SuspectField, 'id'> | SuspectField]
  cancel: []
}>()

// Form data
const formData = ref<Omit<SuspectField, 'id'> & { id?: string }>({
  label: '',
  icon: 'fas fa-text-width',
  inputType: 'text',
  isRequired: false,
  isVisible: true,
  order: 1,
  options: [],
  validation: {}
})

// Available icons
const availableIcons = [
  'fas fa-user', 'fas fa-id-card', 'fas fa-phone', 'fas fa-envelope',
  'fas fa-map-marker-alt', 'fas fa-calendar-alt', 'fas fa-camera',
  'fas fa-file-alt', 'fas fa-briefcase', 'fas fa-heart',
  'fas fa-flag', 'fas fa-birthday-cake', 'fas fa-sticky-note',
  'fas fa-box', 'fas fa-text-width', 'fas fa-list-alt'
]

// Input types
const inputTypes = [
  { value: 'text', label: 'نص', icon: 'fas fa-text-width' },
  { value: 'textarea', label: 'نص طويل', icon: 'fas fa-align-left' },
  { value: 'image', label: 'صورة', icon: 'fas fa-camera' },
  { value: 'file', label: 'ملف', icon: 'fas fa-file' },
  { value: 'date', label: 'تاريخ', icon: 'fas fa-calendar-alt' },
  { value: 'select', label: 'قائمة اختيار', icon: 'fas fa-list' }
]

// Computed
const showValidation = computed(() => {
  return ['text', 'textarea', 'image', 'file'].includes(formData.value.inputType)
})

const isFormValid = computed(() => {
  return formData.value.label.trim().length > 0 && 
         formData.value.icon.length > 0 &&
         (formData.value.inputType !== 'select' || formData.value.options!.length > 0)
})

const fileTypesString = computed({
  get: () => formData.value.validation?.fileTypes?.join(',') || '',
  set: (value: string) => {
    if (!formData.value.validation) formData.value.validation = {}
    formData.value.validation.fileTypes = value.split(',').map(s => s.trim()).filter(Boolean)
  }
})

const maxFileSizeMB = computed({
  get: () => formData.value.validation?.maxFileSize ? Math.round(formData.value.validation.maxFileSize / 1024 / 1024) : 5,
  set: (value: number) => {
    if (!formData.value.validation) formData.value.validation = {}
    formData.value.validation.maxFileSize = value * 1024 * 1024
  }
})

// Methods
function addOption() {
  if (!formData.value.options) formData.value.options = []
  formData.value.options.push('')
}

function removeOption(index: number) {
  formData.value.options?.splice(index, 1)
}

function handleSubmit() {
  if (!isFormValid.value) return

  // Clean up validation object
  const validation: FieldValidation = {}
  if (formData.value.validation?.minLength) validation.minLength = formData.value.validation.minLength
  if (formData.value.validation?.maxLength) validation.maxLength = formData.value.validation.maxLength
  if (formData.value.validation?.pattern) validation.pattern = formData.value.validation.pattern
  if (formData.value.validation?.fileTypes?.length) validation.fileTypes = formData.value.validation.fileTypes
  if (formData.value.validation?.maxFileSize) validation.maxFileSize = formData.value.validation.maxFileSize

  // Prepare field data for storage
  const fieldData = {
    ...formData.value,
    validation: Object.keys(validation).length > 0 ? validation : undefined,
    // Don't include options here - let the store handle the conversion
    options: formData.value.inputType === 'select' ? formData.value.options?.filter(Boolean) : undefined
  }

  // Remove empty options array
  if (fieldData.options && fieldData.options.length === 0) {
    fieldData.options = undefined
  }

  emit('save', fieldData)
}

// Initialize form data
onMounted(() => {
  if (props.field) {
    formData.value = {
      ...props.field,
      options: props.field.options ? [...props.field.options] : [],
      validation: props.field.validation ? { ...props.field.validation } : {}
    }
  } else {
    // Initialize with default options for select
    formData.value.options = []
  }
})

// Watch input type changes
watch(() => formData.value.inputType, (newType) => {
  if (newType === 'select' && !formData.value.options?.length) {
    formData.value.options = ['']
  }
  
  // Set default file types
  if (newType === 'image' && !formData.value.validation?.fileTypes?.length) {
    if (!formData.value.validation) formData.value.validation = {}
    formData.value.validation.fileTypes = ['jpg', 'jpeg', 'png', 'gif']
    formData.value.validation.maxFileSize = 5 * 1024 * 1024 // 5MB
  } else if (newType === 'file' && !formData.value.validation?.fileTypes?.length) {
    if (!formData.value.validation) formData.value.validation = {}
    formData.value.validation.fileTypes = ['pdf', 'doc', 'docx', 'txt']
    formData.value.validation.maxFileSize = 10 * 1024 * 1024 // 10MB
  }
})
</script>
