<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - قاعدة البيانات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            direction: rtl;
            background: #f8fafc;
        }
        .test-card {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: white;
            padding: 12px 24px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            margin: 8px;
            cursor: pointer;
            transition: all 0.3s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .test-button:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-1px);
        }
        .success { color: #10b981; }
        .primary { color: #3b82f6; }
        .warning { color: #f59e0b; }
        .danger { color: #ef4444; }
        .info { color: #06b6d4; }
        
        .status {
            padding: 8px 16px;
            border-radius: 6px;
            margin: 8px 0;
            font-weight: bold;
        }
        .status.success { background: #ecfdf5; color: #065f46; }
        .status.error { background: #fef2f2; color: #991b1b; }
        .status.info { background: #eff6ff; color: #1e40af; }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #1e40af;">🧪 اختبار سريع - قاعدة البيانات</h1>

    <div class="test-card">
        <h2>✅ الخطوات المطلوبة للاختبار:</h2>
        
        <div class="status info">
            <strong>1. افتح صفحة قاعدة البيانات:</strong>
            <a href="http://localhost:5173/database" target="_blank" style="color: #3b82f6; text-decoration: underline;">
                http://localhost:5173/database
            </a>
        </div>

        <div class="status info">
            <strong>2. تحقق من ظهور الأيقونات والأزرار:</strong>
            <ul>
                <li>أيقونة قاعدة البيانات في العنوان</li>
                <li>زر "إضافة تبويب" مع أيقونة +</li>
                <li>زر "استيراد Excel" مع أيقونة ملف</li>
            </ul>
        </div>

        <div class="status info">
            <strong>3. اختبر إضافة تبويب جديد:</strong>
            <ul>
                <li>اضغط على "إضافة تبويب"</li>
                <li>املأ النموذج بالبيانات التالية:</li>
                <li><strong>اسم التبويب:</strong> اختبار الميزات</li>
                <li><strong>الأيقونة:</strong> fas fa-test-tube</li>
                <li><strong>الأعمدة:</strong> الاسم، العمر، المدينة، التاريخ</li>
            </ul>
        </div>

        <div class="status info">
            <strong>4. اختبر الميزات الأساسية:</strong>
            <ul>
                <li>إضافة صف جديد</li>
                <li>إضافة عمود جديد</li>
                <li>التحرير المباشر للخلايا</li>
                <li>البحث في البيانات</li>
            </ul>
        </div>
    </div>

    <div class="test-card">
        <h2>🎯 الأزرار المتوقع ظهورها:</h2>
        
        <div style="display: flex; flex-wrap: wrap; gap: 10px;">
            <button class="test-button success">
                <i class="fas fa-plus"></i>
                إضافة تبويب
            </button>
            
            <button class="test-button primary">
                <i class="fas fa-file-excel"></i>
                استيراد Excel
            </button>
            
            <button class="test-button success">
                <i class="fas fa-plus"></i>
                إضافة صف
            </button>
            
            <button class="test-button info">
                <i class="fas fa-columns"></i>
                إضافة عمود
            </button>
            
            <button class="test-button danger">
                <i class="fas fa-trash"></i>
                حذف المحدد
            </button>
            
            <button class="test-button warning">
                <i class="fas fa-save"></i>
                حفظ التنسيقات
            </button>
            
            <button class="test-button info">
                <i class="fas fa-layer-group"></i>
                إعدادات المجموعات
            </button>
        </div>
    </div>

    <div class="test-card">
        <h2>📊 قائمة التحقق:</h2>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
            <div>
                <h3>الواجهة الأساسية:</h3>
                <label><input type="checkbox"> أيقونة قاعدة البيانات تظهر</label><br>
                <label><input type="checkbox"> زر إضافة تبويب يعمل</label><br>
                <label><input type="checkbox"> زر استيراد Excel يعمل</label><br>
                <label><input type="checkbox"> الألوان والتصميم صحيح</label><br>
            </div>
            
            <div>
                <h3>الوظائف المتقدمة:</h3>
                <label><input type="checkbox"> إضافة تبويب جديد</label><br>
                <label><input type="checkbox"> إضافة صف وعمود</label><br>
                <label><input type="checkbox"> التحرير المباشر</label><br>
                <label><input type="checkbox"> البحث والفرز</label><br>
            </div>
        </div>
    </div>

    <div class="test-card">
        <h2>🚨 في حالة وجود مشاكل:</h2>
        
        <div class="status error">
            <strong>إذا لم تظهر الأيقونات:</strong>
            <ul>
                <li>تحقق من اتصال الإنترنت (Font Awesome يحمل من CDN)</li>
                <li>افتح Developer Tools واطلع على أخطاء الكونسول</li>
                <li>تأكد من أن الخادم يعمل على المنفذ 5173</li>
            </ul>
        </div>

        <div class="status error">
            <strong>إذا لم تعمل الأزرار:</strong>
            <ul>
                <li>تحقق من أخطاء JavaScript في الكونسول</li>
                <li>تأكد من أن Vue.js يعمل بشكل صحيح</li>
                <li>أعد تحميل الصفحة</li>
            </ul>
        </div>
    </div>

    <div style="text-align: center; margin: 30px 0; padding: 20px; background: #dbeafe; border-radius: 10px;">
        <h2 style="color: #1e40af;">🎉 النتيجة المتوقعة</h2>
        <p><strong>جميع الأيقونات والأزرار تظهر وتعمل بشكل صحيح</strong></p>
        <p>إذا كان كل شيء يعمل، فقد تم إصلاح المشكلة بنجاح! 🎯</p>
    </div>

    <script>
        // Test Font Awesome loading
        window.addEventListener('load', function() {
            const icon = document.querySelector('.fas');
            if (icon) {
                const computed = window.getComputedStyle(icon, ':before');
                const content = computed.getPropertyValue('content');
                
                if (content && content !== 'none' && content !== '""') {
                    console.log('✅ Font Awesome محمل بشكل صحيح');
                    document.body.insertAdjacentHTML('beforeend', 
                        '<div class="status success" style="position: fixed; top: 10px; left: 10px; z-index: 1000;">✅ Font Awesome يعمل</div>'
                    );
                } else {
                    console.log('❌ Font Awesome غير محمل');
                    document.body.insertAdjacentHTML('beforeend', 
                        '<div class="status error" style="position: fixed; top: 10px; left: 10px; z-index: 1000;">❌ Font Awesome لا يعمل</div>'
                    );
                }
            }
        });
    </script>
</body>
</html>
