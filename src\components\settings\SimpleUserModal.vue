<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6 pb-4 border-b border-secondary-200">
        <div class="flex items-center gap-3">
          <div class="neumorphic-icon">
            <i :class="isEditing ? 'fas fa-user-edit' : 'fas fa-user-plus'" class="text-primary-600"></i>
          </div>
          <h2 class="text-xl font-bold text-secondary-800">
            {{ isEditing ? 'تعديل المستخدم' : 'إضافة مستخدم جديد' }}
          </h2>
        </div>
        <button @click="$emit('cancel')" class="text-secondary-400 hover:text-secondary-600">
          <i class="fas fa-times text-xl"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- معلومات المستخدم -->
        <div class="neumorphic-card bg-gradient-to-br from-blue-50 to-blue-100 p-6">
          <div class="flex items-center gap-3 mb-4">
            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
              <i class="fas fa-user text-white text-sm"></i>
            </div>
            <h3 class="text-lg font-semibold text-blue-800">معلومات المستخدم</h3>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- اسم المستخدم -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">
                <i class="fas fa-user ml-2"></i>
                اسم المستخدم
              </label>
              <input
                v-model="formData.username"
                type="text"
                required
                class="neumorphic-input w-full"
                placeholder="أدخل اسم المستخدم"
                :disabled="isEditing"
              />
            </div>

            <!-- الاسم الكامل -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">
                <i class="fas fa-id-card ml-2"></i>
                الاسم الكامل
              </label>
              <input
                v-model="formData.name"
                type="text"
                required
                class="neumorphic-input w-full"
                placeholder="أدخل الاسم الكامل"
              />
            </div>

            <!-- البريد الإلكتروني -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">
                <i class="fas fa-envelope ml-2"></i>
                البريد الإلكتروني
              </label>
              <input
                v-model="formData.email"
                type="email"
                required
                class="neumorphic-input w-full"
                placeholder="أدخل البريد الإلكتروني"
              />
            </div>

            <!-- دور المستخدم -->
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">
                <i class="fas fa-shield-alt ml-2"></i>
                دور المستخدم
              </label>
              <select
                v-model="formData.role"
                required
                class="neumorphic-input w-full"
              >
                <option value="">اختر الدور</option>
                <option
                  v-for="role in availableRoles"
                  :key="role.id"
                  :value="role"
                >
                  {{ role.displayName }}
                </option>
              </select>
              <p v-if="formData.role?.description" class="text-xs text-secondary-600 mt-1">
                {{ formData.role.description }}
              </p>
            </div>

            <!-- كلمة المرور -->
            <div v-if="!isEditing" class="md:col-span-2">
              <label class="block text-sm font-medium text-secondary-700 mb-2">
                <i class="fas fa-key ml-2"></i>
                كلمة المرور
              </label>
              <div class="relative">
                <input
                  v-model="formData.password"
                  :type="showPassword ? 'text' : 'password'"
                  required
                  minlength="8"
                  class="neumorphic-input w-full pl-10"
                  placeholder="أدخل كلمة المرور (8 أحرف على الأقل)"
                />
                <button
                  type="button"
                  @click="showPassword = !showPassword"
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary-400 hover:text-secondary-600"
                >
                  <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                </button>
              </div>
            </div>

            <!-- حالة المستخدم -->
            <div class="md:col-span-2">
              <label class="flex items-center gap-3">
                <input
                  v-model="formData.isActive"
                  type="checkbox"
                  class="neumorphic-checkbox"
                />
                <span class="text-sm font-medium text-secondary-700">
                  <i class="fas fa-toggle-on ml-2"></i>
                  المستخدم نشط
                </span>
              </label>
            </div>
          </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="flex items-center justify-end gap-3 pt-4 border-t border-secondary-200">
          <button
            type="button"
            @click="$emit('cancel')"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700 px-6 py-2"
          >
            <i class="fas fa-times ml-2"></i>
            إلغاء
          </button>
          <button
            type="submit"
            class="neumorphic-button bg-primary-500 text-white px-6 py-2 hover:bg-primary-600"
            :disabled="!isFormValid || isLoading"
          >
            <i v-if="isLoading" class="fas fa-spinner fa-spin ml-2"></i>
            <i v-else :class="isEditing ? 'fas fa-save' : 'fas fa-plus'" class="ml-2"></i>
            {{ isLoading ? 'جاري الحفظ...' : (isEditing ? 'تحديث' : 'إضافة') }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'

// Props
interface Props {
  user?: any | null
  isEditing: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  save: [user: any]
  cancel: []
}>()

// Reactive data
const isLoading = ref(false)
const showPassword = ref(false)

// Available roles
const availableRoles = ref([
  {
    id: 'admin',
    name: 'admin',
    displayName: 'مدير عام',
    description: 'صلاحيات كاملة لإدارة النظام والمستخدمين والإعدادات'
  },
  {
    id: 'supervisor',
    name: 'supervisor',
    displayName: 'مشرف',
    description: 'صلاحيات إشرافية مع إمكانية إدارة البيانات والتقارير'
  },
  {
    id: 'officer',
    name: 'officer',
    displayName: 'ضابط',
    description: 'صلاحيات تشغيلية لإدارة بيانات المتهمين وإنشاء التقارير'
  },
  {
    id: 'investigator',
    name: 'investigator',
    displayName: 'محقق رئيسي',
    description: 'صلاحيات التحقيق مع إمكانية الوصول للبيانات والتحليل'
  },
  {
    id: 'viewer',
    name: 'viewer',
    displayName: 'مراقب',
    description: 'صلاحيات القراءة فقط لعرض البيانات والتقارير'
  }
])

// Form data
const formData = ref({
  username: '',
  name: '',
  email: '',
  role: null as any,
  password: '',
  isActive: true
})

// Computed
const isFormValid = computed(() => {
  const hasRequiredFields = (formData.value.username?.trim()?.length || 0) > 0 && 
                           (formData.value.name?.trim()?.length || 0) > 0 &&
                           (formData.value.email?.trim()?.length || 0) > 0 &&
                           formData.value.role
  
  if (!props.isEditing) {
    return hasRequiredFields && (formData.value.password?.length || 0) >= 8
  }
  
  return hasRequiredFields
})

// Methods
function initializeFormData() {
  if (props.isEditing && props.user) {
    formData.value = {
      username: props.user.username || '',
      name: props.user.name || '',
      email: props.user.email || '',
      role: props.user.role || null,
      password: '',
      isActive: props.user.isActive !== undefined ? props.user.isActive : true
    }
  } else {
    formData.value = {
      username: '',
      name: '',
      email: '',
      role: null,
      password: '',
      isActive: true
    }
  }
}

async function handleSubmit() {
  if (!isFormValid.value || isLoading.value) return
  
  isLoading.value = true
  
  try {
    // محاكاة حفظ البيانات
    const userData = {
      id: props.user?.id || Date.now().toString(),
      username: formData.value.username,
      name: formData.value.name,
      email: formData.value.email,
      role: formData.value.role,
      isActive: formData.value.isActive,
      createdAt: props.user?.createdAt || new Date(),
      updatedAt: new Date()
    }
    
    // محاكاة تأخير الشبكة
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('save', userData)
    
  } catch (error) {
    console.error('Error saving user:', error)
    alert('حدث خطأ أثناء حفظ المستخدم')
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  initializeFormData()
})

// Watch for prop changes
watch(() => props.user, () => {
  initializeFormData()
}, { deep: true })
</script>

<style scoped>
.neumorphic-checkbox {
  @apply w-4 h-4 text-primary-600 bg-white border-2 border-secondary-300 rounded focus:ring-primary-500 focus:ring-2;
}

.neumorphic-input {
  @apply px-4 py-2 border border-secondary-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white;
}

.neumorphic-button {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md;
}

.neumorphic-card {
  @apply rounded-xl shadow-lg border border-secondary-100;
}

.neumorphic-icon {
  @apply w-10 h-10 rounded-full flex items-center justify-center shadow-inner bg-gradient-to-br from-white to-secondary-100;
}
</style>
