<template>
  <div class="min-h-screen bg-secondary-50 p-6 rtl">
    <!-- Header -->
    <div class="neumorphic-header mb-8">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <div class="neumorphic-icon">
            <i class="fas fa-users text-primary-600"></i>
          </div>
          <div>
            <h1 class="text-2xl font-bold text-secondary-800">بيانات المتهمين</h1>
            <p class="text-secondary-600">إدارة شاملة لبيانات المتهمين والمساجين</p>
          </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="hidden lg:flex items-center gap-4">
          <div class="neumorphic-card bg-white p-3 text-center min-w-[80px]">
            <div class="text-lg font-bold text-primary-600">{{ statistics.total }}</div>
            <div class="text-xs text-secondary-600">إجمالي</div>
          </div>
          <div class="neumorphic-card bg-white p-3 text-center min-w-[80px]">
            <div class="text-lg font-bold text-danger-600">{{ statistics.detained }}</div>
            <div class="text-xs text-secondary-600">معتقل</div>
          </div>
          <div class="neumorphic-card bg-white p-3 text-center min-w-[80px]">
            <div class="text-lg font-bold text-success-600">{{ statistics.released }}</div>
            <div class="text-xs text-secondary-600">مفرج</div>
          </div>
          <div class="neumorphic-card bg-white p-3 text-center min-w-[80px]">
            <div class="text-lg font-bold text-blue-600">{{ statistics.transferred }}</div>
            <div class="text-xs text-secondary-600">محال</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tabs Navigation -->
    <div class="neumorphic-card mb-8">
      <div class="flex flex-wrap gap-2 p-2">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            'neumorphic-tab flex items-center gap-2 px-4 py-3 transition-all duration-300',
            { 'active': activeTab === tab.id }
          ]"
        >
          <i :class="tab.icon"></i>
          <span>{{ tab.label }}</span>
          <span v-if="tab.count !== undefined" class="bg-primary-100 text-primary-700 px-2 py-1 rounded-full text-xs">
            {{ tab.count }}
          </span>
        </button>
      </div>
    </div>

    <!-- Tab Content -->
    <div class="grid grid-cols-1 gap-8">
      <!-- Add Suspect Tab -->
      <AddSuspect
        v-if="activeTab === 'add'"
        :fields="suspectFields"
        :loading="isLoading"
        :editing-suspect="editingSuspect"
        @add-suspect="handleAddSuspect"
        @clear-editing="clearEditingState"
      />

      <!-- Review Suspects Tab -->
      <ReviewSuspects 
        v-if="activeTab === 'review'"
        :suspects="filteredSuspects"
        :fields="suspectFields"
        :loading="isLoading"
        :search-query="searchQuery"
        :status-filter="statusFilter"
        @update-search="setSearchQuery"
        @update-status-filter="setStatusFilter"
        @update-suspect="handleUpdateSuspect"
        @delete-suspect="handleDeleteSuspect"
        @update-suspect-status="handleUpdateSuspectStatus"
      />

      <!-- Charts Tab -->
      <SuspectsCharts
        v-if="activeTab === 'charts'"
        :statistics="statistics"
        :suspects="suspects"
        :fields="suspectFields"
        :loading="isLoading"
      />

      <!-- Cards Tab -->
      <SuspectsCards
        v-if="activeTab === 'cards'"
        :suspects="filteredSuspects"
        :fields="suspectFields"
        :loading="isLoading"
        :search-query="searchQuery"
        @update-search="setSearchQuery"
        @update-suspect="handleUpdateSuspect"
        @delete-suspect="handleDeleteSuspect"
        @export-suspect="handleExportSuspect"
        @edit-suspect="handleEditSuspect"
      />
    </div>

    <!-- Loading Overlay -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="neumorphic-card p-8 text-center">
        <div class="spinner mx-auto mb-4"></div>
        <p class="text-secondary-700">جاري المعالجة...</p>
      </div>
    </div>

    <!-- Success/Error Messages -->
    <Transition name="fade">
      <div v-if="message" :class="[
        'fixed top-4 right-4 p-4 rounded-neumorphic shadow-neumorphic z-50',
        messageType === 'success' ? 'bg-success-100 text-success-800' : 'bg-danger-100 text-danger-800'
      ]">
        <div class="flex items-center gap-3">
          <i :class="messageType === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'"></i>
          <span>{{ message }}</span>
          <button @click="message = ''" class="mr-auto">
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useSuspectsStore } from '@/stores/suspects'
import { useSettingsStore } from '@/stores/settings'
import type { SuspectData } from '@/types'

// Components (will be created)
import AddSuspect from '@/components/suspects/AddSuspect.vue'
import ReviewSuspects from '@/components/suspects/ReviewSuspects.vue'
import SuspectsCharts from '@/components/suspects/SuspectsCharts.vue'
import SuspectsCards from '@/components/suspects/SuspectsCards.vue'

// Stores
const suspectsStore = useSuspectsStore()
const settingsStore = useSettingsStore()

// Reactive data
const activeTab = ref('add')
const message = ref('')
const messageType = ref<'success' | 'error'>('success')
const editingSuspect = ref<SuspectData | null>(null)

// Computed
const suspects = computed(() => suspectsStore.suspects)
const filteredSuspects = computed(() => suspectsStore.filteredSuspects)
const statistics = computed(() => suspectsStore.statistics)
const isLoading = computed(() => suspectsStore.isLoading)
const suspectFields = computed(() => settingsStore.suspectFields)
const searchQuery = computed(() => suspectsStore.searchQuery)
const statusFilter = computed(() => suspectsStore.statusFilter)

// Tabs configuration
const tabs = computed(() => [
  {
    id: 'add',
    label: 'إضافة بيانات المتهم',
    icon: 'fas fa-user-plus'
  },
  {
    id: 'review',
    label: 'مراجعة البيانات والوضع الحالي',
    icon: 'fas fa-table',
    count: statistics.value.total
  },
  {
    id: 'charts',
    label: 'الرسوم البيانية الملخصة',
    icon: 'fas fa-chart-pie'
  },
  {
    id: 'cards',
    label: 'عرض بطاقات المتهمين',
    icon: 'fas fa-id-card',
    count: filteredSuspects.value.length
  }
])

// Methods
async function handleAddSuspect(suspectData: Omit<SuspectData, 'id' | 'createdAt' | 'updatedAt'>) {
  try {
    if (editingSuspect.value) {
      // Update existing suspect
      await suspectsStore.updateSuspect(editingSuspect.value.id!, suspectData)
      showMessage('تم تحديث بيانات المتهم بنجاح', 'success')
      editingSuspect.value = null // Clear editing state
    } else {
      // Add new suspect
      await suspectsStore.addSuspect(suspectData)
      showMessage('تم إضافة المتهم بنجاح', 'success')
    }
    activeTab.value = 'review' // Switch to review tab after adding/updating
  } catch (error) {
    showMessage(editingSuspect.value ? 'فشل في تحديث المتهم' : 'فشل في إضافة المتهم', 'error')
  }
}

async function handleUpdateSuspect(id: string, updates: Partial<SuspectData>) {
  try {
    await suspectsStore.updateSuspect(id, updates)
    showMessage('تم تحديث بيانات المتهم بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في تحديث بيانات المتهم', 'error')
  }
}

async function handleDeleteSuspect(id: string) {
  try {
    await suspectsStore.deleteSuspect(id, 'current-user') // In real app, get from auth store
    showMessage('تم حذف المتهم بنجاح', 'success')
  } catch (error) {
    showMessage('فشل في حذف المتهم', 'error')
  }
}

async function handleUpdateSuspectStatus(id: string, status: 'released' | 'transferred', date?: Date) {
  try {
    await suspectsStore.updateSuspectStatus(id, status, date, 'current-user')
    showMessage(`تم تحديث حالة المتهم إلى ${status === 'released' ? 'مفرج عنه' : 'محال إلى النيابة'}`, 'success')
  } catch (error) {
    showMessage('فشل في تحديث حالة المتهم', 'error')
  }
}

async function handleExportSuspect(suspect: SuspectData) {
  try {
    const { exportSuspectData } = await import('@/utils/export')
    await exportSuspectData(suspect, suspectFields.value)
    showMessage('تم تصدير بيانات المتهم بنجاح', 'success')
  } catch (error) {
    console.error('Error exporting suspect:', error)
    showMessage('فشل في تصدير بيانات المتهم', 'error')
  }
}

function handleEditSuspect(suspect: SuspectData) {
  // Set the suspect data for editing
  editingSuspect.value = suspect
  // Switch to add tab
  activeTab.value = 'add'
  showMessage(`تم فتح نموذج تعديل بيانات ${suspect.fields.fullName || 'المتهم'}`, 'success')
}

function setSearchQuery(query: string) {
  suspectsStore.setSearchQuery(query)
}

function setStatusFilter(filter: 'all' | 'detained' | 'released' | 'transferred') {
  suspectsStore.setStatusFilter(filter)
}

function showMessage(text: string, type: 'success' | 'error') {
  message.value = text
  messageType.value = type
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

function clearEditingState() {
  editingSuspect.value = null
}

// Watch for tab changes to clear editing state when leaving add tab
watch(activeTab, (newTab, oldTab) => {
  if (oldTab === 'add' && newTab !== 'add') {
    clearEditingState()
  }
})

// Lifecycle
onMounted(() => {
  suspectsStore.loadSuspects()
  settingsStore.loadSettings()
})
</script>
