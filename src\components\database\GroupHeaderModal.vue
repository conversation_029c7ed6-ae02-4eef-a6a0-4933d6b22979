<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-lg w-full">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-bold text-secondary-800">إعدادات عناوين المجموعات</h3>
        <button @click="$emit('close')" class="text-secondary-400 hover:text-secondary-600">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <div class="space-y-6">
        <!-- Enable Group Headers -->
        <div>
          <label class="flex items-center gap-3">
            <input
              v-model="formData.enabled"
              type="checkbox"
              class="neumorphic-checkbox"
            />
            <span class="font-medium text-secondary-800">تفعيل عناوين المجموعات</span>
          </label>
          <p class="text-sm text-secondary-600 mt-1">
            يتم التعرف على عناوين المجموعات تلقائياً بناءً على النص المحدد
          </p>
        </div>

        <div v-if="formData.enabled" class="space-y-4">
          <!-- Identifier Text -->
          <div>
            <label class="block text-sm font-medium text-secondary-700 mb-2">
              <i class="fas fa-search ml-1"></i>
              النص المعرف لعناوين المجموعات
            </label>
            <input
              v-model="formData.identifier"
              type="text"
              class="neumorphic-input w-full"
              placeholder="مثال: سجل حركة، مجموعة، قسم"
            />
            <p class="text-xs text-secondary-500 mt-1">
              الصفوف التي تبدأ بهذا النص ستعتبر عناوين مجموعات
            </p>
          </div>

          <!-- ColSpan Option -->
          <div>
            <label class="flex items-center gap-3">
              <input
                v-model="formData.colSpan"
                type="checkbox"
                class="neumorphic-checkbox"
              />
              <span class="text-sm font-medium text-secondary-800">امتداد عبر جميع الأعمدة</span>
            </label>
            <p class="text-xs text-secondary-500 mt-1">
              عند التفعيل، سيمتد عنوان المجموعة عبر جميع الأعمدة
            </p>
          </div>

          <!-- Text Formatting -->
          <div class="neumorphic-card bg-secondary-50">
            <h4 class="text-md font-semibold text-secondary-800 mb-4">تنسيق النص</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <!-- Text Color -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">لون النص</label>
                <div class="flex items-center gap-2">
                  <input
                    v-model="formData.textColor"
                    type="color"
                    class="w-12 h-10 rounded border border-secondary-300"
                  />
                  <input
                    v-model="formData.textColor"
                    type="text"
                    class="neumorphic-input flex-1 text-sm"
                  />
                </div>
              </div>

              <!-- Background Color -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">لون الخلفية</label>
                <div class="flex items-center gap-2">
                  <input
                    v-model="formData.backgroundColor"
                    type="color"
                    class="w-12 h-10 rounded border border-secondary-300"
                  />
                  <input
                    v-model="formData.backgroundColor"
                    type="text"
                    class="neumorphic-input flex-1 text-sm"
                  />
                </div>
              </div>

              <!-- Font Size -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">حجم الخط</label>
                <div class="flex items-center gap-2">
                  <input
                    v-model.number="formData.fontSize"
                    type="range"
                    min="12"
                    max="24"
                    class="flex-1"
                  />
                  <span class="text-sm text-secondary-600 w-12">{{ formData.fontSize }}px</span>
                </div>
              </div>

              <!-- Font Weight -->
              <div>
                <label class="block text-sm font-medium text-secondary-700 mb-2">سُمك الخط</label>
                <select
                  v-model="formData.fontWeight"
                  class="neumorphic-select w-full text-sm"
                >
                  <option value="normal">عادي</option>
                  <option value="bold">غامق</option>
                </select>
              </div>

              <!-- Text Align -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-secondary-700 mb-2">محاذاة النص</label>
                <div class="flex items-center gap-4">
                  <label class="flex items-center gap-1">
                    <input
                      v-model="formData.textAlign"
                      type="radio"
                      value="right"
                      class="neumorphic-radio"
                    />
                    <span class="text-sm">يمين</span>
                  </label>
                  <label class="flex items-center gap-1">
                    <input
                      v-model="formData.textAlign"
                      type="radio"
                      value="center"
                      class="neumorphic-radio"
                    />
                    <span class="text-sm">وسط</span>
                  </label>
                  <label class="flex items-center gap-1">
                    <input
                      v-model="formData.textAlign"
                      type="radio"
                      value="left"
                      class="neumorphic-radio"
                    />
                    <span class="text-sm">يسار</span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <!-- Preview -->
          <div class="neumorphic-card bg-white">
            <h4 class="text-md font-semibold text-secondary-800 mb-4">معاينة</h4>
            
            <div class="border border-secondary-200 rounded overflow-hidden">
              <table class="w-full border-collapse">
                <thead>
                  <tr class="bg-secondary-100">
                    <th class="p-2 text-sm border-r border-secondary-200">العمود 1</th>
                    <th class="p-2 text-sm border-r border-secondary-200">العمود 2</th>
                    <th class="p-2 text-sm">العمود 3</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td class="p-2 text-sm border-r border-secondary-200">بيانات عادية</td>
                    <td class="p-2 text-sm border-r border-secondary-200">بيانات عادية</td>
                    <td class="p-2 text-sm">بيانات عادية</td>
                  </tr>
                  <tr>
                    <td
                      :colspan="formData.colSpan ? 3 : 1"
                      :style="getPreviewStyle"
                      class="p-2 border-r border-secondary-200"
                    >
                      <div class="flex items-center gap-2">
                        <i class="fas fa-layer-group text-sm"></i>
                        <span>{{ formData.identifier }} - مثال على عنوان المجموعة</span>
                      </div>
                    </td>
                    <td v-if="!formData.colSpan" class="p-2 text-sm border-r border-secondary-200">-</td>
                    <td v-if="!formData.colSpan" class="p-2 text-sm">-</td>
                  </tr>
                  <tr>
                    <td class="p-2 text-sm border-r border-secondary-200">بيانات تحت المجموعة</td>
                    <td class="p-2 text-sm border-r border-secondary-200">بيانات تحت المجموعة</td>
                    <td class="p-2 text-sm">بيانات تحت المجموعة</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-end gap-3 pt-4 border-t border-secondary-200">
          <button
            @click="resetToDefaults"
            class="neumorphic-button text-warning-600 hover:text-warning-700"
          >
            <i class="fas fa-undo ml-2"></i>
            إعادة تعيين
          </button>
          <button
            @click="$emit('close')"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700"
          >
            إلغاء
          </button>
          <button
            @click="saveSettings"
            class="neumorphic-button text-success-600 hover:text-success-700"
          >
            <i class="fas fa-save ml-2"></i>
            حفظ الإعدادات
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, computed } from 'vue'
import type { GroupHeaderSettings } from '@/types/database'

// Props
interface Props {
  settings: GroupHeaderSettings
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  save: [settings: GroupHeaderSettings]
}>()

// Form data
const formData = reactive<GroupHeaderSettings>({
  ...props.settings
})

// Computed
const getPreviewStyle = computed(() => ({
  backgroundColor: formData.backgroundColor,
  color: formData.textColor,
  fontSize: `${formData.fontSize}px`,
  fontWeight: formData.fontWeight,
  textAlign: formData.textAlign
}))

// Methods
function resetToDefaults() {
  Object.assign(formData, {
    enabled: true,
    identifier: 'سجل حركة',
    textColor: '#1e40af',
    backgroundColor: '#dbeafe',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'right',
    colSpan: true
  })
}

function saveSettings() {
  emit('save', { ...formData })
}
</script>
