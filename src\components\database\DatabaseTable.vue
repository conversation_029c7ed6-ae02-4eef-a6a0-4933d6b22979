<template>
  <div class="neumorphic-card overflow-hidden" :data-tab-id="tabData.id">
    <!-- Table Container -->
    <div class="overflow-x-auto">
      <table class="database-table w-full border-collapse">
        <!-- Table Header -->
        <thead>
          <tr class="bg-secondary-100">
            <!-- Checkbox Column -->
            <th v-if="tabData.settings.showCheckboxes" class="w-12 p-3 text-center">
              <input
                type="checkbox"
                :checked="allRowsSelected"
                @change="toggleAllRows"
                class="neumorphic-checkbox"
              />
            </th>
            
            <!-- Row Number Column -->
            <th v-if="tabData.settings.showRowNumbers" class="w-16 p-3 text-center">
              <span class="text-sm font-semibold text-secondary-700">#</span>
            </th>

            <!-- Data Columns -->
            <th
              v-for="column in visibleColumns"
              :key="column.id"
              :style="getHeaderStyle(column)"
              class="relative group"
            >
              <!-- Column Header Content -->
              <div class="flex items-center justify-between p-3">
                <div class="flex items-center gap-2">
                  <span
                    :style="getHeaderTextStyle(column)"
                    class="font-semibold"
                  >
                    {{ column.name }}
                  </span>
                </div>

                <!-- Column Settings Button - Always visible -->
                <div class="flex items-center gap-1">
                  <button
                    @click.stop="openColumnSettings(column.id)"
                    class="p-1 hover:bg-secondary-200 rounded-full transition-all duration-200 hover:scale-110"
                    title="إعدادات العمود"
                  >
                    <i class="fas fa-cog text-sm text-secondary-500 hover:text-primary-600 transition-colors"></i>
                  </button>
                </div>
              </div>

              <!-- Resize Handle -->
              <div
                v-if="column.isResizable"
                @mousedown.stop="startResize(column.id, $event)"
                class="absolute left-0 top-0 w-3 h-full cursor-col-resize hover:bg-primary-500 transition-all duration-200 bg-secondary-400 opacity-30 hover:opacity-100"
                title="اسحب لتغيير عرض العمود"
              ></div>
            </th>
          </tr>
        </thead>

        <!-- Table Body -->
        <tbody>
          <tr
            v-for="(row, rowIndex) in paginatedRows"
            :key="`${tabData.id}-${row.id}-${rowIndex}`"
            :class="[
              'border-b border-secondary-200 hover:bg-secondary-50 transition-colors',
              row.isSelected ? 'bg-primary-50' : '',
              row.isGroupHeader ? 'bg-secondary-100 font-semibold group-header-row' : '',
              tabData.settings.alternateRowColors && rowIndex % 2 === 1 ? 'bg-secondary-25' : ''
            ]"
          >
            <!-- Checkbox Column -->
            <td v-if="tabData.settings.showCheckboxes && !row.isGroupHeader" class="p-3 text-center">
              <input
                type="checkbox"
                :checked="row.isSelected"
                @change="toggleRow(row.id)"
                class="neumorphic-checkbox"
              />
            </td>
            <td v-else-if="tabData.settings.showCheckboxes" class="p-3"></td>
            
            <!-- Row Number Column -->
            <td v-if="tabData.settings.showRowNumbers && !row.isGroupHeader" class="p-3 text-center text-sm text-secondary-600">
              {{ getRowNumber(rowIndex) }}
            </td>
            <td v-else-if="tabData.settings.showRowNumbers" class="p-3"></td>

            <!-- Group Header Row -->
            <template v-if="row.isGroupHeader">
              <!-- Show data in cells before the group header if colSpan is enabled -->
              <template v-if="tabData.groupHeaders.colSpan">
                <!-- Checkbox Column -->
                <td v-if="tabData.settings.showCheckboxes" class="p-3"></td>

                <!-- Row Number Column -->
                <td v-if="tabData.settings.showRowNumbers" class="p-3"></td>

                <!-- Group Header spans remaining columns -->
                <td
                  :colspan="visibleColumns.length"
                  :style="getGroupHeaderStyle()"
                  class="p-3 border-r border-secondary-200"
                >
                  <div class="flex items-center gap-2">
                    <i class="fas fa-layer-group text-sm"></i>
                    <span class="font-semibold">{{ row.groupHeaderText }}</span>
                  </div>
                </td>
              </template>

              <!-- Full row span if colSpan is disabled -->
              <template v-else>
                <td
                  :colspan="getGroupHeaderColspan()"
                  :style="getGroupHeaderStyle()"
                  class="p-3"
                >
                  <div class="flex items-center gap-2">
                    <i class="fas fa-layer-group text-sm"></i>
                    <span class="font-semibold">{{ row.groupHeaderText }}</span>
                  </div>
                </td>
              </template>
            </template>

            <!-- Data Columns -->
            <template v-else>
              <td
                v-for="column in visibleColumns"
                :key="column.id"
                :style="getCellStyle(column)"
                class="p-3 border-r border-secondary-200 last:border-r-0"
                @dblclick="startEdit(row.id, column.id)"
              >
                <!-- Editable Cell -->
                <div v-if="editingCell?.rowId === row.id && editingCell?.columnId === column.id">
                  <input
                    v-if="column.type === 'text' || column.type === 'number'"
                    v-model="editValue"
                    @blur="finishEdit"
                    @keyup.enter="finishEdit"
                    @keyup.escape="cancelEdit"
                    :type="column.type === 'number' ? 'number' : 'text'"
                    class="w-full p-1 border border-primary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-400"
                    ref="editInput"
                  />
                  <input
                    v-else-if="column.type === 'date'"
                    v-model="editValue"
                    @blur="finishEdit"
                    @keyup.enter="finishEdit"
                    @keyup.escape="cancelEdit"
                    type="date"
                    class="w-full p-1 border border-primary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-400"
                    ref="editInput"
                  />
                  <input
                    v-else-if="column.type === 'time'"
                    v-model="editValue"
                    @blur="finishEdit"
                    @keyup.enter="finishEdit"
                    @keyup.escape="cancelEdit"
                    type="time"
                    class="w-full p-1 border border-primary-300 rounded focus:outline-none focus:ring-2 focus:ring-primary-400"
                    ref="editInput"
                  />
                </div>

                <!-- Display Cell -->
                <div
                  v-else
                  :style="getCellTextStyle(column, row)"
                  :class="{ 'cursor-pointer': importantMode }"
                  :title="importantMode ? 'اضغط لتمييز هذا الصف كمحتوى مهم' : ''"
                  @click="startEdit(row.id, column.id)"
                >
                  {{ formatCellValue(row.data[column.id], column) }}
                  <!-- Debug: show raw data for first few rows -->
                  <span v-if="false" style="font-size: 10px; color: red; display: block;">
                    [{{ column.id }}: {{ row.data[column.id] }}]
                  </span>
                </div>
              </td>
            </template>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Empty State -->
    <div v-if="paginatedRows.length === 0" class="text-center py-12">
      <div class="neumorphic-icon mx-auto mb-4">
        <i class="fas fa-table text-secondary-400"></i>
      </div>
      <h3 class="text-lg font-semibold text-secondary-700 mb-2">لا توجد بيانات</h3>
      <p class="text-secondary-500 mb-4">ابدأ بإضافة بيانات أو استيراد ملف Excel</p>

      <div class="flex items-center justify-center gap-3">
        <button
          v-if="permissions.database?.write"
          @click="$emit('addRow')"
          class="neumorphic-button text-success-600 hover:text-success-700"
        >
          <i class="fas fa-plus ml-2"></i>
          إضافة صف جديد
        </button>
        <button
          v-if="permissions.database?.import"
          @click="handleImportClick"
          class="neumorphic-button text-primary-600 hover:text-primary-700"
        >
          <i class="fas fa-file-excel ml-2"></i>
          استيراد Excel
        </button>
      </div>
    </div>

    <!-- Statistics and Pagination -->
    <div class="border-t border-secondary-200">
      <!-- Statistics -->
      <div class="flex items-center justify-between p-3 bg-secondary-25 text-sm">
        <div class="flex items-center gap-6">
          <span class="text-secondary-600">
            <i class="fas fa-table ml-1"></i>
            إجمالي الصفوف: <strong class="text-secondary-800">{{ totalRows }}</strong>
          </span>
          <span v-if="selectedRowsCount > 0" class="text-primary-600">
            <i class="fas fa-check-square ml-1"></i>
            محدد: <strong>{{ selectedRowsCount }}</strong>
          </span>
          <span v-if="props.searchQuery" class="text-info-600">
            <i class="fas fa-search ml-1"></i>
            نتائج البحث: <strong>{{ filteredRows.length }}</strong>
          </span>
        </div>

        <div class="text-secondary-500">
          آخر تحديث: {{ formatTime(new Date()) }}
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between p-4">
        <div class="text-sm text-secondary-600">
          عرض {{ startRow }} - {{ endRow }} من {{ totalRows }} صف
        </div>

        <div class="flex items-center gap-2">
          <button
            @click="goToPage(1)"
            :disabled="currentPage === 1"
            class="neumorphic-button p-2 disabled:opacity-50 disabled:cursor-not-allowed"
            title="الصفحة الأولى"
          >
            <i class="fas fa-angle-double-right"></i>
          </button>

          <button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage === 1"
            class="neumorphic-button p-2 disabled:opacity-50 disabled:cursor-not-allowed"
            title="الصفحة السابقة"
          >
            <i class="fas fa-chevron-right"></i>
          </button>

          <span class="px-3 py-1 text-sm bg-secondary-100 rounded">
            {{ currentPage }} / {{ totalPages }}
          </span>

          <button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="neumorphic-button p-2 disabled:opacity-50 disabled:cursor-not-allowed"
            title="الصفحة التالية"
          >
            <i class="fas fa-chevron-left"></i>
          </button>

          <button
            @click="goToPage(totalPages)"
            :disabled="currentPage === totalPages"
            class="neumorphic-button p-2 disabled:opacity-50 disabled:cursor-not-allowed"
            title="الصفحة الأخيرة"
          >
            <i class="fas fa-angle-double-left"></i>
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Column Settings Modal -->
  <ColumnSettingsModal
    v-if="columnSettingsModal.isOpen"
    :column-id="columnSettingsModal.columnId"
    :settings="columnSettingsModal.settings"
    @close="closeColumnSettings"
    @save="saveColumnSettings"
  />
</template>

<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'
import type {
  DatabaseTab,
  DatabaseColumn,
  DatabaseRow,
  ColumnFormatting,
  CellValue
} from '@/types/database'
import ColumnSettingsModal from './ColumnSettingsModal.vue'
import { usePermissions } from '@/composables/usePermissions'

// Props
interface Props {
  tabData: DatabaseTab
  searchQuery: string
  filters: Record<string, any>
  importantMode?: boolean
  importantColor?: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  updateData: [data: { rows: DatabaseRow[], columns: DatabaseColumn[] }]
  updateColumnSettings: [columnId: string, settings: ColumnFormatting]
  updateGroupHeaders: [settings: any]
  addRow: []
  importData: []
}>()

// Permissions
const { permissions, requirePermission } = usePermissions()

// Reactive data
const editingCell = ref<{ rowId: string, columnId: string } | null>(null)
const editValue = ref<CellValue>('')
const editInput = ref<HTMLInputElement>()
const resizingColumn = ref<string | null>(null)

const columnSettingsModal = ref({
  isOpen: false,
  columnId: '',
  settings: {} as ColumnFormatting
})

// Computed
const visibleColumns = computed(() =>
  props.tabData.columns.filter(col => col.isVisible).sort((a, b) => a.order - b.order)
)

const selectedRowsCount = computed(() =>
  props.tabData.rows.filter(row => row.isSelected && !row.isGroupHeader).length
)

const filteredRows = computed(() => {
  let rows = [...props.tabData.rows]

  // Apply search filter
  if (props.searchQuery) {
    const query = props.searchQuery.toLowerCase().trim()
    rows = rows.filter(row => {
      // Don't filter group headers if they contain the search term
      if (row.isGroupHeader) {
        return row.groupHeaderText?.toLowerCase().includes(query) || false
      }

      // Search in visible columns
      return visibleColumns.value.some(column => {
        const value = row.data[column.id]
        if (value === null || value === undefined) return false

        const stringValue = value.toString().toLowerCase()
        return stringValue.includes(query)
      })
    })
  }

  // Apply advanced filters
  if (props.filters && Object.keys(props.filters).length > 0) {
    // Apply quick filter if exists
    if (props.filters.quickFilter && props.filters.quickFilter.column && props.filters.quickFilter.value) {
      const filter = props.filters.quickFilter
      const filterValue = filter.value.toLowerCase()

      rows = rows.filter(row => {
        if (row.isGroupHeader) return true // Keep group headers

        const cellValue = row.data[filter.column]
        if (cellValue === null || cellValue === undefined) return false

        const stringValue = cellValue.toString().toLowerCase()

        switch (filter.operator) {
          case 'equals':
            return stringValue === filterValue
          case 'contains':
            return stringValue.includes(filterValue)
          case 'startsWith':
            return stringValue.startsWith(filterValue)
          case 'endsWith':
            return stringValue.endsWith(filterValue)
          case 'greaterThan':
            return parseFloat(stringValue) > parseFloat(filterValue)
          case 'lessThan':
            return parseFloat(stringValue) < parseFloat(filterValue)
          default:
            return true
        }
      })
    }
  }

  return rows
})

const currentPage = computed(() => props.tabData.settings.currentPage || 1)
const pageSize = computed(() => props.tabData.settings.pageSize || 50)
const totalRows = computed(() => filteredRows.value.filter(row => !row.isGroupHeader).length)
const totalPages = computed(() => Math.ceil(totalRows.value / pageSize.value))
const startRow = computed(() => (currentPage.value - 1) * pageSize.value + 1)
const endRow = computed(() => Math.min(currentPage.value * pageSize.value, totalRows.value))

// Paginated rows
const paginatedRows = computed(() => {
  if (totalPages.value <= 1) return filteredRows.value

  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

  // Get only data rows (non-group headers) for pagination
  const dataRows = filteredRows.value.filter(row => !row.isGroupHeader)
  const paginatedDataRows = dataRows.slice(start, end)

  if (paginatedDataRows.length === 0) return []

  // Now we need to find which group headers should be included
  const result = []
  const allRows = filteredRows.value

  // For each paginated data row, find its group header
  const groupHeadersToInclude = new Set()

  for (const dataRow of paginatedDataRows) {
    const dataRowIndex = allRows.findIndex(row => row.id === dataRow.id)

    // Look backwards to find the group header for this data row
    for (let i = dataRowIndex - 1; i >= 0; i--) {
      if (allRows[i].isGroupHeader) {
        groupHeadersToInclude.add(allRows[i].id)
        break
      }
    }
  }

  // Build the final result with group headers and their data rows
  let currentGroupHeader = null

  for (const dataRow of paginatedDataRows) {
    const dataRowIndex = allRows.findIndex(row => row.id === dataRow.id)

    // Check if we need to add a group header before this data row
    for (let i = dataRowIndex - 1; i >= 0; i--) {
      if (allRows[i].isGroupHeader) {
        if (groupHeadersToInclude.has(allRows[i].id) &&
            (!currentGroupHeader || currentGroupHeader.id !== allRows[i].id)) {
          result.push(allRows[i])
          currentGroupHeader = allRows[i]
        }
        break
      }
    }

    // Add the data row
    result.push(dataRow)
  }

  return result
})

const allRowsSelected = computed(() => {
  const selectableRows = filteredRows.value.filter(row => !row.isGroupHeader)
  return selectableRows.length > 0 && selectableRows.every(row => row.isSelected)
})

// Methods
function getHeaderStyle(column: DatabaseColumn): Record<string, string> {
  const formatting = column.formatting
  return {
    backgroundColor: formatting.headerBackgroundColor || '#f1f5f9',
    borderColor: formatting.borderColor || '#e2e8f0',
    borderWidth: `${formatting.borderWidth || 1}px`,
    borderStyle: formatting.borderStyle || 'solid',
    width: `${column.width}px`,
    minWidth: `${column.width}px`,
    maxWidth: `${column.width}px`
  }
}

function getHeaderTextStyle(column: DatabaseColumn): Record<string, string> {
  const formatting = column.formatting
  return {
    color: formatting.headerTextColor || '#334155',
    fontSize: `${formatting.headerFontSize || 14}px`,
    fontWeight: formatting.headerFontWeight || 'bold',
    textAlign: formatting.headerTextAlign || 'center'
  }
}

function getCellStyle(column: DatabaseColumn): Record<string, string> {
  const formatting = column.formatting
  return {
    backgroundColor: formatting.cellBackgroundColor || 'transparent',
    borderColor: formatting.borderColor || '#e2e8f0',
    borderWidth: `${formatting.borderWidth || 1}px`,
    borderStyle: formatting.borderStyle || 'solid'
  }
}

function getCellTextStyle(column: DatabaseColumn, row?: DatabaseRow): Record<string, string> {
  const formatting = column.formatting

  // Use important color if row is marked as important
  const textColor = row?.isImportant && row.importantColor
    ? row.importantColor
    : formatting.cellTextColor || '#475569'

  return {
    color: textColor,
    fontSize: `${formatting.cellFontSize || 14}px`,
    fontWeight: formatting.cellFontWeight || 'normal',
    textAlign: formatting.cellTextAlign || 'right',
    whiteSpace: formatting.cellTextWrap ? 'normal' : 'nowrap',
    width: formatting.cellFitContent ? 'fit-content' : 'auto',
    maxWidth: formatting.cellFitContent ? 'none' : '100%'
  }
}

function getGroupHeaderStyle(): Record<string, string> {
  const settings = props.tabData.groupHeaders
  return {
    backgroundColor: settings.backgroundColor || '#f1f5f9',
    color: settings.textColor || '#334155',
    fontSize: `${settings.fontSize || 14}px`,
    fontWeight: settings.fontWeight || 'bold',
    textAlign: settings.textAlign || 'right'
  }
}

function getGroupHeaderColspan(): number {
  let colspan = visibleColumns.value.length
  if (props.tabData.settings.showCheckboxes) colspan++
  if (props.tabData.settings.showRowNumbers) colspan++
  return colspan
}

function formatCellValue(value: CellValue, column: DatabaseColumn): string {
  if (value === null || value === undefined) return ''
  
  switch (column.type) {
    case 'date':
      if (value instanceof Date) {
        return value.toLocaleDateString('ar-SA')
      }
      return value.toString()
    
    case 'time':
      if (value instanceof Date) {
        return value.toLocaleTimeString('ar-SA')
      }
      return value.toString()
    
    case 'number':
      if (typeof value === 'number') {
        const formatting = column.formatting.numberFormat
        if (formatting) {
          let formatted = value.toFixed(formatting.decimals || 0)
          if (formatting.thousandsSeparator) {
            formatted = formatted.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
          }
          return `${formatting.prefix || ''}${formatted}${formatting.suffix || ''}`
        }
        return value.toString()
      }
      return value.toString()
    
    default:
      return value.toString()
  }
}

function toggleRow(rowId: string) {
  const row = props.tabData.rows.find(r => r.id === rowId)
  if (row) {
    row.isSelected = !row.isSelected
    // لا نحتاج لإرسال تحديث كامل للبيانات عند تغيير التحديد فقط
    // emitDataUpdate()
  }
}

function toggleAllRows() {
  const selectableRows = filteredRows.value.filter(row => !row.isGroupHeader)
  const shouldSelect = !allRowsSelected.value

  selectableRows.forEach(row => {
    row.isSelected = shouldSelect
  })

  // لا نحتاج لإرسال تحديث كامل للبيانات عند تغيير التحديد فقط
  // emitDataUpdate()
}

function startEdit(rowId: string, columnId: string) {
  // If important mode is active, toggle row importance instead of editing
  if (props.importantMode) {
    toggleRowImportance(rowId)
    return
  }

  const row = props.tabData.rows.find(r => r.id === rowId)
  const column = props.tabData.columns.find(c => c.id === columnId)

  if (row && !row.isGroupHeader && column) {
    editingCell.value = { rowId, columnId }
    editValue.value = row.data[columnId] || ''

    nextTick(() => {
      editInput.value?.focus()
      editInput.value?.select()
    })
  }
}

function toggleRowImportance(rowId: string) {
  const row = props.tabData.rows.find(r => r.id === rowId)

  if (row && !row.isGroupHeader) {
    if (row.isImportant) {
      // Remove importance
      row.isImportant = false
      row.importantColor = undefined
    } else {
      // Add importance
      row.isImportant = true
      row.importantColor = props.importantColor || '#ff6b35'
      // Store original color for restoration
      const column = props.tabData.columns[0] // Use first column as reference
      row.originalTextColor = column?.formatting?.cellTextColor || '#475569'
    }
    // البيانات يتم تحديثها مباشرة في props.tabData
    // لا نحتاج لإرسال تحديث كامل
  }
}

function finishEdit() {
  if (editingCell.value) {
    const row = props.tabData.rows.find(r => r.id === editingCell.value!.rowId)
    if (row) {
      row.data[editingCell.value.columnId] = editValue.value
      // البيانات يتم تحديثها مباشرة في props.tabData
      // لا نحتاج لإرسال تحديث كامل
    }
  }
  cancelEdit()
}

function cancelEdit() {
  editingCell.value = null
  editValue.value = ''
}

function startResize(columnId: string, event: MouseEvent) {
  resizingColumn.value = columnId
  const startX = event.clientX
  const column = props.tabData.columns.find(c => c.id === columnId)
  const startWidth = column?.width || 150

  const handleMouseMove = (e: MouseEvent) => {
    if (column) {
      const diff = e.clientX - startX
      column.width = Math.max(50, startWidth + diff)
    }
  }

  const handleMouseUp = () => {
    resizingColumn.value = null
    document.removeEventListener('mousemove', handleMouseMove)
    document.removeEventListener('mouseup', handleMouseUp)
    // تغيير حجم العمود لا يحتاج لإرسال تحديث كامل للبيانات
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

function openColumnSettings(columnId: string) {
  const column = props.tabData.columns.find(c => c.id === columnId)
  if (column) {
    columnSettingsModal.value = {
      isOpen: true,
      columnId,
      settings: { ...column.formatting }
    }
  }
}

function closeColumnSettings() {
  columnSettingsModal.value.isOpen = false
}

function saveColumnSettings(settings: ColumnFormatting) {
  emit('updateColumnSettings', columnSettingsModal.value.columnId, settings)

  // Apply sorting if specified
  if (settings.sortable && settings.sortDirection) {
    applySortFromColumnSettings(columnSettingsModal.value.columnId, settings.sortDirection)
  }

  closeColumnSettings()
}

function applySortFromColumnSettings(columnId: string, direction: 'asc' | 'desc') {
  // Update tab settings
  props.tabData.settings.sortColumn = columnId
  props.tabData.settings.sortDirection = direction

  // Sort the rows
  const column = props.tabData.columns.find(c => c.id === columnId)
  if (column) {
    props.tabData.rows.sort((a, b) => {
      // Keep group headers in their original positions
      if (a.isGroupHeader && b.isGroupHeader) return 0
      if (a.isGroupHeader) return -1
      if (b.isGroupHeader) return 1

      const aValue = a.data[columnId] || ''
      const bValue = b.data[columnId] || ''

      let comparison = 0

      if (column.type === 'number') {
        const aNum = parseFloat(aValue.toString()) || 0
        const bNum = parseFloat(bValue.toString()) || 0
        comparison = aNum - bNum
      } else if (column.type === 'date' || column.type === 'datetime') {
        const aDate = new Date(aValue.toString()).getTime() || 0
        const bDate = new Date(bValue.toString()).getTime() || 0
        comparison = aDate - bDate
      } else {
        // Text comparison
        comparison = aValue.toString().localeCompare(bValue.toString(), 'ar')
      }

      return direction === 'asc' ? comparison : -comparison
    })

    // البيانات يتم تحديثها مباشرة في props.tabData
  }
}

function goToPage(page: number) {
  if (page >= 1 && page <= totalPages.value) {
    // Update tab settings
    props.tabData.settings.currentPage = page
    // تغيير الصفحة لا يحتاج لإرسال تحديث كامل للبيانات
  }
}

// تم حذف دالة emitDataUpdate لمنع تضاعف البيانات
// البيانات يتم تحديثها مباشرة في props.tabData من خلال المتجر

function handleImportClick() {
  // فحص الصلاحيات قبل فتح نافذة الاستيراد
  if (!requirePermission('database', 'import', 'استيراد البيانات')) {
    return
  }

  console.log('🔍 تم الضغط على زر الاستيراد من DatabaseTable')
  emit('importData')
}

// Expose filtered rows for export
function getFilteredRows() {
  return filteredRows.value
}

// Expose method to parent component
defineExpose({
  getFilteredRows
})

function getRowNumber(rowIndex: number): number {
  // Count only non-group-header rows up to this index
  let count = 0
  for (let i = 0; i <= rowIndex; i++) {
    if (!filteredRows.value[i]?.isGroupHeader) {
      count++
    }
  }
  return count
}



// Helper function for formatting time
function formatTime(date: Date): string {
  return date.toLocaleTimeString('ar-SA', {
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
