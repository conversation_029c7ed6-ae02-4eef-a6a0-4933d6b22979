<template>
  <div class="large-data-importer">
    <!-- Import Header -->
    <div class="neumorphic-card bg-white p-6 rounded-2xl mb-6">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-3">
          <div class="neumorphic-icon w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
            <i class="fas fa-upload text-white text-xl"></i>
          </div>
          <div>
            <h3 class="text-xl font-bold text-secondary-800">استيراد البيانات الضخمة</h3>
            <p class="text-secondary-600">دعم ملفات CSV و Excel حتى مليون سجل</p>
          </div>
        </div>
        
        <div class="flex items-center gap-2">
          <span class="text-sm text-secondary-500">
            <i class="fas fa-info-circle mr-1"></i>
            يدعم الملفات حتى 500 ميجابايت
          </span>
        </div>
      </div>

      <!-- File Selection -->
      <div v-if="!isImporting && !importResult" class="space-y-4">
        <div class="border-2 border-dashed border-secondary-300 rounded-xl p-8 text-center hover:border-primary-400 transition-colors">
          <div class="mb-4">
            <i class="fas fa-cloud-upload-alt text-4xl text-secondary-400 mb-2"></i>
            <p class="text-lg font-medium text-secondary-700">اختر ملف البيانات</p>
            <p class="text-sm text-secondary-500">CSV, XLSX, XLS</p>
          </div>
          
          <button
            @click="selectFile"
            class="neumorphic-button px-6 py-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 transition-colors"
          >
            <i class="fas fa-folder-open mr-2"></i>
            تصفح الملفات
          </button>
        </div>

        <!-- Selected File Info -->
        <div v-if="selectedFile" class="neumorphic-card bg-secondary-50 p-4 rounded-xl">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
              <i class="fas fa-file-csv text-green-500 text-xl"></i>
              <div>
                <p class="font-medium text-secondary-800">{{ selectedFile.name }}</p>
                <p class="text-sm text-secondary-600">{{ formatFileSize(selectedFile.size) }}</p>
              </div>
            </div>
            
            <button
              @click="clearSelection"
              class="text-red-500 hover:text-red-700 p-2"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <!-- Import Options -->
        <div v-if="selectedFile" class="neumorphic-card bg-white p-4 rounded-xl space-y-4">
          <h4 class="font-medium text-secondary-800 mb-3">خيارات الاستيراد</h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">
                حجم الدفعة
              </label>
              <select v-model="importOptions.batchSize" class="neumorphic-input w-full">
                <option value="500">500 سجل</option>
                <option value="1000">1000 سجل</option>
                <option value="2000">2000 سجل</option>
                <option value="5000">5000 سجل</option>
              </select>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-secondary-700 mb-2">
                ترميز الملف
              </label>
              <select v-model="importOptions.encoding" class="neumorphic-input w-full">
                <option value="utf8">UTF-8</option>
                <option value="utf16le">UTF-16</option>
                <option value="latin1">Latin-1</option>
              </select>
            </div>
          </div>
          
          <div class="flex items-center gap-4">
            <label class="flex items-center gap-2">
              <input
                type="checkbox"
                v-model="importOptions.validateData"
                class="rounded border-secondary-300"
              />
              <span class="text-sm text-secondary-700">التحقق من صحة البيانات</span>
            </label>
            
            <label class="flex items-center gap-2">
              <input
                type="checkbox"
                v-model="importOptions.skipDuplicates"
                class="rounded border-secondary-300"
              />
              <span class="text-sm text-secondary-700">تخطي السجلات المكررة</span>
            </label>
          </div>
          
          <button
            @click="startImport"
            :disabled="!selectedFile"
            class="neumorphic-button w-full py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-xl hover:from-green-600 hover:to-blue-600 transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <i class="fas fa-play mr-2"></i>
            بدء الاستيراد
          </button>
        </div>
      </div>

      <!-- Import Progress -->
      <div v-if="isImporting" class="space-y-4">
        <div class="text-center mb-6">
          <div class="neumorphic-icon w-16 h-16 mx-auto bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mb-4">
            <i class="fas fa-spinner fa-spin text-white text-2xl"></i>
          </div>
          <h4 class="text-lg font-bold text-secondary-800 mb-2">{{ importProgress.message }}</h4>
          <p class="text-secondary-600">{{ importProgress.details }}</p>
        </div>

        <!-- Progress Bar -->
        <div class="neumorphic-card bg-secondary-50 p-4 rounded-xl">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-secondary-700">التقدم</span>
            <span class="text-sm font-bold text-primary-600">{{ importProgress.progress }}%</span>
          </div>
          
          <div class="w-full bg-secondary-200 rounded-full h-3 overflow-hidden">
            <div
              class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-300 ease-out"
              :style="{ width: `${importProgress.progress}%` }"
            ></div>
          </div>
        </div>

        <!-- Import Stats -->
        <div v-if="importProgress.stage === 'processing'" class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="neumorphic-card bg-white p-3 rounded-xl text-center">
            <div class="text-2xl font-bold text-blue-600">{{ formatNumber(importStatus?.processedRows || 0) }}</div>
            <div class="text-xs text-secondary-600">تم معالجتها</div>
          </div>
          
          <div class="neumorphic-card bg-white p-3 rounded-xl text-center">
            <div class="text-2xl font-bold text-green-600">{{ formatNumber(importStatus?.successfulRows || 0) }}</div>
            <div class="text-xs text-secondary-600">نجحت</div>
          </div>
          
          <div class="neumorphic-card bg-white p-3 rounded-xl text-center">
            <div class="text-2xl font-bold text-red-600">{{ formatNumber(importStatus?.failedRows || 0) }}</div>
            <div class="text-xs text-secondary-600">فشلت</div>
          </div>
          
          <div class="neumorphic-card bg-white p-3 rounded-xl text-center">
            <div class="text-2xl font-bold text-purple-600">{{ formatNumber(importStatus?.totalRows || 0) }}</div>
            <div class="text-xs text-secondary-600">الإجمالي</div>
          </div>
        </div>

        <!-- Cancel Button -->
        <button
          @click="cancelImport"
          class="neumorphic-button w-full py-3 bg-red-500 text-white rounded-xl hover:bg-red-600 transition-colors"
        >
          <i class="fas fa-stop mr-2"></i>
          إلغاء الاستيراد
        </button>
      </div>

      <!-- Import Result -->
      <div v-if="importResult" class="space-y-4">
        <div class="text-center mb-6">
          <div class="neumorphic-icon w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
               :class="importResult.success ? 'bg-green-500' : 'bg-red-500'">
            <i :class="importResult.success ? 'fas fa-check' : 'fas fa-times'" class="text-white text-2xl"></i>
          </div>
          
          <h4 class="text-lg font-bold mb-2"
              :class="importResult.success ? 'text-green-600' : 'text-red-600'">
            {{ importResult.success ? 'تم الاستيراد بنجاح!' : 'فشل في الاستيراد' }}
          </h4>
          
          <p class="text-secondary-600">
            {{ importResult.success ? 'تم استيراد البيانات بنجاح' : importResult.error }}
          </p>
        </div>

        <!-- Import Summary -->
        <div v-if="importResult.summary" class="neumorphic-card bg-secondary-50 p-4 rounded-xl">
          <h5 class="font-medium text-secondary-800 mb-3">ملخص الاستيراد</h5>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div class="text-center">
              <div class="text-xl font-bold text-blue-600">{{ formatNumber(importResult.summary.totalRows) }}</div>
              <div class="text-xs text-secondary-600">إجمالي السجلات</div>
            </div>
            
            <div class="text-center">
              <div class="text-xl font-bold text-green-600">{{ formatNumber(importResult.summary.successfulRows) }}</div>
              <div class="text-xs text-secondary-600">نجح</div>
            </div>
            
            <div class="text-center">
              <div class="text-xl font-bold text-red-600">{{ formatNumber(importResult.summary.failedRows) }}</div>
              <div class="text-xs text-secondary-600">فشل</div>
            </div>
            
            <div class="text-center">
              <div class="text-xl font-bold text-purple-600">{{ formatDuration(importResult.summary.duration) }}</div>
              <div class="text-xs text-secondary-600">المدة</div>
            </div>
          </div>
          
          <div v-if="importResult.summary.throughput" class="text-center text-sm text-secondary-600">
            معدل المعالجة: {{ formatNumber(importResult.summary.throughput) }} سجل/ثانية
          </div>
        </div>

        <!-- Errors -->
        <div v-if="importResult.errors && importResult.errors.length > 0" class="neumorphic-card bg-red-50 p-4 rounded-xl">
          <h5 class="font-medium text-red-800 mb-3">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            الأخطاء ({{ importResult.errors.length }})
          </h5>
          
          <div class="max-h-40 overflow-y-auto space-y-2">
            <div v-for="(error, index) in importResult.errors.slice(0, 10)" :key="index" 
                 class="text-sm bg-white p-2 rounded border-l-4 border-red-400">
              <div class="font-medium text-red-700">السطر {{ error.row }}</div>
              <div class="text-red-600">{{ error.errors.join(', ') }}</div>
            </div>
          </div>
          
          <div v-if="importResult.errors.length > 10" class="text-sm text-red-600 mt-2">
            وهناك {{ importResult.errors.length - 10 }} أخطاء أخرى...
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex gap-3">
          <button
            @click="resetImporter"
            class="neumorphic-button flex-1 py-3 bg-primary-500 text-white rounded-xl hover:bg-primary-600 transition-colors"
          >
            <i class="fas fa-plus mr-2"></i>
            استيراد ملف آخر
          </button>
          
          <button
            v-if="importResult.success"
            @click="$emit('importCompleted', importResult)"
            class="neumorphic-button flex-1 py-3 bg-green-500 text-white rounded-xl hover:bg-green-600 transition-colors"
          >
            <i class="fas fa-check mr-2"></i>
            عرض البيانات
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// Emits
const emit = defineEmits<{
  importCompleted: [result: any]
}>()

// Reactive data
const selectedFile = ref<File | null>(null)
const isImporting = ref(false)
const importProgress = ref({
  stage: 'preparing',
  progress: 0,
  message: 'جاري التحضير...',
  details: ''
})
const importStatus = ref<any>(null)
const importResult = ref<any>(null)

const importOptions = ref({
  batchSize: 1000,
  validateData: true,
  skipDuplicates: true,
  encoding: 'utf8'
})

// Methods
const selectFile = async () => {
  if (window.electronAPI) {
    try {
      const result = await window.electronAPI.openFile({
        filters: [
          { name: 'CSV Files', extensions: ['csv'] },
          { name: 'Excel Files', extensions: ['xlsx', 'xls'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      })
      
      if (result.success && !result.data.canceled && result.data.filePaths.length > 0) {
        const filePath = result.data.filePaths[0]
        // Create a mock file object for display
        selectedFile.value = {
          name: filePath.split('\\').pop() || filePath.split('/').pop() || 'Unknown',
          path: filePath,
          size: 0 // Will be determined during import
        } as any
      }
    } catch (error) {
      console.error('File selection error:', error)
    }
  } else {
    // Web fallback
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.csv,.xlsx,.xls'
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (file) {
        selectedFile.value = file
      }
    }
    input.click()
  }
}

const clearSelection = () => {
  selectedFile.value = null
  importResult.value = null
}

const startImport = async () => {
  if (!selectedFile.value) return

  isImporting.value = true
  importResult.value = null
  importProgress.value = {
    stage: 'preparing',
    progress: 0,
    message: 'جاري التحضير...',
    details: ''
  }

  try {
    if (window.electronAPI) {
      // Setup progress listener
      window.electronAPI.onImportProgress((event: any, progress: any) => {
        importProgress.value = progress
      })

      // Start import
      const result = await window.electronAPI.importLargeDataset(
        selectedFile.value.path || selectedFile.value.name,
        importOptions.value
      )

      importResult.value = result
    } else {
      // Web fallback - handle smaller files
      await handleWebImport()
    }
  } catch (error) {
    console.error('Import error:', error)
    importResult.value = {
      success: false,
      error: error instanceof Error ? error.message : 'حدث خطأ أثناء الاستيراد'
    }
  } finally {
    isImporting.value = false
    if (window.electronAPI) {
      window.electronAPI.removeImportProgressListener()
    }
  }
}

const cancelImport = async () => {
  if (window.electronAPI) {
    try {
      await window.electronAPI.cancelImport()
      isImporting.value = false
      importProgress.value = {
        stage: 'cancelled',
        progress: 0,
        message: 'تم إلغاء الاستيراد',
        details: ''
      }
    } catch (error) {
      console.error('Cancel import error:', error)
    }
  }
}

const resetImporter = () => {
  selectedFile.value = null
  isImporting.value = false
  importResult.value = null
  importProgress.value = {
    stage: 'preparing',
    progress: 0,
    message: 'جاري التحضير...',
    details: ''
  }
}

const handleWebImport = async () => {
  // Simplified web import for smaller files
  // This would use the existing IndexedDB system
  importResult.value = {
    success: false,
    error: 'استيراد البيانات الضخمة متاح فقط في إصدار سطح المكتب'
  }
}

// Utility functions
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatNumber = (num: number): string => {
  return num.toLocaleString('ar-SA')
}

const formatDuration = (ms: number): string => {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}س ${minutes % 60}د`
  } else if (minutes > 0) {
    return `${minutes}د ${seconds % 60}ث`
  } else {
    return `${seconds}ث`
  }
}

// Lifecycle
onMounted(() => {
  // Setup any initial state
})

onUnmounted(() => {
  if (window.electronAPI) {
    window.electronAPI.removeImportProgressListener()
  }
})
</script>

<style scoped>
.large-data-importer {
  @apply max-w-4xl mx-auto;
}

.neumorphic-card {
  box-shadow: 
    8px 8px 16px #d1d9e6,
    -8px -8px 16px #ffffff;
}

.neumorphic-icon {
  box-shadow: 
    inset 4px 4px 8px #d1d9e6,
    inset -4px -4px 8px #ffffff;
}

.neumorphic-input {
  box-shadow: 
    inset 4px 4px 8px #d1d9e6,
    inset -4px -4px 8px #ffffff;
  border: none;
  background: #f0f4f8;
}

.neumorphic-button {
  box-shadow: 
    4px 4px 8px #d1d9e6,
    -4px -4px 8px #ffffff;
}

.neumorphic-button:hover:not(:disabled) {
  box-shadow: 
    6px 6px 12px #d1d9e6,
    -6px -6px 12px #ffffff;
  transform: translateY(-1px);
}

.neumorphic-button:active:not(:disabled) {
  box-shadow: 
    inset 4px 4px 8px #d1d9e6,
    inset -4px -4px 8px #ffffff;
  transform: translateY(0);
}
</style>
