# Environment Configuration Example
# Copy this file to .env and fill in your actual values
# NEVER commit .env file to version control

# Development/Production Mode
NODE_ENV=development

# Application Settings
APP_NAME="برنامج بيانات المتهمين"
APP_VERSION=1.0.0
APP_DESCRIPTION="نظام متكامل لإدارة بيانات المتهمين"

# Database Settings
DB_ENCRYPTION_KEY=your-32-character-encryption-key-here
DB_BACKUP_ENCRYPTION=true
DB_MAX_CONNECTIONS=10

# Security Settings
SECURITY_SALT_ROUNDS=12
JWT_SECRET=your-jwt-secret-key-here
SESSION_SECRET=your-session-secret-here

# Code Signing - Windows
WINDOWS_CERTIFICATE_FILE=path/to/your/certificate.p12
WINDOWS_CERTIFICATE_PASSWORD=your-certificate-password
WINDOWS_PUBLISHER_NAME="محرم اليفرسي"

# Code Signing - macOS
APPLE_IDENTITY="Developer ID Application: Your Name (TEAM_ID)"
APPLE_TEAM_ID=YOUR_TEAM_ID
APPLE_ID=<EMAIL>
APPLE_ID_PASSWORD=your-app-specific-password

# Auto-updater Settings
GITHUB_OWNER=your-github-username
GITHUB_REPO=suspects-data-management
GITHUB_TOKEN=your-github-token

# Update Server (if using custom server)
UPDATE_SERVER_URL=https://your-update-server.com
UPDATE_CHECK_INTERVAL=********

# Logging Settings
LOG_LEVEL=info
LOG_FILE_PATH=logs/app.log
LOG_MAX_SIZE=********
LOG_MAX_FILES=5

# Network Settings
PROXY_URL=
PROXY_USERNAME=
PROXY_PASSWORD=

# Feature Flags
ENABLE_AUTO_BACKUP=true
ENABLE_CRASH_REPORTING=false
ENABLE_ANALYTICS=false
ENABLE_DEBUG_MODE=false

# Backup Settings
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key
BACKUP_CLOUD_PROVIDER=
BACKUP_CLOUD_CREDENTIALS=

# License Settings
LICENSE_SERVER_URL=
LICENSE_PUBLIC_KEY=

# Development Settings (only for development)
DEV_TOOLS_ENABLED=true
DEV_RELOAD_ENABLED=true
DEV_MOCK_DATA=false

# Build Settings
BUILD_NUMBER=1
BUILD_TIMESTAMP=
BUILD_COMMIT_HASH=

# Notification Settings
NOTIFICATION_SOUND_ENABLED=true
NOTIFICATION_DESKTOP_ENABLED=true

# Performance Settings
MEMORY_LIMIT=512
CPU_LIMIT=80
DISK_CACHE_SIZE=100

# Compliance Settings
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=2555
AUDIT_LOG_RETENTION_DAYS=2555

# Integration Settings
EXTERNAL_API_KEY=
EXTERNAL_API_URL=
EXTERNAL_API_TIMEOUT=30000

# Localization Settings
DEFAULT_LANGUAGE=ar
SUPPORTED_LANGUAGES=ar,en
RTL_SUPPORT=true

# UI Settings
THEME_PRIMARY_COLOR=#3b82f6
THEME_SECONDARY_COLOR=#64748b
THEME_BACKGROUND_COLOR=#f8fafc

# Advanced Security Settings
CONTENT_SECURITY_POLICY_ENABLED=true
HTTPS_ONLY=true
CERTIFICATE_PINNING=false
INTEGRITY_CHECK_ENABLED=true

# Error Reporting
ERROR_REPORTING_ENABLED=false
ERROR_REPORTING_URL=
ERROR_REPORTING_API_KEY=

# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED=false
PERFORMANCE_MONITORING_URL=
PERFORMANCE_MONITORING_API_KEY=

# Custom Settings
CUSTOM_LOGO_PATH=
CUSTOM_SPLASH_SCREEN=
CUSTOM_ABOUT_TEXT=

# Database Migration Settings
AUTO_MIGRATE_DATABASE=true
BACKUP_BEFORE_MIGRATION=true
MIGRATION_TIMEOUT=300000

# Import/Export Settings
MAX_IMPORT_FILE_SIZE=524288000
MAX_EXPORT_RECORDS=1000000
IMPORT_BATCH_SIZE=1000
EXPORT_BATCH_SIZE=5000

# Search Settings
SEARCH_INDEX_ENABLED=true
SEARCH_CACHE_SIZE=50
SEARCH_TIMEOUT=10000
FUZZY_SEARCH_THRESHOLD=0.7

# Activation System
ACTIVATION_REQUIRED=true
ACTIVATION_OFFLINE_MODE=true
ACTIVATION_GRACE_PERIOD=7

# System Requirements
MIN_RAM_MB=2048
MIN_DISK_SPACE_MB=1024
MIN_SCREEN_RESOLUTION=1024x768

# Telemetry (if enabled)
TELEMETRY_ENABLED=false
TELEMETRY_URL=
TELEMETRY_API_KEY=
TELEMETRY_INTERVAL=3600000

# Development Debugging
DEBUG_SQL_QUERIES=false
DEBUG_PERFORMANCE=false
DEBUG_MEMORY_USAGE=false
DEBUG_NETWORK_REQUESTS=false

# Testing Settings
TEST_MODE=false
TEST_DATA_ENABLED=false
TEST_SKIP_ACTIVATION=false

# Maintenance Mode
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="النظام قيد الصيانة، يرجى المحاولة لاحقاً"

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20

# File Upload Settings
MAX_ATTACHMENT_SIZE=********
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,txt
VIRUS_SCAN_ENABLED=false

# Backup Verification
BACKUP_INTEGRITY_CHECK=true
BACKUP_COMPRESSION_LEVEL=6
BACKUP_ENCRYPTION_ALGORITHM=AES-256-GCM

# User Session Settings
SESSION_TIMEOUT=3600000
REMEMBER_ME_DURATION=**********
MAX_CONCURRENT_SESSIONS=3

# Password Policy
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=true
PASSWORD_HISTORY_COUNT=5

# Account Lockout
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
LOCKOUT_RESET_TIME=3600000

# Data Validation
STRICT_DATA_VALIDATION=true
SANITIZE_INPUT=true
VALIDATE_FILE_TYPES=true

# Monitoring and Alerts
DISK_SPACE_WARNING_THRESHOLD=85
MEMORY_USAGE_WARNING_THRESHOLD=80
CPU_USAGE_WARNING_THRESHOLD=85
DATABASE_SIZE_WARNING_THRESHOLD=**********

# Cleanup Settings
AUTO_CLEANUP_ENABLED=true
CLEANUP_INTERVAL=********
TEMP_FILE_RETENTION=3600000
LOG_CLEANUP_DAYS=30

# Network Security
DISABLE_HTTP=true
TLS_MIN_VERSION=1.2
CERTIFICATE_VALIDATION=true
HOSTNAME_VERIFICATION=true

# Application Integrity
CODE_SIGNATURE_VERIFICATION=true
FILE_INTEGRITY_CHECK=true
RUNTIME_PROTECTION=true
ANTI_TAMPERING=true
