import { computed, ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import type { UserPermissions } from '@/types'
import { permissionsService } from '@/services/PermissionsService'
import { userService } from '@/services/UserService'

export function useAdvancedPermissions() {
  const authStore = useAuthStore()
  const userPermissions = ref<UserPermissions | null>(null)
  const isLoading = ref(false)

  // تحميل صلاحيات المستخدم الحالي
  async function loadCurrentUserPermissions() {
    if (!authStore.currentUser?.id) return

    try {
      isLoading.value = true
      userPermissions.value = await userService.getUserPermissions(authStore.currentUser.id)
      
      // إذا لم توجد صلاحيات محفوظة، إنشاء صلاحيات افتراضية
      if (!userPermissions.value && authStore.currentUser.role) {
        userPermissions.value = permissionsService.createDefaultPermissions(
          authStore.currentUser.id, 
          authStore.currentUser.role
        )
      }
    } catch (error) {
      console.error('Error loading user permissions:', error)
    } finally {
      isLoading.value = false
    }
  }

  // التحقق من صلاحية الوصول لقسم
  const canAccessSection = computed(() => (sectionId: string): boolean => {
    if (!userPermissions.value) return false
    if (authStore.currentUser?.role === 'admin') return true
    
    return permissionsService.canAccessSection(userPermissions.value, sectionId)
  })

  // التحقق من صلاحية الوصول لتبويب
  const canAccessTab = computed(() => (sectionId: string, tabId: string): boolean => {
    if (!userPermissions.value) return false
    if (authStore.currentUser?.role === 'admin') return true
    
    return permissionsService.canAccessTab(userPermissions.value, sectionId, tabId)
  })

  // التحقق من صلاحية تنفيذ إجراء
  const canPerformAction = computed(() => (sectionId: string, tabId: string, actionId: string): boolean => {
    if (!userPermissions.value) return false
    if (authStore.currentUser?.role === 'admin') return true
    
    return permissionsService.canPerformAction(userPermissions.value, sectionId, tabId, actionId)
  })

  // صلاحيات خاصة بأقسام محددة
  const suspectsPermissions = computed(() => ({
    canView: canAccessSection.value('suspects'),
    canAdd: canAccessTab.value('suspects', 'add-suspect'),
    canEdit: canPerformAction.value('suspects', 'review-status', 'edit-suspect-btn'),
    canDelete: canPerformAction.value('suspects', 'review-status', 'delete-suspect-btn'),
    canExport: canPerformAction.value('suspects', 'review-status', 'export-suspect-btn'),
    canViewCharts: canAccessTab.value('suspects', 'summary-charts'),
    canViewCards: canAccessTab.value('suspects', 'card-view')
  }))

  const databasePermissions = computed(() => ({
    canView: canAccessSection.value('database'),
    canImport: canPerformAction.value('database', 'database-tabs', 'import-data'),
    canExport: canPerformAction.value('database', 'database-tabs', 'export-data'),
    canEditCells: canPerformAction.value('database', 'database-tabs', 'edit-cells'),
    canDeleteTabs: canPerformAction.value('database', 'database-tabs', 'delete-tabs'),
    canSearch: canPerformAction.value('database', 'database-tabs', 'search-matching'),
    canFormat: canPerformAction.value('database', 'database-tabs', 'format-table'),
    canHighlight: canPerformAction.value('database', 'database-tabs', 'highlight-content')
  }))

  const settingsPermissions = computed(() => ({
    canView: canAccessSection.value('settings'),
    canManageTheme: canAccessTab.value('settings', 'theme-customization'),
    canManageFields: canAccessTab.value('settings', 'fields-configuration'),
    canManageBackup: canAccessTab.value('settings', 'backup-restore'),
    canManageUsers: canAccessTab.value('settings', 'user-management'),
    canManageDeveloper: canAccessTab.value('settings', 'developer-settings'),
    canAddUser: canPerformAction.value('settings', 'user-management', 'add-user'),
    canEditUser: canPerformAction.value('settings', 'user-management', 'edit-user'),
    canToggleUserStatus: canPerformAction.value('settings', 'user-management', 'toggle-user-status'),
    canResetPassword: canPerformAction.value('settings', 'user-management', 'reset-password'),
    canManagePermissions: canPerformAction.value('settings', 'user-management', 'manage-permissions')
  }))

  // دالة للحصول على الأقسام المسموحة
  const allowedSections = computed(() => {
    const sections = permissionsService.getAppStructure()
    return sections.filter(section => canAccessSection.value(section.id))
  })

  // دالة للحصول على التبويبات المسموحة لقسم معين
  const getAllowedTabs = computed(() => (sectionId: string) => {
    const section = permissionsService.getSection(sectionId)
    if (!section) return []
    
    return section.tabs.filter(tab => canAccessTab.value(sectionId, tab.id))
  })

  // دالة للحصول على الإجراءات المسموحة لتبويب معين
  const getAllowedActions = computed(() => (sectionId: string, tabId: string) => {
    const tab = permissionsService.getTab(sectionId, tabId)
    if (!tab) return []
    
    return tab.actions.filter(action => canPerformAction.value(sectionId, tabId, action.id))
  })

  // دالة لإظهار رسالة عدم وجود صلاحية
  function showPermissionError(action: string = 'تنفيذ هذا الإجراء') {
    alert(`عذراً، ليس لديك صلاحية ${action}`)
  }

  // دالة للتحقق من الصلاحية مع إظهار رسالة خطأ
  function requirePermission(sectionId: string, tabId?: string, actionId?: string, errorMessage?: string): boolean {
    let hasAccess = false
    
    if (actionId && tabId) {
      hasAccess = canPerformAction.value(sectionId, tabId, actionId)
    } else if (tabId) {
      hasAccess = canAccessTab.value(sectionId, tabId)
    } else {
      hasAccess = canAccessSection.value(sectionId)
    }
    
    if (!hasAccess) {
      showPermissionError(errorMessage || 'الوصول لهذه الميزة')
    }
    
    return hasAccess
  }

  // دالة لتحديث صلاحيات المستخدم
  async function updateUserPermissions(newPermissions: UserPermissions) {
    if (!authStore.currentUser?.id) return
    
    try {
      await userService.saveUserPermissions(authStore.currentUser.id, newPermissions)
      userPermissions.value = newPermissions
    } catch (error) {
      console.error('Error updating user permissions:', error)
      throw new Error('فشل في تحديث الصلاحيات')
    }
  }

  // دالة للتحقق من كون المستخدم مدير
  const isAdmin = computed(() => authStore.currentUser?.role === 'admin')

  // دالة للتحقق من كون المستخدم مشرف
  const isSupervisor = computed(() => authStore.currentUser?.role === 'supervisor')

  // دالة للتحقق من كون المستخدم ضابط
  const isOfficer = computed(() => authStore.currentUser?.role === 'officer')

  // دالة للتحقق من كون المستخدم محقق
  const isInvestigator = computed(() => authStore.currentUser?.role === 'investigator')

  // دالة للتحقق من كون المستخدم مراقب
  const isViewer = computed(() => authStore.currentUser?.role === 'viewer')

  // دالة للحصول على مستوى الصلاحية
  const permissionLevel = computed(() => {
    const role = authStore.currentUser?.role
    switch (role) {
      case 'admin': return 5
      case 'supervisor': return 4
      case 'officer': return 3
      case 'investigator': return 2
      case 'viewer': return 1
      default: return 0
    }
  })

  // دالة للتحقق من مستوى الصلاحية المطلوب
  const hasMinimumPermissionLevel = computed(() => (requiredLevel: number): boolean => {
    return permissionLevel.value >= requiredLevel
  })

  return {
    // State
    userPermissions,
    isLoading,
    
    // Computed permissions
    canAccessSection,
    canAccessTab,
    canPerformAction,
    suspectsPermissions,
    databasePermissions,
    settingsPermissions,
    allowedSections,
    getAllowedTabs,
    getAllowedActions,
    
    // Role checks
    isAdmin,
    isSupervisor,
    isOfficer,
    isInvestigator,
    isViewer,
    permissionLevel,
    hasMinimumPermissionLevel,
    
    // Methods
    loadCurrentUserPermissions,
    showPermissionError,
    requirePermission,
    updateUserPermissions
  }
}
