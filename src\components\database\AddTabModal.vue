<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="neumorphic-card bg-white max-w-md w-full">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-bold text-secondary-800">إضافة تبويب جديد</h3>
        <button @click="$emit('close')" class="text-secondary-400 hover:text-secondary-600">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <form @submit.prevent="handleSubmit" class="space-y-4">
        <!-- Tab Name -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-tag ml-1"></i>
            اسم التبويب
          </label>
          <input
            v-model="formData.name"
            type="text"
            required
            class="neumorphic-input w-full"
            placeholder="مثال: رسائل نصية، حركة اتصالات"
          />
        </div>

        <!-- Tab Icon -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-icons ml-1"></i>
            أيقونة التبويب
          </label>
          <select
            v-model="formData.icon"
            class="neumorphic-select w-full"
          >
            <option value="fas fa-sms">رسائل نصية (fas fa-sms)</option>
            <option value="fas fa-phone">مكالمات (fas fa-phone)</option>
            <option value="fas fa-database">قاعدة بيانات (fas fa-database)</option>
            <option value="fas fa-table">جدول (fas fa-table)</option>
            <option value="fas fa-file-alt">ملف (fas fa-file-alt)</option>
            <option value="fas fa-chart-line">إحصائيات (fas fa-chart-line)</option>
            <option value="fas fa-users">أشخاص (fas fa-users)</option>
            <option value="fas fa-map-marker-alt">مواقع (fas fa-map-marker-alt)</option>
            <option value="fas fa-calendar">تواريخ (fas fa-calendar)</option>
            <option value="fas fa-clock">أوقات (fas fa-clock)</option>
          </select>
        </div>

        <!-- Initial Columns -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-columns ml-1"></i>
            الأعمدة الأولية
          </label>
          
          <div class="space-y-3">
            <div
              v-for="(column, index) in formData.columns"
              :key="index"
              class="flex items-center gap-2 p-3 bg-secondary-50 rounded-neumorphic-sm"
            >
              <input
                v-model="column.name"
                type="text"
                placeholder="اسم العمود"
                class="flex-1 neumorphic-input text-sm"
              />
              <select
                v-model="column.type"
                class="neumorphic-select text-sm"
              >
                <option value="text">نص</option>
                <option value="number">رقم</option>
                <option value="date">تاريخ</option>
                <option value="time">وقت</option>
                <option value="datetime">تاريخ ووقت</option>
              </select>
              <button
                type="button"
                @click="removeColumn(index)"
                class="text-danger-500 hover:text-danger-700 p-1"
              >
                <i class="fas fa-trash text-xs"></i>
              </button>
            </div>
          </div>

          <button
            type="button"
            @click="addColumn"
            class="mt-3 neumorphic-button text-success-600 hover:text-success-700 text-sm"
          >
            <i class="fas fa-plus ml-1"></i>
            إضافة عمود
          </button>
        </div>

        <!-- Settings -->
        <div>
          <label class="block text-sm font-medium text-secondary-700 mb-2">
            <i class="fas fa-cog ml-1"></i>
            إعدادات التبويب
          </label>
          
          <div class="space-y-2">
            <label class="flex items-center gap-2">
              <input
                v-model="formData.settings.showCheckboxes"
                type="checkbox"
                class="neumorphic-checkbox"
              />
              <span class="text-sm">إظهار مربعات الاختيار</span>
            </label>
            
            <label class="flex items-center gap-2">
              <input
                v-model="formData.settings.showRowNumbers"
                type="checkbox"
                class="neumorphic-checkbox"
              />
              <span class="text-sm">إظهار أرقام الصفوف</span>
            </label>
            
            <label class="flex items-center gap-2">
              <input
                v-model="formData.settings.alternateRowColors"
                type="checkbox"
                class="neumorphic-checkbox"
              />
              <span class="text-sm">ألوان متناوبة للصفوف</span>
            </label>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-end gap-3 pt-4 border-t border-secondary-200">
          <button
            type="button"
            @click="$emit('close')"
            class="neumorphic-button text-secondary-600 hover:text-secondary-700"
          >
            إلغاء
          </button>
          <button
            type="submit"
            class="neumorphic-button text-success-600 hover:text-success-700"
          >
            <i class="fas fa-plus ml-2"></i>
            إضافة التبويب
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive } from 'vue'
import type { DatabaseTab, DatabaseColumn, TabSettings, GroupHeaderSettings } from '@/types/database'
import { generateId } from '@/utils/helpers'

// Emits
const emit = defineEmits<{
  close: []
  addTab: [tabData: Omit<DatabaseTab, 'id'>]
}>()

// Form data
const formData = reactive({
  name: '',
  icon: 'fas fa-table',
  columns: [
    { name: 'التاريخ', type: 'date' as const },
    { name: 'الوقت', type: 'time' as const },
    { name: 'المحتوى', type: 'text' as const }
  ],
  settings: {
    showCheckboxes: true,
    showRowNumbers: true,
    alternateRowColors: true,
    pageSize: 50,
    currentPage: 1,
    sortDirection: 'asc' as const,
    filters: {},
    searchQuery: '',
    searchColumns: [],
    exportSettings: {
      includeHeaders: true,
      includeGroupHeaders: true,
      includeHiddenColumns: false,
      preserveFormatting: true,
      paperSize: 'A4' as const,
      orientation: 'portrait' as const,
      margins: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20
      }
    }
  }
})

// Methods
function addColumn() {
  formData.columns.push({
    name: '',
    type: 'text'
  })
}

function removeColumn(index: number) {
  if (formData.columns.length > 1) {
    formData.columns.splice(index, 1)
  }
}

function handleSubmit() {
  if (!formData.name.trim()) {
    alert('يرجى إدخال اسم التبويب')
    return
  }

  if (formData.columns.some(col => !col.name.trim())) {
    alert('يرجى إدخال أسماء جميع الأعمدة')
    return
  }

  // Create columns with proper formatting
  const columns: DatabaseColumn[] = formData.columns.map((col, index) => ({
    id: generateId(),
    name: col.name,
    type: col.type,
    width: 150,
    isVisible: true,
    isResizable: true,
    order: index + 1,
    formatting: {
      // Header formatting
      headerTextColor: '#334155',
      headerBackgroundColor: '#f1f5f9',
      headerFontSize: 14,
      headerFontWeight: 'bold',
      headerTextAlign: 'center',
      
      // Cell formatting
      cellTextColor: '#475569',
      cellBackgroundColor: 'transparent',
      cellFontSize: 14,
      cellFontWeight: 'normal',
      cellTextAlign: 'right',
      cellTextWrap: false,
      
      // Border formatting
      borderColor: '#e2e8f0',
      borderWidth: 1,
      borderStyle: 'solid',
      
      // Number formatting
      numberFormat: {
        decimals: 0,
        thousandsSeparator: false
      },
      
      // Date formatting
      dateFormat: 'YYYY-MM-DD'
    }
  }))

  // Create group header settings
  const groupHeaders: GroupHeaderSettings = {
    enabled: true,
    identifier: 'سجل حركة',
    textColor: '#1e40af',
    backgroundColor: '#dbeafe',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'right',
    colSpan: true
  }

  const tabData: Omit<DatabaseTab, 'id'> = {
    name: formData.name,
    icon: formData.icon,
    columns,
    rows: [],
    settings: formData.settings as TabSettings,
    groupHeaders,
    createdAt: new Date(),
    updatedAt: new Date()
  }

  emit('addTab', tabData)
}
</script>
