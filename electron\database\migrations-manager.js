const fs = require('fs').promises
const path = require('path')

class MigrationsManager {
  constructor(sqliteManager) {
    this.db = sqliteManager
    this.migrationsDir = path.join(__dirname, 'migrations')
    this.currentVersion = '1.0.0'
  }

  async initialize() {
    try {
      // Ensure migrations directory exists
      await fs.mkdir(this.migrationsDir, { recursive: true })
      
      // Create migrations table if it doesn't exist
      await this.createMigrationsTable()
      
      console.log('✅ Migrations manager initialized')
    } catch (error) {
      console.error('❌ Failed to initialize migrations manager:', error)
      throw error
    }
  }

  async createMigrationsTable() {
    const sql = `
      CREATE TABLE IF NOT EXISTS migrations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        version TEXT UNIQUE NOT NULL,
        name TEXT NOT NULL,
        executed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        execution_time INTEGER, -- in milliseconds
        checksum TEXT,
        success BOOLEAN DEFAULT 1
      )
    `
    
    this.db.db.exec(sql)
    console.log('✅ Migrations table created')
  }

  async runMigrations() {
    try {
      console.log('🔄 Starting database migrations...')
      
      // Get all migration files
      const migrationFiles = await this.getMigrationFiles()
      
      // Get executed migrations
      const executedMigrations = await this.getExecutedMigrations()
      
      // Filter pending migrations
      const pendingMigrations = migrationFiles.filter(file => 
        !executedMigrations.some(executed => executed.name === file.name)
      )

      if (pendingMigrations.length === 0) {
        console.log('✅ No pending migrations')
        return { success: true, migrationsRun: 0 }
      }

      console.log(`📋 Found ${pendingMigrations.length} pending migrations`)

      let successCount = 0
      const results = []

      // Run each pending migration
      for (const migration of pendingMigrations) {
        try {
          console.log(`🔄 Running migration: ${migration.name}`)
          
          const startTime = Date.now()
          const result = await this.runSingleMigration(migration)
          const executionTime = Date.now() - startTime

          if (result.success) {
            await this.recordMigration(migration, executionTime, result.checksum)
            successCount++
            console.log(`✅ Migration completed: ${migration.name} (${executionTime}ms)`)
          } else {
            console.error(`❌ Migration failed: ${migration.name}`, result.error)
            await this.recordFailedMigration(migration, result.error)
          }

          results.push({
            name: migration.name,
            success: result.success,
            executionTime,
            error: result.error
          })

        } catch (error) {
          console.error(`❌ Migration error: ${migration.name}`, error)
          await this.recordFailedMigration(migration, error.message)
          
          results.push({
            name: migration.name,
            success: false,
            error: error.message
          })
        }
      }

      console.log(`✅ Migrations completed: ${successCount}/${pendingMigrations.length} successful`)

      return {
        success: successCount === pendingMigrations.length,
        migrationsRun: successCount,
        totalMigrations: pendingMigrations.length,
        results
      }

    } catch (error) {
      console.error('❌ Migrations failed:', error)
      throw error
    }
  }

  async getMigrationFiles() {
    try {
      const files = await fs.readdir(this.migrationsDir)
      const migrationFiles = []

      for (const file of files) {
        if (file.endsWith('.js')) {
          const filePath = path.join(this.migrationsDir, file)
          const stats = await fs.stat(filePath)
          
          migrationFiles.push({
            name: file,
            path: filePath,
            version: this.extractVersionFromFilename(file),
            createdAt: stats.birthtime
          })
        }
      }

      // Sort by version/name
      migrationFiles.sort((a, b) => a.name.localeCompare(b.name))

      return migrationFiles
    } catch (error) {
      console.error('Error reading migration files:', error)
      return []
    }
  }

  async getExecutedMigrations() {
    try {
      const sql = 'SELECT * FROM migrations ORDER BY executed_at'
      const stmt = this.db.db.prepare(sql)
      return stmt.all()
    } catch (error) {
      console.error('Error getting executed migrations:', error)
      return []
    }
  }

  async runSingleMigration(migration) {
    try {
      // Load migration module
      const migrationModule = require(migration.path)
      
      // Calculate checksum
      const content = await fs.readFile(migration.path, 'utf8')
      const checksum = this.calculateChecksum(content)

      // Run migration in transaction
      const transaction = this.db.db.transaction(() => {
        if (typeof migrationModule.up === 'function') {
          migrationModule.up(this.db.db)
        } else {
          throw new Error('Migration must export an "up" function')
        }
      })

      transaction()

      return {
        success: true,
        checksum
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      }
    }
  }

  async recordMigration(migration, executionTime, checksum) {
    const sql = `
      INSERT INTO migrations (version, name, execution_time, checksum, success)
      VALUES (?, ?, ?, ?, 1)
    `
    
    const stmt = this.db.db.prepare(sql)
    stmt.run(migration.version, migration.name, executionTime, checksum)
  }

  async recordFailedMigration(migration, error) {
    const sql = `
      INSERT INTO migrations (version, name, success, checksum)
      VALUES (?, ?, 0, ?)
    `
    
    const stmt = this.db.db.prepare(sql)
    stmt.run(migration.version, migration.name, `ERROR: ${error}`)
  }

  async rollbackMigration(migrationName) {
    try {
      console.log(`🔄 Rolling back migration: ${migrationName}`)

      // Find migration file
      const migrationFiles = await this.getMigrationFiles()
      const migration = migrationFiles.find(f => f.name === migrationName)

      if (!migration) {
        throw new Error(`Migration not found: ${migrationName}`)
      }

      // Load migration module
      const migrationModule = require(migration.path)

      if (typeof migrationModule.down !== 'function') {
        throw new Error('Migration must export a "down" function for rollback')
      }

      // Run rollback in transaction
      const transaction = this.db.db.transaction(() => {
        migrationModule.down(this.db.db)
      })

      transaction()

      // Remove from migrations table
      const sql = 'DELETE FROM migrations WHERE name = ?'
      const stmt = this.db.db.prepare(sql)
      stmt.run(migrationName)

      console.log(`✅ Migration rolled back: ${migrationName}`)

      return { success: true }
    } catch (error) {
      console.error(`❌ Rollback failed: ${migrationName}`, error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  async createMigration(name, description = '') {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
      const filename = `${timestamp}_${name.replace(/\s+/g, '_').toLowerCase()}.js`
      const filePath = path.join(this.migrationsDir, filename)

      const template = `// Migration: ${name}
// Description: ${description}
// Created: ${new Date().toISOString()}

module.exports = {
  // Run the migration
  up: (db) => {
    // Add your migration code here
    // Example:
    // db.exec(\`
    //   ALTER TABLE suspects ADD COLUMN new_field TEXT;
    //   CREATE INDEX idx_suspects_new_field ON suspects(new_field);
    // \`)
    
    console.log('Migration ${name} applied')
  },

  // Rollback the migration
  down: (db) => {
    // Add your rollback code here
    // Example:
    // db.exec(\`
    //   DROP INDEX IF EXISTS idx_suspects_new_field;
    //   ALTER TABLE suspects DROP COLUMN new_field;
    // \`)
    
    console.log('Migration ${name} rolled back')
  }
}
`

      await fs.writeFile(filePath, template)
      console.log(`✅ Migration created: ${filename}`)

      return {
        success: true,
        filename,
        path: filePath
      }
    } catch (error) {
      console.error('Failed to create migration:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  async getMigrationStatus() {
    try {
      const migrationFiles = await this.getMigrationFiles()
      const executedMigrations = await this.getExecutedMigrations()

      const status = migrationFiles.map(file => {
        const executed = executedMigrations.find(e => e.name === file.name)
        return {
          name: file.name,
          version: file.version,
          executed: !!executed,
          executedAt: executed?.executed_at || null,
          executionTime: executed?.execution_time || null,
          success: executed?.success || null
        }
      })

      return {
        total: migrationFiles.length,
        executed: executedMigrations.filter(m => m.success).length,
        pending: migrationFiles.length - executedMigrations.filter(m => m.success).length,
        failed: executedMigrations.filter(m => !m.success).length,
        migrations: status
      }
    } catch (error) {
      console.error('Error getting migration status:', error)
      return {
        total: 0,
        executed: 0,
        pending: 0,
        failed: 0,
        migrations: []
      }
    }
  }

  extractVersionFromFilename(filename) {
    // Extract version from filename like "2024-01-01_add_new_field.js"
    const match = filename.match(/^(\d{4}-\d{2}-\d{2})/)
    return match ? match[1] : '1.0.0'
  }

  calculateChecksum(content) {
    // Simple checksum calculation
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36)
  }

  async validateMigrations() {
    try {
      const executedMigrations = await this.getExecutedMigrations()
      const migrationFiles = await this.getMigrationFiles()
      const issues = []

      // Check for missing migration files
      for (const executed of executedMigrations) {
        const file = migrationFiles.find(f => f.name === executed.name)
        if (!file) {
          issues.push({
            type: 'missing_file',
            migration: executed.name,
            message: 'Migration file is missing but was recorded as executed'
          })
        }
      }

      // Check for checksum mismatches
      for (const executed of executedMigrations) {
        if (executed.success && executed.checksum && !executed.checksum.startsWith('ERROR:')) {
          const file = migrationFiles.find(f => f.name === executed.name)
          if (file) {
            const content = await fs.readFile(file.path, 'utf8')
            const currentChecksum = this.calculateChecksum(content)
            if (currentChecksum !== executed.checksum) {
              issues.push({
                type: 'checksum_mismatch',
                migration: executed.name,
                message: 'Migration file has been modified after execution'
              })
            }
          }
        }
      }

      return {
        valid: issues.length === 0,
        issues
      }
    } catch (error) {
      console.error('Error validating migrations:', error)
      return {
        valid: false,
        issues: [{
          type: 'validation_error',
          message: error.message
        }]
      }
    }
  }
}

module.exports = MigrationsManager
