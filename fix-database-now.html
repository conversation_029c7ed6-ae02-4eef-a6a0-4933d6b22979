<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>🔧 إصلاح قاعدة البيانات الآن</title>
  <style>
    body {
      font-family: 'Cairo', sans-serif;
      direction: rtl;
      text-align: center;
      margin: 20px;
      background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
      min-height: 100vh;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .container {
      max-width: 600px;
      background: rgba(255, 255, 255, 0.1);
      padding: 40px;
      border-radius: 20px;
      backdrop-filter: blur(10px);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .title {
      font-size: 42px;
      font-weight: bold;
      margin-bottom: 30px;
      text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.8; }
    }

    .fix-button {
      background: linear-gradient(135deg, #10b981, #059669);
      color: white;
      padding: 25px 50px;
      border: none;
      border-radius: 30px;
      font-weight: bold;
      font-size: 20px;
      cursor: pointer;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4);
      transition: all 0.3s;
      margin: 20px;
    }

    .fix-button:hover {
      transform: scale(1.1) translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.6);
    }

    .log {
      background: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      font-family: monospace;
      text-align: right;
      max-height: 300px;
      overflow-y: auto;
      display: none;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .success {
      background: linear-gradient(135deg, #10b981, #059669);
      border-radius: 15px;
      padding: 30px;
      margin: 25px 0;
      font-size: 18px;
      display: none;
      box-shadow: 0 10px 20px rgba(16, 185, 129, 0.4);
    }

    .credentials {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 10px;
      padding: 20px;
      margin: 20px 0;
      font-family: monospace;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1 class="title">🔧 إصلاح فوري</h1>
    
    <div style="background: rgba(255, 255, 255, 0.2); border-radius: 15px; padding: 25px; margin: 25px 0;">
      <h3 style="color: #fbbf24; margin-bottom: 15px;">⚡ إصلاح سريع ومضمون</h3>
      <p style="font-size: 16px; line-height: 1.6;">
        سيتم حذف قاعدة البيانات القديمة وإنشاء قاعدة جديدة مع جميع المستخدمين الافتراضيين.
      </p>
    </div>

    <button class="fix-button" onclick="fixDatabaseNow()">
      ⚡ إصلاح قاعدة البيانات الآن
    </button>

    <div class="log" id="log"></div>

    <div class="success" id="success">
      <h3>🎉 تم الإصلاح بنجاح!</h3>
      <p>قاعدة البيانات جاهزة والمستخدمين تم إنشاؤهم.</p>
      
      <div class="credentials">
        <h4 style="color: #3b82f6; margin-bottom: 15px;">🔑 بيانات تسجيل الدخول:</h4>
        <div style="text-align: right;">
          <p><strong>👑 المدير العام:</strong> admin / admin123</p>
          <p><strong>🔍 محقق رئيسي:</strong> investigator / inv123</p>
          <p><strong>👁️ مراقب:</strong> viewer / view123</p>
        </div>
      </div>
      
      <a href="http://localhost:5175" style="
        background: linear-gradient(135deg, #3b82f6, #2563eb);
        color: white;
        padding: 15px 30px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: bold;
        font-size: 18px;
        display: inline-block;
        margin-top: 20px;
        box-shadow: 0 6px 12px rgba(59, 130, 246, 0.4);
      ">🚀 العودة للتطبيق</a>
    </div>
  </div>

  <script>
    function log(message) {
      const logDiv = document.getElementById('log');
      logDiv.style.display = 'block';
      logDiv.innerHTML += message + '<br>';
      logDiv.scrollTop = logDiv.scrollHeight;
      console.log(message);
    }

    async function fixDatabaseNow() {
      document.querySelector('.fix-button').style.display = 'none';

      try {
        log('🔄 بدء الإصلاح الفوري...');

        // حذف قاعدة البيانات القديمة
        await new Promise((resolve) => {
          const deleteRequest = indexedDB.deleteDatabase('SuspectsDatabase');
          deleteRequest.onsuccess = () => {
            log('✅ تم حذف قاعدة البيانات القديمة');
            resolve();
          };
          deleteRequest.onerror = () => {
            log('⚠️ تجاهل خطأ الحذف - المتابعة');
            resolve();
          };
          deleteRequest.onblocked = () => {
            log('⚠️ قاعدة البيانات محجوبة - المتابعة');
            setTimeout(resolve, 1000);
          };
        });

        // إنشاء قاعدة البيانات الجديدة
        const db = await new Promise((resolve, reject) => {
          log('🔄 إنشاء قاعدة البيانات الجديدة...');
          const dbRequest = indexedDB.open('SuspectsDatabase', 3);
          
          dbRequest.onupgradeneeded = function(event) {
            const db = event.target.result;
            log('🔄 إنشاء الجداول...');
            
            // جدول المستخدمين
            const userStore = db.createObjectStore('users', { keyPath: 'id', autoIncrement: true });
            userStore.createIndex('username', 'username', { unique: true });
            userStore.createIndex('email', 'email', { unique: true });
            log('✅ جدول المستخدمين');
            
            // باقي الجداول
            const tables = ['suspects', 'fields', 'attachments', 'settings', 'reports', 'notifications', 'auditLog', 'backupRecords'];
            tables.forEach(name => {
              db.createObjectStore(name, { keyPath: 'id', autoIncrement: true });
              log(`✅ جدول ${name}`);
            });
          };
          
          dbRequest.onsuccess = (event) => resolve(event.target.result);
          dbRequest.onerror = (event) => reject(event.target.error);
        });

        // إضافة المستخدمين
        log('🔄 إضافة المستخدمين الافتراضيين...');
        
        const transaction = db.transaction(['users'], 'readwrite');
        const userStore = transaction.objectStore('users');

        const users = [
          { username: 'admin', name: 'المدير العام', email: '<EMAIL>', role: { id: 'admin', name: 'admin', displayName: 'مدير عام', description: 'صلاحيات كاملة', permissions: [] }, isActive: true, createdAt: new Date(), updatedAt: new Date() },
          { username: 'investigator', name: 'محقق رئيسي', email: '<EMAIL>', role: { id: 'investigator', name: 'investigator', displayName: 'محقق رئيسي', description: 'صلاحيات التحقيق', permissions: [] }, isActive: true, createdAt: new Date(), updatedAt: new Date() },
          { username: 'viewer', name: 'مراقب', email: '<EMAIL>', role: { id: 'viewer', name: 'viewer', displayName: 'مراقب', description: 'صلاحيات القراءة فقط', permissions: [] }, isActive: true, createdAt: new Date(), updatedAt: new Date() }
        ];

        const passwords = { 'admin': 'admin123', 'investigator': 'inv123', 'viewer': 'view123' };

        for (const user of users) {
          const userId = await new Promise((resolve, reject) => {
            const addRequest = userStore.add(user);
            addRequest.onsuccess = () => resolve(addRequest.result);
            addRequest.onerror = () => reject(addRequest.error);
          });

          log(`✅ مستخدم: ${user.username} (ID: ${userId})`);

          // حفظ كلمة المرور
          const password = passwords[user.username];
          const salt = 'suspects_app_salt';
          let hash = btoa(password + salt);
          for (let i = 0; i < 3; i++) hash = btoa(hash + salt);
          
          localStorage.setItem(`user_password_${userId}`, hash);
          log(`🔑 كلمة مرور: ${user.username}`);
        }

        db.close();
        log('🎉 تم الإصلاح بنجاح!');
        
        document.getElementById('success').style.display = 'block';

      } catch (error) {
        log('❌ خطأ: ' + error.message);
        console.error(error);
        document.querySelector('.fix-button').style.display = 'block';
      }
    }

    // تشغيل تلقائي بعد 3 ثوان
    setTimeout(() => {
      if (confirm('هل تريد بدء الإصلاح التلقائي؟')) {
        fixDatabaseNow();
      }
    }, 3000);

    console.log('🔧 صفحة الإصلاح الفوري جاهزة');
  </script>
</body>
</html>
